{"name": "netstream_graphql", "version": "2.0.0", "description": "NetStream - High Performance Streaming Platform", "main": "server-fastify.js", "scripts": {"start": "node server-fastify.js", "start:legacy": "node server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "free-ports": "node scripts/free_ports.js", "safe-start": "node scripts/free_ports.js && node server-fastify.js", "dev": "nodemon server-fastify.js", "benchmark": "node scripts/benchmark.js", "validate": "node scripts/validate-migration.js", "migrate": "node scripts/migrate-to-fastify.js", "health": "curl http://localhost:3001/health", "metrics": "curl http://localhost:3001/metrics"}, "keywords": ["streaming", "graphql", "fastify", "performance"], "author": "NetStream Team", "license": "ISC", "dependencies": {"@fastify/compress": "^7.0.3", "@fastify/cors": "^9.0.1", "@fastify/rate-limit": "^9.1.0", "@fastify/redis": "^6.1.1", "@fastify/static": "^7.0.4", "@fastify/websocket": "^10.0.1", "@fastify/request-context": "^5.1.0", "@google/generative-ai": "^0.6.0", "axios": "^1.8.4", "axios-cookiejar-support": "^6.0.0", "bullmq": "^5.4.2", "canvas": "^3.1.0", "cheerio": "^1.0.0", "dataloader": "^2.2.2", "dotenv": "^16.5.0", "fastify": "^4.26.2", "fastify-metrics": "^11.0.0", "graphql": "^16.8.1", "graphql-request": "^7.1.2", "ioredis": "^5.3.2", "jsdom": "^26.1.0", "mercurius": "^13.3.3", "mongodb": "^6.3.0", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "node-telegram-bot-api": "^0.64.0", "p-limit": "^2.3.0", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "puppeteer": "^24.8.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "string-similarity": "^4.0.4", "tough-cookie": "^5.1.2", "ws": "^8.18.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.3", "supertest": "^6.3.4", "@types/jest": "^29.5.12"}}