#!/bin/bash

# NetStream Docker Deployment with Custom IP
# Usage: ./deploy-with-ip.sh [IP_ADDRESS]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get IP address from argument or detect automatically
if [ -n "$1" ]; then
    VM_IP="$1"
    print_status "Using provided IP: $VM_IP"
else
    # Try to detect VM IP automatically
    VM_IP=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' || echo "")
    if [ -z "$VM_IP" ]; then
        print_error "Could not detect IP address automatically"
        echo "Usage: $0 [IP_ADDRESS]"
        echo "Example: $0 *************"
        exit 1
    fi
    print_status "Auto-detected IP: $VM_IP"
fi

# Validate IP format
if ! [[ $VM_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
    print_error "Invalid IP address format: $VM_IP"
    exit 1
fi

print_status "Deploying NetStream with IP: $VM_IP"

# Create temporary docker-compose file with the correct IP
TEMP_COMPOSE="docker-compose.temp.yml"
cp docker-compose.no-healthcheck.yml "$TEMP_COMPOSE"

# Update the build args and environment variables in the temporary file
sed -i "s|NEXT_PUBLIC_API_URL=http://[^:]*:[0-9]*/graphql|NEXT_PUBLIC_API_URL=http://$VM_IP:3001/graphql|g" "$TEMP_COMPOSE"
sed -i "s|NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://[^:]*:[0-9]*/graphql|NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://$VM_IP:3001/graphql|g" "$TEMP_COMPOSE"
sed -i "s|NEXT_PUBLIC_API_BASE_URL=http://[^:]*:[0-9]*|NEXT_PUBLIC_API_BASE_URL=http://$VM_IP:3001|g" "$TEMP_COMPOSE"

# Update CORS configuration for backend
sed -i "s|CORS_ORIGIN=http://[^/]*|CORS_ORIGIN=http://$VM_IP:3000|g" "$TEMP_COMPOSE"
sed -i "s|FRONTEND_URL=http://[^/]*|FRONTEND_URL=http://$VM_IP:3000|g" "$TEMP_COMPOSE"

# Ensure JWT_SECRET and ADMIN_KEY are properly set from environment
if [ -f .env ]; then
    source .env
    print_status "Loaded environment variables from .env file"
fi

print_status "Updated docker-compose configuration for IP: $VM_IP"

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.no-healthcheck.yml down 2>/dev/null || true

# Remove frontend image to force rebuild
print_status "Removing old frontend image..."
docker rmi netstream_graphql_frontend 2>/dev/null || true

# Build with new IP
print_status "Building frontend with IP: $VM_IP..."
docker-compose -f "$TEMP_COMPOSE" build --no-cache frontend

# Start all services
print_status "Starting all services..."
docker-compose -f "$TEMP_COMPOSE" up -d

# Clean up temporary file
rm "$TEMP_COMPOSE"

# Wait for services to start
print_status "Waiting for services to start..."
sleep 10

# Check status
print_status "Checking service status..."
docker-compose -f docker-compose.no-healthcheck.yml ps

print_success "Deployment completed!"
echo ""
print_status "Access your NetStream application:"
echo "  Frontend: http://$VM_IP:3000"
echo "  GraphQL API: http://$VM_IP:3001/graphql"
echo "  Health Check: http://$VM_IP:3001/health"
echo ""
print_status "Testing connectivity..."

# Test backend
if curl -f "http://$VM_IP:3001/health" >/dev/null 2>&1; then
    print_success "Backend is responding"
else
    print_warning "Backend health check failed"
fi

# Test frontend
if curl -f "http://$VM_IP:3000" >/dev/null 2>&1; then
    print_success "Frontend is responding"
else
    print_warning "Frontend health check failed"
fi

print_success "NetStream deployment with IP $VM_IP completed!"
