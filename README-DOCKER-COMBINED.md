# 🎬 NetStream - Combined Docker Deployment

> **Branch**: `docker-combined-render`  
> **Purpose**: Single container deployment for Render.com

## 🚀 Quick Start

### Local Development
```bash
# Build the combined image
docker build -t netstream-combined .

# Run locally
docker run -p 3000:3000 \
  -e MONGO_URI="your-mongodb-uri" \
  netstream-combined:latest

# Access the app
open http://localhost:3000
```

### Deploy to Render.com
1. Push this branch to GitHub
2. Create new Web Service in Render
3. Select Docker environment
4. Set environment variables (see DEPLOYMENT-GUIDE.md)
5. Deploy! 🎉

## 📁 Key Files

- **`Dockerfile`** - Multi-stage build (frontend + backend)
- **`server-combined.js`** - Process manager for both services
- **`render-combined.yaml`** - Render.com configuration
- **`DEPLOYMENT-GUIDE.md`** - Complete deployment instructions

## 🏗️ Architecture

```
Single Container:
├── Frontend (Next.js) → Port 3000
├── Backend (Fastify) → Port 3001
└── Combined Server → Manages both processes
```

## ✅ Features

- ✅ **Single container** - Easy deployment
- ✅ **Multi-stage build** - Optimized image size
- ✅ **Process management** - Graceful startup/shutdown
- ✅ **Health checks** - Built-in monitoring
- ✅ **Environment ready** - All dependencies included
- ✅ **Render.com optimized** - Free tier compatible

## 🔧 Environment Variables

### Required
```env
MONGO_URI=mongodb+srv://user:<EMAIL>/NetStream
NODE_ENV=production
```

### Optional
```env
REDIS_URL=redis://user:pass@host:port
TMDB_API_KEY=your-tmdb-api-key
PORT=3000
FASTIFY_PORT=3001
```

## 📊 Status

- ✅ **Docker build** - Working
- ✅ **Local testing** - Successful
- ✅ **Frontend** - Next.js standalone ready
- ✅ **Backend** - Fastify GraphQL API ready
- ✅ **Database** - MongoDB connection tested
- ⏳ **Render deployment** - Ready for deployment

## 🎯 Next Steps

1. **Deploy to Render.com** using the deployment guide
2. **Configure custom domain** (optional)
3. **Set up monitoring** and alerts
4. **Add Redis caching** for better performance

## 📚 Documentation

- [DEPLOYMENT-GUIDE.md](./DEPLOYMENT-GUIDE.md) - Complete deployment instructions
- [Dockerfile](./Dockerfile) - Container configuration
- [server-combined.js](./server-combined.js) - Process manager

---

**Ready for production deployment!** 🚀
