#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update or clean any field for a specific document by ID in any collection
 *
 * Usage:
 *   node scripts/updateDbField.js <collection> <documentId> <fieldName> <newValue>
 *   node scripts/updateDbField.js --clean <collection> <documentId> <fieldName>
 *
 * Collections:
 *   - movie
 *   - series
 *   - anime
 *   - livetv
 *
 * Common Field Names by Collection:
 *
 * MOVIE COLLECTION FIELDS:
 *   - title: The movie title
 *   - cleanedTitle: Cleaned version of the title (for search)
 *   - detailUrl: URL to the movie details page
 *   - detailUrlPath: Path portion of the detailUrl
 *   - image: URL to the movie poster/image
 *   - thumbnail: URL to the movie thumbnail
 *   - "streamingUrls": Array of streaming URLs (use specific index to update)
 *   - "streamingUrls.0.url": First streaming URL
 *   - "streamingUrls.0.provider": Provider for first streaming URL
 *   - "streamingUrls.0.language": Language for first streaming URL
 *   - "streamingUrls.0.sourceStreamUrl": Source stream URL for first streaming URL
 *   - "streamingUrls.0.isActive": Active status for first streaming URL
 *   - "metadata.synopsis": Movie synopsis/description
 *   - "metadata.year": Release year
 *   - "metadata.genre": Movie genre(s)
 *   - "metadata.actors": Movie actors
 *   - "metadata.creator": Movie director/creator
 *   - "metadata.duration": Movie duration
 *   - "tmdb.title": Title from TMDB
 *   - "tmdb.overview": Overview from TMDB
 *   - "tmdb.id": TMDB ID
 *   - "tmdb.poster_path": Poster path from TMDB
 *   - "tmdb.vote_average": Vote average from TMDB
 *
 * SERIES COLLECTION FIELDS:
 *   - title: The series title
 *   - cleanedTitle: Cleaned version of the title (for search)
 *   - originalTitle: Original title if different
 *   - description: Series description
 *   - year: Release year
 *   - detailUrl: URL to the series details page
 *   - detailUrlPath: Path portion of the detailUrl
 *   - image: URL to the series poster/image
 *   - thumbnail: URL to the series thumbnail
 *   - season: Current season number
 *   - totalSeasons: Total number of seasons
 *   - status: Series status (e.g., "Returning Series", "Ended")
 *   - "episodes": Array of episodes (use specific index to update)
 *   - "episodes.0.episodeNumber": Episode number for first episode
 *   - "episodes.0.season": Season number for first episode
 *   - "episodes.0.streamingUrls": Array of streaming URLs for first episode
 *   - "episodes.0.streamingUrls.0.url": First streaming URL for first episode
 *   - "episodes.0.streamingUrls.0.provider": Provider for first streaming URL of first episode
 *   - "episodes.0.streamingUrls.0.language": Language for first streaming URL of first episode
 *   - "episodes.0.streamingUrls.0.sourceStreamUrl": Source stream URL for first streaming URL of first episode
 *   - "episodes.0.streamingUrls.0.isActive": Active status for first streaming URL of first episode
 *   - "metadata.synopsis": Series synopsis/description
 *   - "metadata.actors": Series actors
 *   - "metadata.creator": Series creator
 *   - "metadata.year": Release year
 *   - "metadata.genre": Series genre(s)
 *   - "tmdb.title": Title from TMDB
 *   - "tmdb.overview": Overview from TMDB
 *   - "tmdb.id": TMDB ID
 *   - "tmdb.poster_path": Poster path from TMDB
 *   - "tmdbSeasons.0.name": Name of first season from TMDB
 *   - "tmdbSeasons.0.overview": Overview of first season from TMDB
 *   - "tmdbSeasons.0.poster_path": Poster path of first season from TMDB
 *
 * ANIME COLLECTION FIELDS:
 *   - title: The anime title
 *   - cleanedTitle: Cleaned version of the title (for search)
 *   - detailUrl: URL to the anime details page
 *   - detailUrlPath: Path portion of the detailUrl
 *   - image: URL to the anime poster/image
 *   - season: Season number
 *   - animeLanguage: Language (VF, VOSTFR, unknown)
 *   - "episodes": Array of episodes (use specific index to update)
 *   - "episodes.0.episodeNumber": Episode number for first episode
 *   - "episodes.0.season": Season number for first episode
 *   - "episodes.0.streamingUrls": Array of streaming URLs for first episode
 *   - "episodes.0.streamingUrls.0.url": First streaming URL for first episode
 *   - "episodes.0.streamingUrls.0.provider": Provider for first streaming URL of first episode
 *   - "episodes.0.streamingUrls.0.language": Language for first streaming URL of first episode
 *   - "episodes.0.streamingUrls.0.sourceStreamUrl": Source stream URL for first streaming URL of first episode
 *   - "episodes.0.streamingUrls.0.isActive": Active status for first streaming URL of first episode
 *   - "metadata.synopsis": Anime synopsis/description
 *   - "metadata.year": Release year
 *   - "metadata.genre": Anime genre(s)
 *   - "jikan.title.default": Default title from Jikan API
 *   - "jikan.title.english": English title from Jikan API
 *   - "jikan.title.japanese": Japanese title from Jikan API
 *   - "jikan.mal_id": MyAnimeList ID
 *   - "jikan.score": Score from MyAnimeList
 *   - "jikan.episodes": Total number of episodes
 *   - "jikan.status": Status (e.g., "Finished Airing")
 *   - "jikan.aired.from": Air date start
 *   - "jikan.aired.to": Air date end
 *
 * LIVETV COLLECTION FIELDS:
 *   - title: The channel title
 *   - cleanedTitle: Cleaned version of the title (for search)
 *   - detailUrl: URL to the channel details page
 *   - detailUrlPath: Path portion of the detailUrl
 *   - image: URL to the channel logo/image
 *   - "streamingUrls": Array of streaming URLs (use specific index to update)
 *   - "streamingUrls.0.url": First streaming URL
 *   - "streamingUrls.0.provider": Provider for first streaming URL
 *   - "streamingUrls.0.language": Language for first streaming URL
 *   - "streamingUrls.0.sourceStreamUrl": Source stream URL for first streaming URL
 *   - "streamingUrls.0.isActive": Active status for first streaming URL
 *   - "tmdb.title": Title from TMDB
 *   - "tmdb.id": TMDB ID
 *
 * Examples:
 *
 * Movie examples:
 *   # Basic fields
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e cleanedTitle "blanche neige mirroir mirroir"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e title "Blanche Neige - Mirror Mirror"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "metadata.year" "2012"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "metadata.genre" "Fantasy, Adventure"
 *
 *   # Streaming URL examples
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "streamingUrls.0.url" "https://example.com/movie.mp4"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "streamingUrls.0.provider" "doodstream"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "streamingUrls.0.sourceStreamUrl" "https://example.com/source.mp4"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "streamingUrls.0.isActive" "true"
 *
 *   # TMDB fields
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "tmdb.title" "Mirror Mirror"
 *   node scripts/updateDbField.js movie 67efb2c6c5dcb0fc0905a19e "tmdb.id" "1234567"
 *
 * Series examples:
 *   # Basic fields
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 title "Game of Thrones"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 cleanedTitle "game of thrones"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 season "8"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 totalSeasons 8
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 status "Ended"
 *
 *   # Episode and streaming URL examples
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "episodes.0.episodeNumber" "1"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "episodes.0.season" "1"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "episodes.0.streamingUrls.0.url" "https://example.com/s01e01.mp4"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "episodes.0.streamingUrls.0.provider" "doodstream"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "episodes.0.streamingUrls.0.sourceStreamUrl" "https://example.com/source_s01e01.mp4"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "episodes.0.streamingUrls.0.isActive" "true"
 *
 *   # TMDB fields
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "tmdb.id" "1234567"
 *   node scripts/updateDbField.js series 5f8a12345678901234567890 "tmdbSeasons.0.name" "Season 1"
 *
 * Anime examples:
 *   # Basic fields
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 title "Attack on Titan"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 cleanedTitle "attack on titan"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 animeLanguage "VOSTFR"
 *
 *   # Episode and streaming URL examples
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "episodes.0.episodeNumber" "1"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "episodes.0.season" "1"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "episodes.0.streamingUrls.0.url" "https://example.com/ep01.mp4"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "episodes.0.streamingUrls.0.provider" "doodstream"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "episodes.0.streamingUrls.0.sourceStreamUrl" "https://example.com/source_ep01.mp4"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "episodes.0.streamingUrls.0.isActive" "true"
 *
 *   # Jikan fields
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "jikan.title.english" "Attack on Titan"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "jikan.mal_id" "16498"
 *   node scripts/updateDbField.js anime 5f8a12345678901234567890 "jikan.score" "8.53"
 *
 *   # Clean fields examples
 *   node scripts/updateDbField.js --clean anime 67de17856825d721f00686c6 jikanSeasons
 *   node scripts/updateDbField.js --clean anime 5f8a12345678901234567890 tmdbSeasons
 *   node scripts/updateDbField.js --clean series 5f8a12345678901234567890 episodes
 *
 * LiveTV examples:
 *   # Basic fields
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 title "France 24"
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 cleanedTitle "france 24"
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 image "https://example.com/france24.jpg"
 *
 *   # Streaming URL examples
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 "streamingUrls.0.url" "https://example.com/france24.m3u8"
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 "streamingUrls.0.provider" "direct"
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 "streamingUrls.0.sourceStreamUrl" "https://example.com/source_france24.m3u8"
 *   node scripts/updateDbField.js livetv 5f8a12345678901234567890 "streamingUrls.0.isActive" "true"
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const { ObjectId } = mongoose.Types;

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Import the models
const Movie = require('../src/db/models/Movie');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const LiveTV = require('../src/db/models/LiveTV');
const logger = require('../src/utils/logger');

// Get MongoDB connection URI from environment variables
const MONGO_URI = process.env.MONGO_URI ||
                  process.env.MONGODB_URI ||
                  process.env.DB_URI ||
                  process.env.DATABASE_URI ||
                  'mongodb://localhost:27017/netstream';

// Process command line arguments
const args = process.argv.slice(2);
let isCleanMode = false;

// Check if the first argument is --clean
if (args[0] === '--clean') {
  isCleanMode = true;
  args.shift(); // Remove the --clean flag from args
}

// Check if we have enough arguments
if ((isCleanMode && args.length < 3) || (!isCleanMode && args.length < 4)) {
  console.error('Usage:');
  console.error('  node scripts/updateDbField.js <collection> <documentId> <fieldName> <newValue>');
  console.error('  node scripts/updateDbField.js --clean <collection> <documentId> <fieldName>');
  console.error('Collections: movie, series, anime, livetv');
  process.exit(1);
}

const collection = args[0].toLowerCase();
const documentId = args[1];
const fieldName = args[2];
const newValue = isCleanMode ? null : args[3];

// Get the appropriate model based on collection name
function getModel(collectionName) {
  switch (collectionName) {
    case 'movie':
      return Movie;
    case 'series':
      return Series;
    case 'anime':
      return Anime;
    case 'livetv':
      return LiveTV;
    default:
      throw new Error(`Unknown collection: ${collectionName}. Valid options are: movie, series, anime, livetv`);
  }
}

// Validate documentId format
if (!ObjectId.isValid(documentId)) {
  console.error('Error: Invalid document ID format. Must be a valid MongoDB ObjectId.');
  process.exit(1);
}

async function main() {
  try {
    logger.info('Connecting to MongoDB...');
    await mongoose.connect(MONGO_URI);
    logger.info('Connected to MongoDB');

    const Model = getModel(collection);

    // Find the document by ID
    const document = await Model.findById(documentId);

    if (!document) {
      logger.error(`Document with ID ${documentId} not found in ${collection} collection.`);
      process.exit(1);
    }

    // Display the current document information
    logger.info('Current document information:');
    logger.info(`ID: ${document._id}`);
    logger.info(`Title: ${document.title}`);

    // Get current field value (handle nested fields)
    let currentValue = '(not set)';
    if (fieldName.includes('.')) {
      const parts = fieldName.split('.');
      let current = document;
      let found = true;

      for (let i = 0; i < parts.length; i++) {
        if (current && current[parts[i]] !== undefined) {
          current = current[parts[i]];
        } else {
          found = false;
          break;
        }
      }

      if (found) {
        currentValue = current;
      }
    } else {
      currentValue = document[fieldName] !== undefined ? document[fieldName] : '(not set)';
    }

    logger.info(`${fieldName}: ${currentValue}`);

    // Update or clean the specified field
    if (isCleanMode) {
      // Clean mode - remove the field or set it to an empty array/object
      if (fieldName.includes('.')) {
        const parts = fieldName.split('.');
        let current = document;
        let found = true;

        // Navigate to the parent object
        for (let i = 0; i < parts.length - 1; i++) {
          if (current && current[parts[i]] !== undefined) {
            current = current[parts[i]];
          } else {
            found = false;
            break;
          }
        }

        if (found) {
          const lastPart = parts[parts.length - 1];
          // Check if the field is an array
          if (Array.isArray(current[lastPart])) {
            current[lastPart] = []; // Set to empty array
            logger.info(`Field '${fieldName}' cleaned (set to empty array).`);
          } else if (typeof current[lastPart] === 'object' && current[lastPart] !== null) {
            current[lastPart] = {}; // Set to empty object
            logger.info(`Field '${fieldName}' cleaned (set to empty object).`);
          } else {
            // For primitive types, unset the field
            current[lastPart] = undefined;
            logger.info(`Field '${fieldName}' cleaned (unset).`);
          }
        } else {
          logger.warn(`Field '${fieldName}' not found, nothing to clean.`);
        }
      } else {
        // Direct field cleaning
        if (Array.isArray(document[fieldName])) {
          document[fieldName] = []; // Set to empty array
          logger.info(`Field '${fieldName}' cleaned (set to empty array).`);
        } else if (typeof document[fieldName] === 'object' && document[fieldName] !== null) {
          document[fieldName] = {}; // Set to empty object
          logger.info(`Field '${fieldName}' cleaned (set to empty object).`);
        } else {
          // For primitive types, unset the field
          document[fieldName] = undefined;
          logger.info(`Field '${fieldName}' cleaned (unset).`);
        }
      }
    } else {
      // Update mode - set the field to the new value
      // Handle nested fields (e.g., metadata.synopsis)
      if (fieldName.includes('.')) {
        const parts = fieldName.split('.');
        let current = document;

        // Navigate to the nested object
        for (let i = 0; i < parts.length - 1; i++) {
          // Create the object if it doesn't exist
          if (!current[parts[i]]) {
            current[parts[i]] = {};
          }
          current = current[parts[i]];
        }

        // Set the value on the deepest property
        current[parts[parts.length - 1]] = newValue;
      } else {
        // Direct field assignment
        document[fieldName] = newValue;
      }
    }

    document.updatedAt = new Date();

    // Save the changes
    await document.save();

    if (isCleanMode) {
      logger.info('Document cleaned successfully:');
    } else {
      logger.info('Document updated successfully:');
    }

    logger.info(`ID: ${document._id}`);
    logger.info(`Title: ${document.title}`);

    // Get updated field value (handle nested fields)
    let updatedValue;
    if (fieldName.includes('.')) {
      const parts = fieldName.split('.');
      let current = document;

      for (let i = 0; i < parts.length; i++) {
        if (current && current[parts[i]] !== undefined) {
          current = current[parts[i]];
        } else {
          current = undefined;
          break;
        }
      }

      updatedValue = current;
    } else {
      updatedValue = document[fieldName];
    }

    if (updatedValue === undefined) {
      logger.info(`${fieldName}: (field unset)`);
    } else if (Array.isArray(updatedValue) && updatedValue.length === 0) {
      logger.info(`${fieldName}: (empty array)`);
    } else if (typeof updatedValue === 'object' && updatedValue !== null && Object.keys(updatedValue).length === 0) {
      logger.info(`${fieldName}: (empty object)`);
    } else {
      logger.info(`${fieldName}: ${updatedValue}`);
    }

    logger.info(`Updated at: ${document.updatedAt}`);

  } catch (error) {
    logger.error(`Error: ${error.message}`);
    process.exit(1);
  } finally {
    // Close the database connection
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the main function
main();
