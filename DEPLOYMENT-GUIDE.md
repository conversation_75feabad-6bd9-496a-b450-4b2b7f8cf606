# 🚀 NetStream Combined Docker Deployment Guide

## 📋 Overview

This guide covers deploying NetStream as a **single combined Docker container** to Render.com, containing both the Next.js frontend and Fastify backend.

## 🎯 Architecture

```
┌─────────────────────────────────────┐
│        Combined Container          │
│  ┌─────────────┐ ┌─────────────┐   │
│  │   Next.js   │ │   Fastify   │   │
│  │  Frontend   │ │   Backend   │   │
│  │   :3000     │ │    :3001    │   │
│  └─────────────┘ └─────────────┘   │
│         │              │           │
│         └──────────────┘           │
│       server-combined.js           │
└─────────────────────────────────────┘
```

## 🔧 Local Testing

### Prerequisites
- Docker installed
- MongoDB Atlas account (or local MongoDB)

### Build and Test
```bash
# Build the combined image
docker build -t netstream-combined .

# Test locally
docker run -p 3000:3000 \
  -e MONGO_URI="your-mongodb-connection-string" \
  netstream-combined:latest

# Access the application
# Frontend: http://localhost:3000
# GraphQL API: http://localhost:3001/graphql
```


## 🌐 Render.com Deployment

### Method 1: Using render.yaml (Recommended)

1. **Push to GitHub**
   ```bash
   git push origin docker-combined-render
   ```

2. **Create Render Service**
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New" → "Web Service"
   - Connect your GitHub repository
   - Select the `docker-combined-render` branch
   - Choose "Docker" as environment

3. **Configure Environment Variables**
   ```
   NODE_ENV=production
   MONGO_URI=mongodb+srv://user:<EMAIL>/NetStream
   PORT=3000
   FASTIFY_PORT=3001
   NEXT_TELEMETRY_DISABLED=1
   PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
   ```

4. **Optional: Add Redis**
   - Create a Redis service in Render
   - Add `REDIS_URL` environment variable

### Method 2: Manual Configuration

1. **Service Settings**
   - **Name**: `netstream-combined`
   - **Environment**: `Docker`
   - **Region**: `Oregon` (or preferred)
   - **Branch**: `docker-combined-render`
   - **Dockerfile Path**: `./Dockerfile`

2. **Build Settings**
   - **Build Command**: (leave empty)
   - **Start Command**: (leave empty - uses Dockerfile CMD)

## 🔐 Environment Variables

### Required
- `MONGO_URI`: MongoDB connection string
- `NODE_ENV`: Set to `production`

### Optional
- `REDIS_URL`: Redis connection string (for caching)
- `TMDB_API_KEY`: For movie/TV data updates
- `PORT`: Main port (default: 3000)
- `FASTIFY_PORT`: Backend port (default: 3001)

## 📊 Monitoring

### Health Checks
- **Frontend**: `GET /` (Next.js app)
- **Backend**: `GET /health` (Fastify health endpoint)
- **GraphQL**: `POST /graphql` (GraphQL endpoint)

### Logs
```bash
# View logs in Render dashboard or via CLI
render logs -s your-service-name
```

## 🐛 Troubleshooting

### Common Issues

1. **Build Timeout**
   - Render free tier has build time limits
   - Consider upgrading to paid plan for faster builds

2. **Memory Issues**
   - Free tier has 512MB RAM limit
   - Monitor memory usage in logs
   - Consider optimizing dependencies

3. **Port Conflicts**
   - Ensure PORT environment variable is set correctly
   - Render automatically assigns the PORT variable

4. **Database Connection**
   - Verify MONGO_URI is correct
   - Check MongoDB Atlas network access settings
   - Ensure database user has proper permissions

### Debug Commands
```bash
# Check container locally
docker run -it netstream-combined:latest /bin/bash

# View container structure
docker run --rm netstream-combined:latest ls -la /usr/src/app/

# Test specific components
docker run --rm netstream-combined:latest node --version
docker run --rm netstream-combined:latest chromium --version
```

## 🚀 Performance Optimization

### For Production
1. **Enable Redis caching**
2. **Configure CDN for static assets**
3. **Set up monitoring and alerts**
4. **Consider upgrading to paid Render plan**

### Scaling
- Render automatically handles scaling
- Consider horizontal scaling for high traffic
- Monitor performance metrics

## 📝 Next Steps

1. **Deploy to Render.com**
2. **Configure custom domain**
3. **Set up monitoring**
4. **Add CI/CD pipeline**
5. **Configure backup strategy**

## 🔗 Useful Links

- [Render Documentation](https://render.com/docs)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Fastify Production](https://www.fastify.io/docs/latest/Guides/Deployment/)
