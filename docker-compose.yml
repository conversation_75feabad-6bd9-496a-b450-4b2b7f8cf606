version: '3.8'

services:
  # Redis Cache Service (optional - you can disable this if not needed)
  redis:
    image: redis:7-alpine
    container_name: netstream-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 10s
    networks:
      - netstream-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend GraphQL API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: netstream-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - NODE_ENV=production
      - DOCKER=true
      - CORS_ORIGIN=http://*************:3000
      - FRONTEND_URL=http://*************:3000
      - ADMIN_KEY=${ADMIN_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./provider_url_output:/app/provider_url_output
      - backend_cache:/app/.cache
    networks:
      - netstream-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M

  # Frontend Next.js Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      args:
        - NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
        - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
        - NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
    container_name: netstream-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      - API_URL=http://backend:3001/graphql
      - NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
      - NODE_ENV=production
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - netstream-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 256M

volumes:
  redis_data:
    driver: local
  backend_cache:
    driver: local

networks:
  netstream-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
