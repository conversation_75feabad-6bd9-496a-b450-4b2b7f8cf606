services:
  # Backend Service
  - type: web
    name: netstream2
    env: docker
    dockerfilePath: ./Dockerfile.backend
    plan: free
    region: oregon
    branch: docker-combined-render
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGO_URI
        value: mongodb+srv://crypto:<EMAIL>/NetStream?retryWrites=true&w=majority
      - key: PORT
        value: 3001
      - key: TMDB_API_KEY
        value: 94b3e867d01d17b6de1f13d5775bf60a
      - key: GEMINI_API_KEY
        value: AIzaSyBiv05scT9egPJwPc1juOEEKlBBkaSBdjc
      - key: TELEGRAM_TOKEN
        value: **********************************************
      - key: PRIVATE_GROUP_ID
        value: -1002391462979
      - key: TELEGRAM_API_ID
        value: 26694562
      - key: TELEGRAM_API_HASH
        value: fb0d717c6e07c8e24b0045e6275e304e
      - key: WIFLIX_CHANNEL
        value: "@testflix2025"
      - key: ONEUPLOAD_API_KEY
        value: 8641z58roysnr5hwxoz9
      - key: ADMIN_KEY
        value: namery
      - key: RENDER
        value: true
      - key: ENABLE_CACHING
        value: false
    healthCheckPath: /api/system/health

  # Frontend Service
  - type: web
    name: netstream1
    env: docker
    dockerfilePath: ./Dockerfile.frontend
    plan: free
    region: oregon
    branch: docker-combined-render
    envVars:
      - key: NEXT_PUBLIC_API_BASE_URL
        value: https://netstream2.onrender.com


