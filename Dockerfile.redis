FROM redis:7-alpine

# Copy custom redis configuration if needed
# COPY redis.conf /usr/local/etc/redis/redis.conf

# Set default configuration
RUN echo "maxmemory 256mb" >> /etc/redis.conf && \
    echo "maxmemory-policy allkeys-lru" >> /etc/redis.conf && \
    echo "save 900 1" >> /etc/redis.conf && \
    echo "save 300 10" >> /etc/redis.conf && \
    echo "save 60 10000" >> /etc/redis.conf

# Expose Redis port
EXPOSE 6379

# Start Redis server
CMD ["redis-server", "/etc/redis.conf"]
