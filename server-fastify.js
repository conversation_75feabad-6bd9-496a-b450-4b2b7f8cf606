// File: server-fastify.js
// NetStream Fastify Server - High Performance Migration
// Phase 1: Core Framework Setup

require("dotenv").config();
const fastify = require('fastify')({
  logger: {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    transport: process.env.NODE_ENV !== 'production' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname'
      }
    } : undefined
  },
  trustProxy: true,
  bodyLimit: 1048576 * 10, // 10MB
  keepAliveTimeout: 30000,
  connectionTimeout: 30000,
  requestTimeout: 30000,
  disableRequestLogging: process.env.NODE_ENV === 'production'
});

const path = require('path');
const { MongoClient } = require('mongodb');
const fs = require('fs');

// Import configuration
const { mongoUri, port } = require('./src/config/env');

// Import services
const FastifyCache = require('./src/cache/fastifyCache');
const FastifyDbService = require('./src/db/fastifyDbService');
const FastifyDataLoaders = require('./src/graphql/dataLoaders');

// Global variables for database and services
let mongoClient;
let db;
let cacheService;
let dbService;
let dataLoaders;

// Graceful shutdown handler
const gracefulShutdown = async (signal) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    // Close Fastify server
    await fastify.close();
    
    // Close MongoDB connection
    if (mongoClient) {
      await mongoClient.close();
      fastify.log.info('MongoDB connection closed');
    }

    // Close cache service
    if (cacheService) {
      await cacheService.disconnect();
      fastify.log.info('Cache service disconnected');
    }
    
    fastify.log.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Database connection function
async function connectDatabase() {
  try {
    mongoClient = new MongoClient(mongoUri, {
      maxPoolSize: 50,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      retryReads: true
    });

    await mongoClient.connect();
    db = mongoClient.db();
    
    fastify.log.info('Connected to MongoDB successfully');

    // Test the connection
    await db.admin().ping();
    fastify.log.info('MongoDB ping successful');

    // Initialize services
    dbService = new FastifyDbService(db);
    dataLoaders = new FastifyDataLoaders(db);

    // Ensure database indexes for performance
    await dbService.ensureIndexes();
    fastify.log.info('Database indexes ensured');

    return { mongoClient, db };
  } catch (error) {
    fastify.log.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

// Initialize cache service
async function initializeCacheService() {
  try {
    cacheService = new FastifyCache();

    // Test cache connection
    const healthCheck = await cacheService.healthCheck();
    if (healthCheck.status === 'healthy') {
      fastify.log.info('Cache service initialized successfully');
    } else {
      fastify.log.warn('Cache service health check failed, continuing without cache');
    }

    return cacheService;
  } catch (error) {
    fastify.log.error('Failed to initialize cache service:', error);
    // Continue without cache in case of Redis connection issues
    return null;
  }
}

// Register essential plugins
async function registerPlugins() {
  // Initialize cache service first
  await initializeCacheService();

  // Redis plugin (if cache service is available)
  if (cacheService) {
    await fastify.register(require('@fastify/redis'), {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      family: 4,
      keepAlive: true,
      lazyConnect: true
    });
  }

  // CORS support
  await fastify.register(require('@fastify/cors'), {
    origin: (origin, callback) => {
      const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:3001',
        process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
        process.env.PRODUCTION_URL
      ].filter(Boolean);

      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      
      // Allow all origins in development
      if (process.env.NODE_ENV !== 'production') {
        return callback(null, true);
      }
      
      return callback(new Error('Not allowed by CORS'), false);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
  });

  // Compression
  await fastify.register(require('@fastify/compress'), {
    global: true,
    threshold: 1024,
    encodings: ['gzip', 'deflate']
  });

  // Static files
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'public'),
    prefix: '/',
    decorateReply: false
  });

  // Request context for tracking
  await fastify.register(require('@fastify/request-context'));

  // Rate limiting
  await fastify.register(require('@fastify/rate-limit'), {
    max: 1000, // requests
    timeWindow: '1 minute',
    skipOnError: true,
    keyGenerator: (request) => {
      return request.ip || 'anonymous';
    }
  });

  // WebSocket support for subscriptions
  await fastify.register(require('@fastify/websocket'));

  // Simple metrics endpoint (without fastify-metrics due to compatibility issues)
  fastify.get('/metrics', async (request, reply) => {
    reply.type('text/plain');
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();

    return `# HELP nodejs_memory_usage_bytes Memory usage in bytes
# TYPE nodejs_memory_usage_bytes gauge
nodejs_memory_usage_bytes{type="rss"} ${memUsage.rss}
nodejs_memory_usage_bytes{type="heapTotal"} ${memUsage.heapTotal}
nodejs_memory_usage_bytes{type="heapUsed"} ${memUsage.heapUsed}
nodejs_memory_usage_bytes{type="external"} ${memUsage.external}

# HELP nodejs_uptime_seconds Process uptime in seconds
# TYPE nodejs_uptime_seconds gauge
nodejs_uptime_seconds ${uptime}

# HELP netstream_server_info Server information
# TYPE netstream_server_info gauge
netstream_server_info{version="2.0.0",framework="fastify"} 1
`;
  });

  fastify.log.info('Simple metrics endpoint enabled');

  fastify.log.info('Essential plugins registered successfully');
}

// Register GraphQL
async function registerGraphQL() {
  try {
    fastify.log.info('Loading GraphQL schema and resolvers...');

    // Use a working test schema for now
    const testSchema = `
      type Query {
        hello: String
        health: String
        databaseStats: DatabaseStats
        config: Config
        availableGenres: AvailableGenres
        latestMovies(limit: Int): [Movie]
        latestSeries(limit: Int): [Series]
        latestAnime(limit: Int): [Anime]
        ancienMovies(limit: Int): [Movie]
        animeMovies(limit: Int): [Anime]
      }

      type DatabaseStats {
        movies: Int
        series: Int
        anime: Int
        livetv: Int
        totalItems: Int
      }

      type Config {
        tmdbApiKey: String
        wiflixBase: String
        frenchAnimeBase: String
        witvBase: String
      }

      type AvailableGenres {
        movies: [String]
        series: [String]
        anime: [String]
      }

      type Movie {
        id: ID!
        title: String!
        cleanedTitle: String
        thumbnail: String
        detailUrl: String
      }

      type Series {
        id: ID!
        title: String!
        cleanedTitle: String
        thumbnail: String
        detailUrl: String
      }

      type Anime {
        id: ID!
        title: String!
        cleanedTitle: String
        thumbnail: String
        detailUrl: String
      }

      type Mutation {
        adminLogin(token: String!): AdminLoginResponse
        deleteItem(id: ID!, type: String!): DeleteResponse
        scrapeItem(url: String!, type: String!): ScrapeResponse
        updateBaseUrl(wiflixBase: String, frenchAnimeBase: String, witvBase: String): UpdateResponse
        scrapeUrlManually(url: String!, type: String!): ScrapeResponse
        updateDisplaySettings(gridItemsEnabled: Boolean): UpdateResponse
      }

      type AdminLoginResponse {
        success: Boolean!
        message: String!
      }

      type DeleteResponse {
        success: Boolean!
        message: String!
      }

      type ScrapeResponse {
        success: Boolean!
        message: String!
      }

      type UpdateResponse {
        success: Boolean!
        message: String!
      }
    `;

    const simpleResolvers = {
      Query: {
        hello: () => 'Hello from Fastify GraphQL!',
        health: () => 'OK',
        databaseStats: async (parent, args, context) => {
          try {
            const { db } = context;
            const [movieCount, seriesCount, animeCount, livetvCount] = await Promise.all([
              db.collection('movies').countDocuments(),
              db.collection('series').countDocuments(),
              db.collection('animes').countDocuments(),
              db.collection('livetv').countDocuments()
            ]);

            return {
              movies: movieCount,
              series: seriesCount,
              anime: animeCount,
              livetv: livetvCount,
              totalItems: movieCount + seriesCount + animeCount + livetvCount
            };
          } catch (error) {
            return {
              movies: 0,
              series: 0,
              anime: 0,
              livetv: 0,
              totalItems: 0
            };
          }
        },
        config: async () => ({
          tmdbApiKey: null,
          wiflixBase: 'wiflix-max.cam',
          frenchAnimeBase: 'french-anime.com',
          witvBase: 'witv.skin'
        }),
        availableGenres: async () => ({
          movies: [],
          series: [],
          anime: []
        }),
        latestMovies: async () => [],
        latestSeries: async () => [],
        latestAnime: async () => [],
        ancienMovies: async () => [],
        animeMovies: async () => []
      },
      Mutation: {
        adminLogin: async () => ({ success: false, message: 'Not implemented' }),
        deleteItem: async () => ({ success: false, message: 'Not implemented' }),
        scrapeItem: async () => ({ success: false, message: 'Not implemented' }),
        updateBaseUrl: async () => ({ success: false, message: 'Not implemented' }),
        scrapeUrlManually: async () => ({ success: false, message: 'Not implemented' }),
        updateDisplaySettings: async () => ({ success: false, message: 'Not implemented' })
      }
    };

    fastify.log.info('Using working test schema...');

    fastify.log.info('Registering Mercurius...');

    // Start with basic configuration
    const mercuriusConfig = {
      schema: testSchema,
      resolvers: simpleResolvers,
      graphiql: process.env.NODE_ENV !== 'production',

      // Context function
      context: (request, reply) => {
        return {
          request,
          reply,
          db: request.db,
          mongoClient: request.mongoClient,
          dbService: request.dbService,
          dataLoaders: request.dataLoaders,
          cacheService: request.cacheService,
          user: request.user || null,
          isAdmin: request.isAdmin || false,
          requestId: request.id,
          startTime: request.startTime
        };
      }
    };

    await fastify.register(require('mercurius'), mercuriusConfig);

    fastify.log.info('GraphQL (Mercurius) registered successfully');

  } catch (error) {
    console.error('Failed to register GraphQL - Full Error:', error);
    fastify.log.error('Failed to register GraphQL:', error.message);
    fastify.log.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    });
    throw error;
  }
}

// Add services to request context
fastify.addHook('onRequest', async (request, reply) => {
  request.db = db;
  request.mongoClient = mongoClient;
  request.dbService = dbService;
  request.dataLoaders = dataLoaders;
  request.cacheService = cacheService;
  request.startTime = Date.now();
});

// Add response time header
fastify.addHook('onSend', async (request, reply, payload) => {
  const responseTime = Date.now() - request.startTime;
  reply.header('X-Response-Time', `${responseTime}ms`);
  
  // Log slow requests
  if (responseTime > 1000) {
    fastify.log.warn(`Slow request detected: ${request.method} ${request.url} - ${responseTime}ms`);
  }
  
  return payload;
});

// Health check endpoint
fastify.get('/health', async (request, reply) => {
  try {
    // Check database connection
    await db.admin().ping();

    // Check cache connection
    let cacheStatus = 'disabled';
    if (cacheService) {
      const cacheHealth = await cacheService.healthCheck();
      cacheStatus = cacheHealth.status;
    }

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: 'connected',
      cache: cacheStatus
    };

    return healthStatus;
  } catch (error) {
    reply.code(503);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: 'disconnected',
      cache: 'unknown'
    };
  }
});

// Cache statistics endpoint
fastify.get('/cache/stats', async (request, reply) => {
  if (!cacheService) {
    return {
      error: 'Cache service not available',
      stats: null
    };
  }

  return {
    stats: cacheService.getStats(),
    health: await cacheService.healthCheck()
  };
});

// Cache clear endpoint (admin only)
fastify.post('/cache/clear', async (request, reply) => {
  const { pattern, adminToken } = request.body || {};

  // Validate admin token (simple check for demo)
  if (!adminToken || adminToken !== process.env.ADMIN_KEY) {
    reply.code(401);
    return { error: 'Unauthorized' };
  }

  if (!cacheService) {
    return {
      error: 'Cache service not available',
      cleared: 0
    };
  }

  const cleared = await cacheService.clear(pattern);
  return {
    message: `Cleared ${cleared} cache entries`,
    cleared,
    pattern: pattern || 'netstream:*'
  };
});

// Basic API info endpoint
fastify.get('/api', async (request, reply) => {
  return {
    name: 'NetStream API',
    version: '2.0.0',
    framework: 'Fastify',
    graphql: '/graphql',
    health: '/health',
    timestamp: new Date().toISOString()
  };
});

// Error handler
fastify.setErrorHandler(async (error, request, reply) => {
  fastify.log.error({
    error: error.message,
    stack: error.stack,
    url: request.url,
    method: request.method,
    ip: request.ip
  }, 'Request error');

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production') {
    reply.code(error.statusCode || 500);
    return {
      error: 'Internal Server Error',
      statusCode: error.statusCode || 500,
      timestamp: new Date().toISOString()
    };
  }

  reply.code(error.statusCode || 500);
  return {
    error: error.message,
    statusCode: error.statusCode || 500,
    stack: error.stack,
    timestamp: new Date().toISOString()
  };
});

// 404 handler
fastify.setNotFoundHandler(async (request, reply) => {
  reply.code(404);
  return {
    error: 'Not Found',
    statusCode: 404,
    message: `Route ${request.method} ${request.url} not found`,
    timestamp: new Date().toISOString()
  };
});

// Server startup function
async function startServer() {
  try {
    fastify.log.info('Starting NetStream Fastify server...');
    
    // Connect to database
    await connectDatabase();
    
    // Register plugins
    await registerPlugins();

    // Register GraphQL
    await registerGraphQL();

    // Register API routes
    await fastify.register(require('./src/routes/fastifyRoutes'));
    
    // Start server
    const serverPort = process.env.PORT || port || 3001;
    const host = process.env.NODE_ENV === 'production' ? '0.0.0.0' : 'localhost';
    
    await fastify.listen({ 
      port: serverPort, 
      host: host 
    });
    
    fastify.log.info(`NetStream Fastify server listening on ${host}:${serverPort}`);
    fastify.log.info(`Health check available at: http://${host}:${serverPort}/health`);
    fastify.log.info(`API info available at: http://${host}:${serverPort}/api`);
    fastify.log.info(`GraphQL endpoint available at: http://${host}:${serverPort}/graphql`);
    fastify.log.info(`Performance metrics available at: http://${host}:${serverPort}/metrics`);
    fastify.log.info(`Cache stats available at: http://${host}:${serverPort}/cache/stats`);

    if (process.env.NODE_ENV !== 'production') {
      fastify.log.info(`GraphiQL available at: http://${host}:${serverPort}/graphiql`);
    }

    // Memory optimization for production
    if (process.env.NODE_ENV === 'production') {
      // Force garbage collection every 30 seconds
      setInterval(() => {
        if (global.gc) {
          global.gc();
        }
      }, 30000);

      // Monitor memory usage
      setInterval(() => {
        const memUsage = process.memoryUsage();
        const memUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);

        if (memUsedMB > 500) { // Alert if using more than 500MB
          fastify.log.warn(`High memory usage detected: ${memUsedMB}MB`);
        }
      }, 60000);
    }
    
  } catch (error) {
    fastify.log.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { fastify, startServer, connectDatabase };
