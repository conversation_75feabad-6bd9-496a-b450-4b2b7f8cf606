# NetStream GraphQL - Docker Deployment for Ubuntu/Proxmox

This guide provides instructions for deploying NetStream GraphQL application using Docker Compose on Ubuntu systems, specifically optimized for Proxmox environments.

## 🏗️ Architecture Overview

The application consists of the following services:

- **MongoDB** (mongo:7.0) - Database for storing movies, series, anime, and live TV data
- **Redis** (redis:7-alpine) - Caching and job queue management
- **Backend** (Node.js/Fastify) - GraphQL API server with web scraping capabilities
- **Frontend** (Next.js) - Web interface for browsing content

## 📋 Prerequisites

### System Requirements

- **OS**: Ubuntu 20.04 LTS or newer
- **RAM**: Minimum 2GB, recommended 4GB+
- **Storage**: Minimum 10GB free space
- **CPU**: 2+ cores recommended

### Software Requirements

- Docker Engine 20.10+
- Docker Compose 2.0+

## 🚀 Quick Start

### 1. Install Docker (if not already installed)

```bash
# Update system packages
sudo apt update

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add your user to docker group
sudo usermod -aG docker $USER

# Log out and back in, or run:
newgrp docker

# Verify installation
docker --version
docker compose version
```

### 2. Clone and Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd NetStream_graphql

# Switch to the docker branch
git checkout docker-combined-render

# Run the deployment script
./deploy-ubuntu.sh
```

### 3. Configure Environment Variables

The deployment script will create a `.env` file from the template. You **must** update the following values:

```bash
# Edit the .env file
nano .env

# Update these required values:
TMDB_API_KEY=your_actual_tmdb_api_key
GEMINI_API_KEY=your_actual_gemini_api_key
ADMIN_KEY=your_secure_random_admin_key
JWT_SECRET=your_secure_jwt_secret_key

# Optional (for Telegram features):
TELEGRAM_TOKEN=your_telegram_bot_token
PRIVATE_GROUP_ID=your_telegram_group_id
```

### 4. Start the Application

```bash
# Option 1: Deploy with automatic IP detection
./deploy-with-ip.sh

# Option 2: Deploy with specific IP address
./deploy-with-ip.sh *************

# Option 3: Use standard deployment (localhost only)
./deploy-ubuntu.sh start

# Option 4: Use docker-compose directly
docker-compose up -d
```

## 🔧 Management Commands

The `deploy-ubuntu.sh` script provides several management commands:

```bash
# Full deployment (recommended for first time)
./deploy-ubuntu.sh deploy

# Start services
./deploy-ubuntu.sh start

# Stop services
./deploy-ubuntu.sh stop

# Restart services
./deploy-ubuntu.sh restart

# Check service status
./deploy-ubuntu.sh status

# View logs
./deploy-ubuntu.sh logs

# Check service health
./deploy-ubuntu.sh health

# Cleanup (removes containers and volumes)
./deploy-ubuntu.sh cleanup
```

## 🌐 Access Points

Once deployed, you can access:

- **Frontend**: http://localhost:3000
- **GraphQL API**: http://localhost:3001/graphql
- **GraphQL Playground**: http://localhost:3001/graphql (in development mode)

### External Access

To access from other machines on your network:

1. Replace `localhost` with your server's IP address
2. Ensure firewall allows connections on ports 3000 and 3001
3. Update the `.env` file with your server's IP:

```bash
NEXT_PUBLIC_API_URL=http://YOUR_SERVER_IP:3001/graphql
NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://YOUR_SERVER_IP:3001/graphql
NEXT_PUBLIC_API_BASE_URL=http://YOUR_SERVER_IP:3001
```

## 📊 Monitoring and Logs

### View Service Status
```bash
docker-compose ps
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mongo
docker-compose logs -f redis
```

### Monitor Resource Usage
```bash
docker stats
```

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

| Variable | Description | Required |
|----------|-------------|----------|
| `MONGO_URI` | MongoDB connection string | Yes |
| `REDIS_URL` | Redis connection string | Yes |
| `TMDB_API_KEY` | The Movie Database API key | Yes |
| `GEMINI_API_KEY` | Google Gemini AI API key | Yes |
| `ADMIN_KEY` | Admin access key | Yes |
| `TELEGRAM_TOKEN` | Telegram bot token | Optional |
| `SCRAPE_ON_START` | Enable scraping on startup | Optional |

### Port Configuration

Default ports:
- Frontend: 3000
- Backend: 3001
- MongoDB: 27017
- Redis: 6379

To change ports, update the `docker-compose.yml` file and corresponding environment variables.

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 3001, 27017, and 6379 are not in use
2. **Memory issues**: Increase system memory or adjust container limits
3. **Permission errors**: Ensure Docker daemon is running and user has permissions

### Health Checks

The application includes health checks for all services:

```bash
# Check if all services are healthy
./deploy-ubuntu.sh health

# Manual health check
curl http://localhost:3001/graphql?query={__typename}
curl http://localhost:3000
```

### Reset Everything

If you need to start fresh:

```bash
# Stop and remove everything
./deploy-ubuntu.sh cleanup

# Remove all data (WARNING: This deletes all data!)
docker volume prune -f

# Redeploy
./deploy-ubuntu.sh deploy
```

## 🔒 Security Considerations

1. **Change default admin key**: Always set a secure `ADMIN_KEY`
2. **API keys**: Keep your API keys secure and never commit them to version control
3. **Network access**: Consider using a reverse proxy (nginx) for production
4. **Firewall**: Configure UFW or iptables to restrict access as needed

## 📈 Performance Optimization

### For Proxmox VMs

1. **CPU**: Allocate at least 2 CPU cores
2. **RAM**: 4GB+ recommended for optimal performance
3. **Storage**: Use SSD storage for better I/O performance
4. **Network**: Ensure good network connectivity for web scraping

### Docker Optimization

The configuration includes:
- Resource limits for containers
- Optimized caching strategies
- Health checks and restart policies
- Log rotation to prevent disk space issues

## 🆘 Support

If you encounter issues:

1. Check the logs: `./deploy-ubuntu.sh logs`
2. Verify service health: `./deploy-ubuntu.sh health`
3. Check system resources: `docker stats`
4. Review the troubleshooting section above

## 📝 Notes

- The application will automatically start scraping content on first run
- MongoDB and Redis data are persisted in Docker volumes
- The application is configured for production use with security best practices
- All services include health checks and will restart automatically if they fail
