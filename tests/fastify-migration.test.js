// File: tests/fastify-migration.test.js
// Comprehensive test suite for Fastify migration
// Tests all critical functionality and performance improvements

const { test, beforeAll, afterAll } = require('@jest/globals');
const { build } = require('./helper');

describe('NetStream Fastify Migration Tests', () => {
  let app;

  beforeAll(async () => {
    app = await build();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Server Health and Basic Functionality', () => {
    test('should start server successfully', async () => {
      expect(app).toBeDefined();
      expect(app.server.listening).toBe(true);
    });

    test('should respond to health check', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/health'
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.status).toBe('healthy');
      expect(payload.database).toBe('connected');
    });

    test('should serve API info', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api'
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.name).toBe('NetStream API');
      expect(payload.version).toBe('2.0.0');
      expect(payload.framework).toBe('Fastify');
    });

    test('should have performance metrics endpoint', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/metrics'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('text/plain');
    });
  });

  describe('GraphQL Functionality', () => {
    test('should handle GraphQL queries', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/graphql',
        payload: {
          query: '{ databaseStats { movies series anime livetv totalItems } }'
        }
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.data).toBeDefined();
      expect(payload.data.databaseStats).toBeDefined();
    });

    test('should handle GraphQL config query', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/graphql',
        payload: {
          query: '{ config { wiflixBase frenchAnimeBase witvBase } }'
        }
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.data.config).toBeDefined();
      expect(payload.data.config.wiflixBase).toBeDefined();
    });

    test('should handle GraphQL available genres query', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/graphql',
        payload: {
          query: '{ availableGenres { movies series anime } }'
        }
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.data.availableGenres).toBeDefined();
    });

    test('should handle GraphQL errors gracefully', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/graphql',
        payload: {
          query: '{ invalidQuery }'
        }
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.errors).toBeDefined();
      expect(payload.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Caching System', () => {
    test('should provide cache statistics', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/cache/stats'
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.stats || payload.error).toBeDefined();
    });

    test('should handle cache clear (without admin token)', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/cache/clear',
        payload: {}
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('API Routes', () => {
    test('should handle proxy image requests', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/proxy-image?url=https://httpbin.org/image/jpeg'
      });

      // Should either succeed or fail gracefully
      expect([200, 404, 500]).toContain(response.statusCode);
    });

    test('should validate proxy image URL parameter', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/proxy-image'
      });

      expect(response.statusCode).toBe(400);
    });

    test('should handle performance monitoring', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/performance'
      });

      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.timestamp).toBeDefined();
      expect(payload.uptime).toBeDefined();
      expect(payload.memory).toBeDefined();
    });
  });

  describe('Rate Limiting', () => {
    test('should apply rate limiting to proxy image requests', async () => {
      const requests = [];
      
      // Make multiple requests quickly
      for (let i = 0; i < 5; i++) {
        requests.push(app.inject({
          method: 'GET',
          url: '/proxy-image?url=https://httpbin.org/image/jpeg'
        }));
      }

      const responses = await Promise.all(requests);
      
      // All should succeed or fail gracefully (not rate limited with small number)
      responses.forEach(response => {
        expect([200, 400, 404, 500]).toContain(response.statusCode);
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 routes gracefully', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/non-existent-route'
      });

      expect(response.statusCode).toBe(404);
      const payload = JSON.parse(response.payload);
      expect(payload.error).toBe('Not Found');
      expect(payload.statusCode).toBe(404);
    });

    test('should handle invalid JSON in GraphQL', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/graphql',
        payload: 'invalid json'
      });

      expect([400, 500]).toContain(response.statusCode);
    });
  });

  describe('Security', () => {
    test('should have CORS headers', async () => {
      const response = await app.inject({
        method: 'OPTIONS',
        url: '/health'
      });

      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });

    test('should require admin token for admin routes', async () => {
      const response = await app.inject({
        method: 'DELETE',
        url: '/admin/item/507f1f77bcf86cd799439011?type=MOVIE'
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('Performance Benchmarks', () => {
    test('should respond to health check quickly', async () => {
      const start = Date.now();
      
      const response = await app.inject({
        method: 'GET',
        url: '/health'
      });

      const duration = Date.now() - start;
      
      expect(response.statusCode).toBe(200);
      expect(duration).toBeLessThan(100); // Should respond within 100ms
    });

    test('should handle concurrent GraphQL requests', async () => {
      const start = Date.now();
      const concurrentRequests = 10;
      
      const requests = Array(concurrentRequests).fill().map(() => 
        app.inject({
          method: 'POST',
          url: '/graphql',
          payload: {
            query: '{ databaseStats { totalItems } }'
          }
        })
      );

      const responses = await Promise.all(requests);
      const duration = Date.now() - start;
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.statusCode).toBe(200);
      });
      
      // Should handle 10 concurrent requests within 2 seconds
      expect(duration).toBeLessThan(2000);
    });

    test('should have response time headers', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/health'
      });

      expect(response.headers['x-response-time']).toBeDefined();
      expect(response.headers['x-response-time']).toMatch(/\d+ms/);
    });
  });

  describe('Memory and Resource Management', () => {
    test('should not leak memory on repeated requests', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Make many requests
      for (let i = 0; i < 100; i++) {
        await app.inject({
          method: 'GET',
          url: '/health'
        });
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
});

// Performance comparison test (if original server is available)
describe('Performance Comparison', () => {
  test('should be faster than Express baseline', async () => {
    // This would compare against the original Express server
    // For now, just ensure Fastify is performing well
    
    const start = Date.now();
    const iterations = 100;
    
    const requests = Array(iterations).fill().map(() => 
      app.inject({
        method: 'GET',
        url: '/health'
      })
    );

    await Promise.all(requests);
    const duration = Date.now() - start;
    const avgResponseTime = duration / iterations;
    
    // Average response time should be under 10ms for health checks
    expect(avgResponseTime).toBeLessThan(10);
  });
});
