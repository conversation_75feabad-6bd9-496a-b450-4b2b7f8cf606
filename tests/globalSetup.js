// File: tests/globalSetup.js
// Global setup for Jest tests

module.exports = async () => {
  console.log('Setting up test environment...');
  
  // Set global test environment variables
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error';
  
  // Disable console output during tests
  if (process.env.JEST_SILENT !== 'false') {
    global.console = {
      ...console,
      log: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    };
  }
  
  console.log('Test environment setup completed');
};
