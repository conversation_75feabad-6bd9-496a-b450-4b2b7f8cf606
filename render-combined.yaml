services:
  - type: web
    name: netstream-combined
    env: docker
    dockerfilePath: ./Dockerfile
    plan: free
    region: oregon
    branch: docker-combined-render
    buildCommand: ""
    startCommand: ""
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGO_URI
        fromDatabase:
          name: netstream-mongodb
          property: connectionString
      - key: REDIS_URL
        fromDatabase:
          name: netstream-redis
          property: connectionString
      - key: FASTIFY_PORT
        value: "3001"
      - key: PORT
        value: "3000"
      - key: NEXT_TELEMETRY_DISABLED
        value: "1"
      - key: PUPPETEER_EXECUTABLE_PATH
        value: "/usr/bin/chromium"
      - key: PUPPETEER_ARGS
        value: "--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-gpu,--no-zygote"

databases:
  - name: netstream-mongodb
    databaseName: NetStream
    user: netstream
    plan: free
    region: oregon
    
  - name: netstream-redis
    plan: free
    region: oregon
