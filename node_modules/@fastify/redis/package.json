{"name": "@fastify/redis", "version": "6.2.0", "description": "Plugin to share a common Redis connection across Fastify.", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "redis": "docker run -p 6379:6379 --rm redis", "valkey": "docker run -p 6379:6379 --rm valkey/valkey:7.2", "test": "npm run unit && npm run typescript", "typescript": "tsd", "unit": "tap", "unit:report": "tap --cov --coverage-report=html --coverage-report=cobertura", "unit:verbose": "tap -Rspec"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-redis.git"}, "keywords": ["fastify", "redis", "database", "speed", "cache", "i<PERSON>is"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-redis/issues"}, "homepage": "https://github.com/fastify/fastify-redis#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "fastify": "^4.0.0-rc.2", "proxyquire": "^2.1.3", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.31.0", "why-is-node-running": "^2.2.2"}, "dependencies": {"fastify-plugin": "^4.0.0", "ioredis": "^5.0.0"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}