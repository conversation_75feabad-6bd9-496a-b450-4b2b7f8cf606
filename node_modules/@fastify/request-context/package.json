{"name": "@fastify/request-context", "version": "5.1.0", "description": "Request-scoped storage support, based on Asynchronous Local Storage, with fallback to cls-hooked for older Node versions", "license": "MIT", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}], "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"test": "npm run test:unit && npm run test:typescript", "test:coverage": "tap -J test-tap/*.test.js --cov --coverage-report=lcovonly", "test:jest": "jest --config=jest.config.json", "test:tap": "tap", "test:unit": "npm run test:jest && npm run test:tap", "test:typescript": "tsd", "lint": "eslint \"test/**/*.js\" \"test-tap/**/*.js\" index.js", "prettier": "prettier --write \"{lib,test,test-tap}/**/*.js\" index.js \"types/**/*.ts\""}, "dependencies": {"fastify-plugin": "^4.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "eslint": "^8.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fastify": "^4.0.0-rc.2", "jest": "^29.0.1", "prettier": "^3.0.0", "superagent": "^8.0.0", "tap": "^16.0.0", "tsd": "^0.29.0"}, "homepage": "http://github.com/fastify/fastify-request-context", "repository": {"type": "git", "url": "git://github.com/fastify/fastify-request-context.git"}, "keywords": ["fastify", "plugin", "request", "context", "http-context", "request-context", "fastify-http-context", "fastify-request-context", "asynchronouslocalstorage", "asynchronous-local-storage"], "files": ["README.md", "LICENSE", "lib/*", "index.js", "types/index.d.ts"], "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}