{"name": "@fastify/rate-limit", "version": "9.1.0", "description": "A low overhead rate limiter for your routes", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "redis": "docker run -p 6379:6379 --name rate-limit-redis -d --rm redis", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tap", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-rate-limit.git"}, "keywords": ["fastify", "rate", "limit"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-rate-limit/issues"}, "homepage": "https://github.com/fastify/fastify-rate-limit#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@sinonjs/fake-timers": "^11.0.0", "@types/node": "^20.1.1", "fastify": "^4.7.0", "ioredis": "^5.0.5", "knex": "^3.0.1", "sqlite3": "^5.0.2", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.30.0"}, "dependencies": {"@lukeed/ms": "^2.0.1", "fastify-plugin": "^4.0.0", "toad-cache": "^3.3.1"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}