# NetStream GraphQL - Ubuntu/Proxmox Production Configuration - TEMPLATE ONLY
# This is just a template for new deployments
# You already have a working .env file - no need to use this template

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB - Using Docker service name for internal communication
MONGO_URI=mongodb://mongo:27017/NetStream

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis - Using Docker service name for internal communication
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379
REDIS_SERVICE_URL=

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Application ports - these will be exposed to host
PORT=3000
FASTIFY_PORT=3001
GRAPHQL_PORT=4000

# Production environment
NODE_ENV=production

# =============================================================================
# EXTERNAL API KEYS - CHANGE THESE VALUES
# =============================================================================

# TMDB API Key - Get from https://www.themoviedb.org/settings/api
TMDB_API_KEY=CHANGE_ME_your_tmdb_api_key

# Google Gemini AI API Key - Get from https://makersuite.google.com/app/apikey
GEMINI_API_KEY=CHANGE_ME_your_gemini_api_key

# OneUpload API Key
ONEUPLOAD_API_KEY=CHANGE_ME_your_oneupload_api_key

# =============================================================================
# TELEGRAM CONFIGURATION - CHANGE THESE VALUES
# =============================================================================

# Telegram Bot Token - Get from @BotFather
TELEGRAM_TOKEN=CHANGE_ME_your_telegram_bot_token

# Telegram Group/Channel IDs
PRIVATE_GROUP_ID=CHANGE_ME_your_private_group_id
TELEGRAM_API_ID=CHANGE_ME_your_telegram_api_id
TELEGRAM_API_HASH=CHANGE_ME_your_telegram_api_hash
WIFLIX_CHANNEL=CHANGE_ME_@your_channel

# =============================================================================
# SCRAPING CONFIGURATION
# =============================================================================

# Scraping behavior - optimized for production
SCRAPE_ON_START=true
SCRAPE_MODE=latest

# Scraping source base URLs
WIFLIX_BASE=wiflix-max.cam
FRENCH_ANIME_BASE=french-anime.com
WITV_BASE=witv.skin

# =============================================================================
# PERFORMANCE CONFIGURATION - OPTIMIZED FOR UBUNTU/PROXMOX
# =============================================================================

# Puppeteer settings - optimized for server environment
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
MAX_CONCURRENT_PAGES=1
MAX_CONCURRENT_PUPPETEER_TASKS=1
MAX_CONCURRENT_AXIOS_TASKS=3

# Retry settings
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_BASE=3000

# Rate limiting (requests per minute) - conservative for stability
GEMINI_RATE_LIMIT=25
TMDB_RATE_LIMIT=35
JIKAN_RATE_LIMIT=45

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
USE_ADVANCED_ENRICHMENT=true
FETCH_SEASONS=true
ENABLE_CACHING=true

# =============================================================================
# SECURITY CONFIGURATION - CHANGE THIS VALUE
# =============================================================================

# Admin access key - CHANGE THIS TO A SECURE RANDOM STRING
ADMIN_KEY=CHANGE_ME_generate_secure_random_string_here

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Docker deployment flag
DOCKER=true

# Disable proxy settings for local deployment
NO_PROXY=*

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Internal Docker communication
API_URL=http://backend:3001/graphql

# External access (change localhost to your server IP if needed)
NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# Next.js optimization
NEXT_TELEMETRY_DISABLED=1
SKIP_ENV_VALIDATION=1
NODE_OPTIONS=--max-old-space-size=2048
