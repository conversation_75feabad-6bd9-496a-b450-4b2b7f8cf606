#!/usr/bin/env node

// Combined Server for NetStream
// Serves both GraphQL API and Next.js frontend from a single process

const path = require('path');
const { spawn } = require('child_process');

// Environment configuration
const PORT = process.env.PORT || 3000;
const FASTIFY_PORT = process.env.FASTIFY_PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'production';

console.log('🚀 Starting NetStream Combined Server...');
console.log(`📊 Environment: ${NODE_ENV}`);
console.log(`🌐 Main Port: ${PORT}`);
console.log(`⚡ Backend Port: ${FASTIFY_PORT}`);

// Start backend server
console.log('🔧 Starting Fastify backend server...');
const backendProcess = spawn('node', ['server-fastify.js'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    FASTIFY_PORT: FASTIFY_PORT
  }
});

// Start frontend server
console.log('🎨 Starting Next.js frontend server...');
const frontendProcess = spawn('node', ['server.js'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    PORT: PORT,
    API_URL: `http://localhost:${FASTIFY_PORT}/graphql`,
    NEXT_PUBLIC_API_URL: `http://localhost:${FASTIFY_PORT}/graphql`,
    NEXT_PUBLIC_GRAPHQL_ENDPOINT: `http://localhost:${FASTIFY_PORT}/graphql`,
    NEXT_PUBLIC_API_BASE_URL: `http://localhost:${FASTIFY_PORT}`
  },
  cwd: path.join(__dirname, 'frontend')
});

// Handle process termination
process.on('SIGTERM', () => {
  console.log('📴 Received SIGTERM, shutting down gracefully...');
  backendProcess.kill('SIGTERM');
  frontendProcess.kill('SIGTERM');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('📴 Received SIGINT, shutting down gracefully...');
  backendProcess.kill('SIGINT');
  frontendProcess.kill('SIGINT');
  process.exit(0);
});

// Handle backend process events
backendProcess.on('error', (error) => {
  console.error('❌ Backend process error:', error);
  process.exit(1);
});

backendProcess.on('exit', (code, signal) => {
  console.log(`🔧 Backend process exited with code ${code} and signal ${signal}`);
  if (code !== 0) {
    console.error('❌ Backend process crashed, shutting down...');
    frontendProcess.kill();
    process.exit(1);
  }
});

// Handle frontend process events
frontendProcess.on('error', (error) => {
  console.error('❌ Frontend process error:', error);
  process.exit(1);
});

frontendProcess.on('exit', (code, signal) => {
  console.log(`🎨 Frontend process exited with code ${code} and signal ${signal}`);
  if (code !== 0) {
    console.error('❌ Frontend process crashed, shutting down...');
    backendProcess.kill();
    process.exit(1);
  }
});

console.log('✅ Combined server started successfully!');
console.log(`🌍 Access the application at: http://localhost:${PORT}`);
console.log(`🔗 GraphQL API available at: http://localhost:${FASTIFY_PORT}/graphql`);
