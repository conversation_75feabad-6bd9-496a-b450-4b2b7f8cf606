const cron = require('node-cron');
const { updateTrendingData } = require('./scrapeWorker');

console.log('Starting trending update cron job...');

cron.schedule('0 0 * * *', async () => {
  console.log('Starting updateTrendingData task (scheduled)...');
  try {
    await updateTrendingData();
    console.log('updateTrendingData task completed successfully (scheduled).');
  } catch (error) {
    console.error('Error during updateTrendingData task (scheduled):', error);
  }
});

console.log('Trending update cron job started.');