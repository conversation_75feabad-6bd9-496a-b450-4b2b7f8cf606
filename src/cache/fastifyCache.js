// File: src/cache/fastifyCache.js
// High-Performance Redis Caching Service for Fastify
// Replaces custom unified cache with Redis-based solution

const Redis = require('ioredis');

class FastifyCache {
  constructor(redisConfig = {}) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: process.env.REDIS_DB || 0,
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxLoadingTimeout: 1000,
      lazyConnect: true,
      keepAlive: true,
      family: 4,
      maxRetriesPerRequest: 3,
      ...redisConfig
    });

    this.defaultTTL = 300; // 5 minutes default
    this.keyPrefix = 'netstream:';
    this.compressionThreshold = 1024; // Compress values larger than 1KB
    
    this.setupEventHandlers();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }

  setupEventHandlers() {
    this.redis.on('connect', () => {
      console.log('Redis cache connected successfully');
    });

    this.redis.on('error', (error) => {
      console.error('Redis cache error:', error);
      this.stats.errors++;
    });

    this.redis.on('ready', () => {
      console.log('Redis cache ready for operations');
    });

    this.redis.on('close', () => {
      console.log('Redis cache connection closed');
    });
  }

  // Generate cache key with namespace
  generateKey(namespace, operation, params = {}) {
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    
    return `${this.keyPrefix}${namespace}:${operation}:${paramString}`;
  }

  // Compress large values
  compress(value) {
    if (typeof value === 'string' && value.length > this.compressionThreshold) {
      return {
        compressed: true,
        data: Buffer.from(value).toString('base64')
      };
    }
    return { compressed: false, data: value };
  }

  // Decompress values
  decompress(stored) {
    if (stored.compressed) {
      return Buffer.from(stored.data, 'base64').toString();
    }
    return stored.data;
  }

  // Get value from cache
  async get(key, options = {}) {
    try {
      const fullKey = key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`;
      const stored = await this.redis.get(fullKey);
      
      if (stored === null) {
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      const parsed = JSON.parse(stored);
      return this.decompress(parsed);
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.errors++;
      return null;
    }
  }

  // Set value in cache
  async set(key, value, ttl = this.defaultTTL, options = {}) {
    try {
      const fullKey = key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`;
      const compressed = this.compress(value);
      const serialized = JSON.stringify(compressed);
      
      if (ttl > 0) {
        await this.redis.setex(fullKey, ttl, serialized);
      } else {
        await this.redis.set(fullKey, serialized);
      }
      
      this.stats.sets++;
      
      // Add to tags if specified
      if (options.tags && Array.isArray(options.tags)) {
        await this.tagKey(fullKey, options.tags);
      }
      
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      this.stats.errors++;
      return false;
    }
  }

  // Delete from cache
  async del(key) {
    try {
      const fullKey = key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`;
      const result = await this.redis.del(fullKey);
      this.stats.deletes++;
      return result > 0;
    } catch (error) {
      console.error('Cache delete error:', error);
      this.stats.errors++;
      return false;
    }
  }

  // Check if key exists
  async exists(key) {
    try {
      const fullKey = key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`;
      return await this.redis.exists(fullKey) === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  // Set TTL for existing key
  async expire(key, ttl) {
    try {
      const fullKey = key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`;
      return await this.redis.expire(fullKey, ttl) === 1;
    } catch (error) {
      console.error('Cache expire error:', error);
      return false;
    }
  }

  // Get multiple keys
  async mget(keys) {
    try {
      const fullKeys = keys.map(key => 
        key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`
      );
      
      const values = await this.redis.mget(fullKeys);
      
      return values.map((value, index) => {
        if (value === null) {
          this.stats.misses++;
          return null;
        }
        
        this.stats.hits++;
        try {
          const parsed = JSON.parse(value);
          return this.decompress(parsed);
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('Cache mget error:', error);
      this.stats.errors++;
      return keys.map(() => null);
    }
  }

  // Set multiple keys
  async mset(keyValuePairs, ttl = this.defaultTTL) {
    try {
      const pipeline = this.redis.pipeline();
      
      for (const [key, value] of keyValuePairs) {
        const fullKey = key.startsWith(this.keyPrefix) ? key : `${this.keyPrefix}${key}`;
        const compressed = this.compress(value);
        const serialized = JSON.stringify(compressed);
        
        if (ttl > 0) {
          pipeline.setex(fullKey, ttl, serialized);
        } else {
          pipeline.set(fullKey, serialized);
        }
      }
      
      await pipeline.exec();
      this.stats.sets += keyValuePairs.length;
      return true;
    } catch (error) {
      console.error('Cache mset error:', error);
      this.stats.errors++;
      return false;
    }
  }

  // Tag-based cache invalidation
  async tagKey(key, tags) {
    try {
      const pipeline = this.redis.pipeline();
      
      for (const tag of tags) {
        const tagKey = `${this.keyPrefix}tag:${tag}`;
        pipeline.sadd(tagKey, key);
        pipeline.expire(tagKey, 86400); // Tags expire in 24 hours
      }
      
      await pipeline.exec();
    } catch (error) {
      console.error('Cache tag error:', error);
    }
  }

  // Invalidate by tags
  async invalidateByTags(tags) {
    try {
      const pipeline = this.redis.pipeline();
      const keysToDelete = new Set();
      
      for (const tag of tags) {
        const tagKey = `${this.keyPrefix}tag:${tag}`;
        const keys = await this.redis.smembers(tagKey);
        
        keys.forEach(key => keysToDelete.add(key));
        pipeline.del(tagKey);
      }
      
      if (keysToDelete.size > 0) {
        pipeline.del(...Array.from(keysToDelete));
      }
      
      await pipeline.exec();
      this.stats.deletes += keysToDelete.size;
      
      return keysToDelete.size;
    } catch (error) {
      console.error('Cache invalidate by tags error:', error);
      return 0;
    }
  }

  // Clear all cache
  async clear(pattern = `${this.keyPrefix}*`) {
    try {
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
        this.stats.deletes += keys.length;
      }
      
      return keys.length;
    } catch (error) {
      console.error('Cache clear error:', error);
      return 0;
    }
  }

  // Get cache statistics
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 ? 
      (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2) : 0;
    
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      connected: this.redis.status === 'ready'
    };
  }

  // Reset statistics
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }

  // Health check
  async healthCheck() {
    try {
      const testKey = `${this.keyPrefix}health:${Date.now()}`;
      await this.redis.set(testKey, 'ok', 'EX', 1);
      const result = await this.redis.get(testKey);
      await this.redis.del(testKey);
      
      return {
        status: result === 'ok' ? 'healthy' : 'unhealthy',
        connected: this.redis.status === 'ready',
        latency: await this.getLatency()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        connected: false,
        error: error.message
      };
    }
  }

  // Get Redis latency
  async getLatency() {
    try {
      const start = Date.now();
      await this.redis.ping();
      return Date.now() - start;
    } catch {
      return -1;
    }
  }

  // Graceful shutdown
  async disconnect() {
    try {
      await this.redis.quit();
      console.log('Redis cache disconnected gracefully');
    } catch (error) {
      console.error('Error disconnecting Redis cache:', error);
      this.redis.disconnect();
    }
  }
}

module.exports = FastifyCache;
