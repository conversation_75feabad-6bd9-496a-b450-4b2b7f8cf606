// src/scrapers/sites/wiflix/list.js - FINAL: Added Path Extraction
const cheerio = require('cheerio');
const pLimit = require('p-limit');
const { WIFLIX_BASE } = require('../../../config/constants');
const logger = require('../../../utils/logger');
const { fetchPageWithPuppeteer } = require('../../../utils/browserUtils');
const Config = require('../../../db/models/Config');

// Will be set dynamically in the scrapeWiflixList function
let WIFLIX_BASE_URL;

// Helper to check content
function containsRealContent(html) {
    if (!html || typeof html !== 'string') {
        logger.warn('[Wiflix List] HTML is null or not a string');
        return false;
    }

    // Log the first 200 characters of the HTML for debugging
    logger.debug(`[Wiflix List] HTML preview: ${html.substring(0, 200)}...`);

    // Check for movie items - more flexible patterns
    const moviePatterns = [
        '<div class="mov clearfix">',
        '<div class="mov">',
        '<article class="short">',
        '<div class="short">',
        '<div class="short-in">',
        '<div class="th-item">',
        '<div class="th-in">',
        '<div class="movie-item">',
        '<div class="film-item">',
        '<div class="item">',
        '<div class="poster">',
        '<div class="card">',
        '<div class="card-movie">'
    ];

    for (const pattern of moviePatterns) {
        if (html.includes(pattern)) {
            logger.info(`[Wiflix List] Found movie items in HTML with pattern: ${pattern}`);
            return true;
        }
    }

    // Check for content container - more flexible patterns
    const contentPatterns = [
        '<div id="dle-content">',
        '<div class="dle-content">',
        '<main id="main">',
        '<div id="content">',
        '<div class="content">',
        '<div class="movies-list">',
        '<div class="films-list">',
        '<div class="items">',
        '<div class="grid">',
        '<div class="container">'
    ];

    for (const pattern of contentPatterns) {
        if (html.includes(pattern)) {
            logger.info(`[Wiflix List] Found content container in HTML with pattern: ${pattern}`);
            return true;
        }
    }

    // Check for movie titles or links - more flexible patterns
    const titlePatterns = [
        'mov-t nowrap',
        'mov-mask ps-link',
        'movie-title',
        'film-title',
        'item-title',
        'title-link',
        'name-film',
        'name-movie',
        '<a href',
        '<a class="'
    ];

    for (const pattern of titlePatterns) {
        if (html.includes(pattern)) {
            logger.info(`[Wiflix List] Found movie titles in HTML with pattern: ${pattern}`);
            return true;
        }
    }

    // Debug HTML generation removed

    logger.warn('[Wiflix List] HTML does not contain any expected content markers');
    return false;
}

// Helper function to safely extract path from URL
function extractPathFromUrl(fullUrl, logContext) {
    if (!fullUrl || typeof fullUrl !== 'string' || !fullUrl.startsWith('http')) {
        return '';
    }
    try {
        const urlObject = new URL(fullUrl);
        return urlObject.pathname + urlObject.search + urlObject.hash;
    } catch (e) {
        logger.warn(`[${logContext}] Could not parse path from URL "${fullUrl}": ${e.message}`);
        return ''; // Return empty string on error
    }
}


// --- Pagination Detection Function ---
async function detectTotalWiflixPages(baseUrl) {
     try {
        // Get the latest WIFLIX_BASE from the database if baseUrl is not provided or doesn't contain a protocol
        if (!baseUrl || (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://'))) {
            const wiflixBase = await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);
            const endpoint = baseUrl ? baseUrl.replace(/^\/+|\/+$/g, '') : '';
            baseUrl = `https://${wiflixBase}${endpoint ? '/' + endpoint : ''}`;
            logger.info(`[Wiflix List] Using dynamic base URL for pagination detection: ${baseUrl}`);
        }

        logger.info(`[Wiflix List] Detecting total pages using Puppeteer for ${baseUrl}`);
        const html = await fetchPageWithPuppeteer(baseUrl, 60000, 0);

        if (!html) {
            logger.warn(`[Wiflix List] Puppeteer returned null HTML for pagination detection: ${baseUrl}`);
            return 1;
        }

        // Check if the HTML contains any content
        if (!containsRealContent(html)) {
            logger.warn(`[Wiflix List] Puppeteer did not return valid HTML for pagination detection: ${baseUrl}`);

            // Try to find pagination elements anyway
            if (html.includes('pagi-nav') || html.includes('navigation') || html.includes('pagination')) {
                logger.info(`[Wiflix List] Found pagination elements in HTML despite missing content markers`);
            } else {
                return 1;
            }
        }
        const $ = cheerio.load(html);
        // Robust pagination selector logic
        const lastPageLink = $('.pagi-nav .navigation a:last-child, .wp-pagenavi .last, .paginationjs-page:last-child a, ul.pagination li:nth-last-child(2) a').last().attr('href');
        let lastPageText = $('.pagi-nav .navigation a:last-child, .wp-pagenavi .last, .paginationjs-page:last-child a, ul.pagination li:nth-last-child(2) a').last().text().trim();

        if (lastPageLink) {
            const matchPage = lastPageLink.match(/page(?:=|[\/-])(\d+)/i);
            if (matchPage && matchPage[1]) {
                const total = parseInt(matchPage[1], 10);
                logger.info(`[Wiflix List] Detected ${total} total pages via link (Puppeteer)`);
                return total > 0 ? total : 1;
            }
        }
        if (lastPageText && /^\d+$/.test(lastPageText)) {
             const total = parseInt(lastPageText, 10);
             logger.info(`[Wiflix List] Detected ${total} total pages via text (Puppeteer)`);
             return total > 0 ? total : 1;
        }
        logger.warn(`[Wiflix List] Could not detect total pages via Puppeteer for ${baseUrl}, defaulting to 1.`);
        return 1;
    } catch (error) {
        logger.error(`[Wiflix List] Error detecting total pages via Puppeteer for ${baseUrl}: ${error.message}`);
        return 1; // Default to 1 on error
    }
}
// --- End Pagination Detection ---


async function scrapeWiflixList(type, endpointConfig) {
    const allItems = [];
    const endpoint = Object.keys(endpointConfig || {})[0] || (type === 'series' ? 'serie-en-streaming' : 'film-en-streaming');
    let requestedMaxPages = 1;
     // Correctly handle page limits passed via endpointConfig
     if (endpointConfig && typeof endpointConfig[endpoint] === 'number') {
         requestedMaxPages = endpointConfig[endpoint];
     } else if (endpointConfig && Array.isArray(endpointConfig[endpoint])) {
         requestedMaxPages = endpointConfig[endpoint].length > 0 ? Math.max(...endpointConfig[endpoint]) : 1;
     }

    // Get the latest WIFLIX_BASE from the database
    const wiflixBase = await Config.getValue('WIFLIX_BASE', WIFLIX_BASE);
    WIFLIX_BASE_URL = `https://${wiflixBase}`;
    logger.info(`[Wiflix List] Using Wiflix base URL: ${WIFLIX_BASE_URL}`);

    const concurrencyLimit = 1; // Reduced to 1 to match MAX_PAGES in browserUtils
    const limit = pLimit(concurrencyLimit);

    // Actual page detection happens in the master script using the exported function
    const actualMaxPages = requestedMaxPages === -1 ? 999 : Math.max(1, requestedMaxPages);
    const pagesToScrape = Array.from({ length: actualMaxPages }, (_, i) => i + 1);

    logger.info(`[Wiflix List] Starting Wiflix ${type} list scrape for "${endpoint}", planning to scrape UP TO ${pagesToScrape.length} pages using Puppeteer... Base URL: ${WIFLIX_BASE_URL}`);

    let stopPagination = false;

    const scrapePromises = pagesToScrape.map(page => limit(async () => {
        if (stopPagination) return [];

        const url = page === 1
            ? `${WIFLIX_BASE_URL}/${endpoint}/`
            : `${WIFLIX_BASE_URL}/${endpoint}/page/${page}/`;

        try {
            logger.info(`[Wiflix List] Fetching page ${page} for ${endpoint} using Puppeteer...`);

            // Add a small delay between page requests to avoid overwhelming the queue
            if (page > 1) {
                const pageDelay = 1000; // 1 second delay between pages
                logger.debug(`[Wiflix List] Adding ${pageDelay}ms delay between page requests...`);
                await new Promise(resolve => setTimeout(resolve, pageDelay));
            }

            const html = await fetchPageWithPuppeteer(url, 90000, 0);

            if (!html) {
                logger.error(`[Wiflix List] Puppeteer returned null HTML for ${url}`);
                throw new Error("Puppeteer returned null HTML");
            }

            if (!containsRealContent(html)) {
                logger.warn(`[Wiflix List] Puppeteer HTML for ${url} missing key content. Skipping page.`);
                if (page > 1) stopPagination = true;
                return [];
            }

            // Log success
            logger.info(`[Wiflix List] Successfully parsed HTML content for ${url}`);

            // Log the HTML content for debugging
            logger.info(`[Wiflix List] HTML content length for ${url}: ${html.length} characters`);

            // Load HTML with cheerio with more flexible options
            const $ = cheerio.load(html, {
                normalizeWhitespace: true,
                xmlMode: false,
                decodeEntities: true
            });

            const pageItems = [];

            // Use a more flexible approach to find movie items
            // First, try the standard selectors
            const standardSelectors = [
                'div.mov.clearfix',
                'div.mov',
                '.short',
                '.short-in',
                '.th-item',
                '.th-in',
                '.movie-item',
                '.film-item',
                '.item',
                '.poster',
                '.card',
                '.card-movie'
            ];

            let foundItems = false;
            let itemSelector = '';

            // Try each selector until we find items
            for (const selector of standardSelectors) {
                const count = $(selector).length;
                logger.info(`[Wiflix List] Selector '${selector}' found ${count} items`);

                if (count > 0) {
                    itemSelector = selector;
                    foundItems = true;
                    break;
                }
            }

            // If no items found with standard selectors, try a more aggressive approach
            if (!foundItems) {
                logger.warn(`[Wiflix List] No items found with standard selectors, trying more aggressive approach`);

                // Try to find any links that might be movie items
                const links = $('a[href*="film-en-streaming"], a[href*="serie-en-streaming"]');
                logger.info(`[Wiflix List] Found ${links.length} links that might be movie items`);

                if (links.length > 0) {
                    // Process links directly
                    links.each((_i, link) => {
                        const element = $(link);
                        const href = element.attr('href');
                        const title = element.text().trim() || element.attr('title') || '';

                        if (href && title && !href.includes('/page/')) {
                            const absoluteDetailUrl = href.startsWith('http') ? href : `${WIFLIX_BASE_URL}${href.startsWith('/') ? '' : '/'}${href}`;

                            // Extract image if available
                            let image = undefined;
                            const img = element.find('img').first();
                            if (img.length > 0) {
                                const src = img.attr('src') || img.attr('data-src');
                                if (src) {
                                    image = src.startsWith('http') ? src : `${WIFLIX_BASE_URL}${src.startsWith('/') ? '' : '/'}${src}`;
                                }
                            }

                            // Extract path
                            const detailUrlPath = extractPathFromUrl(absoluteDetailUrl, 'Wiflix List');
                            const imagePath = image ? extractPathFromUrl(image, 'Wiflix List') : '';

                            const item = {
                                title,
                                detailUrl: absoluteDetailUrl,
                                detailUrlPath: detailUrlPath || undefined,
                                image: image || undefined,
                                imagePath: imagePath || undefined,
                                type: type
                            };

                            pageItems.push(item);
                        }
                    });

                    logger.info(`[Wiflix List] Extracted ${pageItems.length} items using aggressive approach`);
                    foundItems = pageItems.length > 0;
                }
            }



            // Process items if we found them using standard selectors
            if (foundItems && itemSelector) {
                logger.info(`[Wiflix List] Processing items using selector: ${itemSelector}`);

                $(itemSelector).each((_i, el) => {
                    const element = $(el);
                    let title = null, detailUrl = null, imageRelative = null;

                    try {
                        // Use more flexible selectors
                        const titleElement = element.find('a.mov-t.nowrap, a.mov-t, .mov-t a, .mov-t, .title, .name, h2, h3, .film-title, .movie-title');
                        const linkElement = element.find('div.mov-mask.ps-link, div.mov-mask, .ps-link, a[href*="film-en-streaming"], a[href*="serie-en-streaming"], a');
                        const imageElement = element.find('div.mov-i img, .mov-i img, img, .poster img, .thumb img');

                        // Try different ways to get the title
                        title = titleElement.text()?.trim() || element.find('a').text()?.trim() || element.attr('title')?.trim();

                        // Try different ways to get the detail URL
                        detailUrl = linkElement.attr('data-link') || linkElement.attr('href') || element.find('a').attr('href');

                        // If element itself is an anchor, use its href
                        if (!detailUrl && element.is('a')) {
                            detailUrl = element.attr('href');
                        }

                        // Try different ways to get the image
                        imageRelative = imageElement.attr('src') || imageElement.attr('data-src') || imageElement.attr('data-original');

                        // Log what we found for debugging
                        logger.debug(`[Wiflix List] Found item: Title='${title}', DetailURL='${detailUrl}', Image='${imageRelative}'`);

                        // Stricter check
                        if (title && detailUrl && !detailUrl.includes('javascript:')) {
                            const absoluteDetailUrl = detailUrl.startsWith('http') ? detailUrl : `${WIFLIX_BASE_URL}${detailUrl.startsWith('/') ? '' : '/'}${detailUrl}`;

                            if (absoluteDetailUrl.includes('/page/') || absoluteDetailUrl.endsWith(`/${endpoint}/`)) {
                                logger.warn(`[Wiflix List] Skipping item on ${url} - detailUrl seems invalid: ${absoluteDetailUrl}`);
                                return; // Skip this iteration
                            }

                            const image = imageRelative ? (imageRelative.startsWith('http') ? imageRelative : `${WIFLIX_BASE_URL}${imageRelative.startsWith('/') ? '' : '/'}${imageRelative}`) : undefined;

                            // --- START: Path Extraction ---
                            const detailUrlPath = extractPathFromUrl(absoluteDetailUrl, 'Wiflix List');
                            const imagePath = image ? extractPathFromUrl(image, 'Wiflix List') : '';
                            // --- END: Path Extraction ---

                            const item = {
                                title,
                                detailUrl: absoluteDetailUrl, // Keep full URL for potential fallback/debugging
                                detailUrlPath: detailUrlPath || undefined, // Store path, use undefined if empty
                                image: image || undefined, // Keep full URL for potential fallback/debugging
                                imagePath: imagePath || undefined, // Store path, use undefined if empty
                                type: type
                            };

                            if (type === 'series') {
                                const blockSaiText = element.find('.block-sai').text().trim();
                                const qualityText = element.find('.quality, .eps span, .metadata span.extra').first().text().trim();
                                const seasonMatch = blockSaiText.match(/Saison\s*(\d+)/i) || qualityText.match(/Saison\s*(\d+)/i);
                                item.season = seasonMatch ? seasonMatch[1] : '1';
                            }
                            pageItems.push(item);
                        } else {
                            logger.warn(`[Wiflix List] Skipping item on ${url}: Validation Failed! Title='${title}', DetailURL='${detailUrl}'`);
                        }
                    } catch (extractError) {
                        logger.error(`[Wiflix List] Error extracting data for one item on ${url}: ${extractError.message}`);
                    }
                }); // End .each loop
            }

            logger.info(`[Wiflix List] Scraped Wiflix ${type} endpoint "${endpoint}" page ${page} - Found & Validated: ${pageItems.length} items`);

            // Stop pagination if no items found on a page > 1
            if (page > 1 && pageItems.length === 0) {
                logger.warn(`[Wiflix List] No items found or validated on Wiflix ${type} page ${page}. Stopping pagination for ${endpoint}.`);
                stopPagination = true;
            }
            return pageItems;

        } catch (error) {
             if (error.message.includes('status 404')) {
                logger.warn(`[Wiflix List] Page ${page} for ${endpoint} returned 404 via Puppeteer. Stopping pagination for ${endpoint}.`);
                stopPagination = true;
            } else if (error.name === 'TimeoutError') {
                 logger.warn(`[Wiflix List] Puppeteer timed out fetching page ${page} for ${endpoint}. Stopping pagination for ${endpoint}.`);
                 stopPagination = true;
            } else {
                logger.error(`[Wiflix List] Failed to scrape page ${page} for ${endpoint} via Puppeteer: ${error.message}`);
                stopPagination = true; // Stop on other critical errors too
            }
            return [];
        }
    }));

    // Process pages sequentially to avoid concurrency issues
    for (const scrapePromise of scrapePromises) {
        const pageItems = await scrapePromise;
        allItems.push(...pageItems);
    }

    // Use detailUrlPath for uniqueness if available, otherwise fallback to detailUrl
    const uniqueItems = Array.from(
        new Map(
            allItems.map(item => [item.detailUrlPath || item.detailUrl, item])
        ).values()
    );
    logger.info(`[Wiflix List] Finished Wiflix ${type} list scrape for endpoint "${endpoint}". Total unique items collected: ${uniqueItems.length}`);
    return uniqueItems;
}

module.exports = { scrapeWiflixList, detectTotalWiflixPages }; // Export both functions