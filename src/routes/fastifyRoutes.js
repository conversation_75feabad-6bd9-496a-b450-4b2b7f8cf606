// File: src/routes/fastifyRoutes.js
// Fastify API Routes - High Performance Migration
// Optimized routes with validation, caching, and rate limiting

const { ObjectId } = require('mongodb');

async function fastifyRoutes(fastify, options) {
  // Proxy image route with caching and validation
  fastify.get('/proxy-image', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string', format: 'uri' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 100,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    let { url } = request.query;

    try {
      // Get current base URLs from config (key-value structure)
      const wiflixConfig = await request.db.collection('config').findOne({ key: 'WIFLIX_BASE' });
      const currentWiflixBase = wiflixConfig?.value || 'flemmix.vip';

      // Comprehensive domain replacement for all known old domains
      const oldWiflixDomainRegex = /(wiflix-max|flemmix|wiflix)\.(site|top|org|net|com|cam|ws|vip|cc|tv|me|info)/i;

      if (oldWiflixDomainRegex.test(url)) {
        // Extract the path and query from the original URL
        const urlObj = new URL(url);
        const oldDomain = urlObj.hostname;
        const newUrl = `https://${currentWiflixBase}${urlObj.pathname}${urlObj.search}`;
        url = newUrl;
        fastify.log.info(`Updated domain in URL: ${oldDomain} -> ${currentWiflixBase}`);
      }

      // Check cache first
      const cacheKey = `proxy-image:${Buffer.from(url).toString('base64')}`;

      if (request.cacheService) {
        const cached = await request.cacheService.get(cacheKey);
        if (cached) {
          reply.type(cached.contentType);
          return Buffer.from(cached.data, 'base64');
        }
      }

      // Fetch image
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/*,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Cache-Control': 'no-cache'
        },
        timeout: 10000
      });

      if (!response.ok) {
        reply.code(404);
        return { error: 'Image not found' };
      }

      const contentType = response.headers.get('content-type') || 'image/jpeg';
      const buffer = await response.arrayBuffer();
      const imageData = Buffer.from(buffer);

      // Cache the image for 1 hour
      if (request.cacheService && imageData.length < 1024 * 1024) { // Cache only if < 1MB
        await request.cacheService.set(cacheKey, {
          data: imageData.toString('base64'),
          contentType
        }, 3600);
      }

      reply.type(contentType);
      reply.header('Cache-Control', 'public, max-age=3600');
      return imageData;

    } catch (error) {
      fastify.log.error('Proxy image error:', error);
      reply.code(500);
      return { error: 'Failed to fetch image' };
    }
  });

  // Proxy video route for LiveTV and streaming
  fastify.get('/proxy-video', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string' },
          referer: { type: 'string' },
          fetch_token: { type: 'string' },
          direct: { type: 'string' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 200,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { url, referer, fetch_token, direct } = request.query;

    if (!url) {
      reply.code(400);
      return { error: 'Missing URL parameter' };
    }

    try {
      const decodedUrl = decodeURIComponent(url);
      const decodedReferer = referer ? decodeURIComponent(referer) : decodedUrl;

      // Get current base URLs from config (key-value structure)
      const witvConfig = await request.db.collection('config').findOne({ key: 'WITV_BASE' });
      const witvBase = witvConfig?.value || 'witv.skin';

      const isWitvSkin = decodedUrl.includes(witvBase) || decodedUrl.includes('play.witv');

      // If fetch_token is true, we're just fetching the token
      if (fetch_token === 'true' && isWitvSkin) {
        const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
        if (!channelIdMatch) {
          reply.code(400);
          return { error: 'Could not extract channel ID from URL' };
        }

        const channelId = channelIdMatch[1];
        const originalUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;

        // Redirect to the proxy-token endpoint
        reply.redirect(`/proxy-token?url=${encodeURIComponent(originalUrl)}`);
        return;
      }

      // Set up headers for the request
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      };

      if (decodedReferer) {
        headers['Referer'] = decodedReferer;
      }

      fastify.log.info(`Proxying video request to: ${decodedUrl.substring(0, 100)}...`);

      // Use dynamic import for fetch if not available globally
      let fetchFn;
      if (typeof fetch !== 'undefined') {
        fetchFn = fetch;
      } else {
        const { default: nodeFetch } = await import('node-fetch');
        fetchFn = nodeFetch;
      }

      // Fetch the video content
      const response = await fetchFn(decodedUrl, {
        headers,
        timeout: 30000
      });

      if (!response.ok) {
        fastify.log.error(`Video proxy error: ${response.status} ${response.statusText}`);
        reply.code(response.status);
        return { error: `Video not found: ${response.statusText}` };
      }

      const contentType = response.headers.get('content-type') || 'video/mp4';

      // Handle M3U8 playlists specially
      if (contentType.includes('mpegurl') || decodedUrl.includes('.m3u8')) {
        const m3u8Content = await response.text();

        // Process M3U8 content to fix relative URLs
        const baseUrl = new URL(decodedUrl).origin;
        const lines = m3u8Content.split('\n');
        const processedLines = lines.map(line => {
          // Skip comments and empty lines
          if (line.startsWith('#') || line.trim() === '') {
            return line;
          }

          // Convert relative segment URLs to proxy URLs
          if (line.startsWith('/hls/')) {
            const segmentUrl = `${baseUrl}${line}`;
            const proxyUrl = `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
            return proxyUrl;
          }

          return line;
        });

        const processedContent = processedLines.join('\n');

        reply.type('application/vnd.apple.mpegurl');
        reply.header('Access-Control-Allow-Origin', '*');
        reply.header('Access-Control-Allow-Headers', '*');
        reply.header('Cache-Control', 'no-cache');

        return processedContent;
      }

      // For other content types, stream the response
      const buffer = await response.arrayBuffer();
      const videoData = Buffer.from(buffer);

      reply.type(contentType);
      reply.header('Access-Control-Allow-Origin', '*');
      reply.header('Access-Control-Allow-Headers', '*');
      reply.header('Content-Length', videoData.length);

      return videoData;

    } catch (error) {
      fastify.log.error('Proxy video error:', error);
      reply.code(500);
      return { error: `Video proxy error: ${error.message}` };
    }
  });

  // Proxy token route for witv.skin authentication
  fastify.get('/proxy-token', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string' }
        },
        required: ['url']
      }
    }
  }, async (request, reply) => {
    const { url } = request.query;

    if (!url) {
      reply.code(400);
      return { error: 'Missing URL parameter' };
    }

    try {
      const decodedUrl = decodeURIComponent(url);

      // Get current base URLs from config
      const witvConfig = await request.db.collection('config').findOne({ key: 'WITV_BASE' });
      const witvBase = witvConfig?.value || 'witv.skin';

      const isWitvSkin = decodedUrl.includes(witvBase) || decodedUrl.includes('play.witv');

      if (!isWitvSkin) {
        reply.code(400);
        return { error: 'Not a witv.skin URL' };
      }

      // Extract channel ID from URL
      const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
      if (!channelIdMatch) {
        reply.code(400);
        return { error: 'Could not extract channel ID from URL' };
      }

      const channelId = channelIdMatch[1];

      // Try to get a fresh token
      const authUrl = `https://${witvBase}/auth.php?channel=${channelId}`;

      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      };

      // Use dynamic import for fetch if not available globally
      let fetchFn;
      if (typeof fetch !== 'undefined') {
        fetchFn = fetch;
      } else {
        const { default: nodeFetch } = await import('node-fetch');
        fetchFn = nodeFetch;
      }

      const authResponse = await fetchFn(authUrl, {
        headers,
        redirect: 'manual',
        timeout: 10000
      });

      if (authResponse.status === 302 || authResponse.status === 301) {
        let location = authResponse.headers.get('location');

        // Fix the strange format with "8;;" at the beginning
        if (location && location.startsWith('8;;')) {
          location = location.substring(3);

          const parts = location.split('8;;');
          if (parts.length > 1) {
            location = parts[0];
          }
        }

        if (location) {
          return location;
        }
      }

      // If no redirect, try the live URL directly
      const liveUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;
      return liveUrl;

    } catch (error) {
      fastify.log.error('Proxy token error:', error);
      reply.code(500);
      return { error: `Token fetch error: ${error.message}` };
    }
  });

  // Stream URL route with validation
  fastify.get('/stream/:id', {
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] },
          ep: { type: 'string' },
          lang: { type: 'string' }
        }
      }
    },
    preHandler: fastify.rateLimit({
      max: 50,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { id } = request.params;
    const { type, ep, lang } = request.query;

    try {
      if (!ObjectId.isValid(id)) {
        reply.code(400);
        return { error: 'Invalid ID format' };
      }

      const collectionMap = {
        MOVIE: 'movies',
        SERIES: 'series',
        ANIME: 'animes',
        LIVETV: 'livetv'
      };

      const collection = collectionMap[type] || 'movies';
      const item = await request.db.collection(collection).findOne({
        _id: new ObjectId(id)
      });

      if (!item) {
        reply.code(404);
        return { error: 'Item not found' };
      }

      let streamingUrl = null;

      // Find streaming URL based on type and episode
      if (type === 'MOVIE' || type === 'LIVETV') {
        if (item.streamingUrls && item.streamingUrls.length > 0) {
          const filteredUrls = lang ? 
            item.streamingUrls.filter(url => url.language === lang) :
            item.streamingUrls;
          streamingUrl = filteredUrls[0];
        }
      } else {
        if (item.episodes && ep) {
          const episode = item.episodes.find(e => 
            e.episodeNumber === ep || e.episodeNumber === ep.toString()
          );
          
          if (episode && episode.streamingUrls && episode.streamingUrls.length > 0) {
            const filteredUrls = lang ?
              episode.streamingUrls.filter(url => url.language === lang) :
              episode.streamingUrls;
            streamingUrl = filteredUrls[0];
          }
        }
      }

      if (!streamingUrl) {
        reply.code(404);
        return { error: 'No streaming URL found' };
      }

      return {
        url: streamingUrl.url || streamingUrl.sourceStreamUrl,
        type: streamingUrl.type,
        size: streamingUrl.size,
        method: streamingUrl.method
      };

    } catch (error) {
      fastify.log.error('Stream URL error:', error);
      reply.code(500);
      return { error: 'Failed to get stream URL' };
    }
  });

  // Admin routes
  fastify.register(async function (fastify) {
    // Admin authentication hook
    fastify.addHook('preHandler', async (request, reply) => {
      const adminToken = request.headers['x-admin-token'] || request.query.adminToken;
      
      if (!adminToken) {
        reply.code(401);
        throw new Error('Admin token required');
      }

      // Validate admin token
      const session = await request.db.collection('admin').findOne({
        type: 'session',
        token: adminToken,
        expiresAt: { $gt: new Date() }
      });

      if (!session) {
        reply.code(401);
        throw new Error('Invalid or expired admin token');
      }

      request.isAdmin = true;
    });

    // Delete item route
    fastify.delete('/admin/item/:id', {
      schema: {
        params: {
          type: 'object',
          properties: {
            id: { type: 'string' }
          },
          required: ['id']
        },
        querystring: {
          type: 'object',
          properties: {
            type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] }
          },
          required: ['type']
        }
      }
    }, async (request, reply) => {
      const { id } = request.params;
      const { type } = request.query;

      try {
        if (!ObjectId.isValid(id)) {
          reply.code(400);
          return { error: 'Invalid ID format' };
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          reply.code(400);
          return { error: 'Invalid item type' };
        }

        const result = await request.db.collection(collection).deleteOne({
          _id: new ObjectId(id)
        });

        if (result.deletedCount === 0) {
          reply.code(404);
          return { error: 'Item not found' };
        }

        // Clear related cache
        if (request.cacheService) {
          await request.cacheService.invalidateByTags([collection, 'items']);
        }

        return {
          success: true,
          message: 'Item deleted successfully',
          deletedCount: result.deletedCount
        };

      } catch (error) {
        fastify.log.error('Delete item error:', error);
        reply.code(500);
        return { error: 'Failed to delete item' };
      }
    });

    // Scrape URL manually route
    fastify.post('/admin/scrape', {
      schema: {
        body: {
          type: 'object',
          properties: {
            url: { type: 'string', format: 'uri' },
            type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] }
          },
          required: ['url', 'type']
        }
      }
    }, async (request, reply) => {
      const { url, type } = request.body;

      try {
        // Add scraping job to queue
        const jobData = { url, type, manual: true };
        
        // This would integrate with the job queue
        // const job = await request.scrapeWorker.addScrapeJob(`scrape${type}`, jobData);

        return {
          success: true,
          message: 'Scraping job added to queue',
          url,
          type
          // jobId: job.id
        };

      } catch (error) {
        fastify.log.error('Manual scrape error:', error);
        reply.code(500);
        return { error: 'Failed to add scraping job' };
      }
    });

    // Update base URL route
    fastify.put('/admin/config/base-url', {
      schema: {
        body: {
          type: 'object',
          properties: {
            wiflixBase: { type: 'string' },
            frenchAnimeBase: { type: 'string' },
            witvBase: { type: 'string' }
          }
        }
      }
    }, async (request, reply) => {
      const updates = request.body;

      try {
        const result = await request.db.collection('config').updateOne(
          {},
          { $set: { ...updates, updatedAt: new Date() } },
          { upsert: true }
        );

        // Clear config cache
        if (request.cacheService) {
          await request.cacheService.del('config');
        }

        return {
          success: true,
          message: 'Base URLs updated successfully',
          updates
        };

      } catch (error) {
        fastify.log.error('Update base URL error:', error);
        reply.code(500);
        return { error: 'Failed to update base URLs' };
      }
    });

    // Get scraping logs route
    fastify.get('/admin/logs/:logId', {
      schema: {
        params: {
          type: 'object',
          properties: {
            logId: { type: 'string' }
          },
          required: ['logId']
        }
      }
    }, async (request, reply) => {
      const { logId } = request.params;

      try {
        // This would integrate with logging system
        const logs = await request.db.collection('scrape_logs').find({
          logId
        }).sort({ timestamp: -1 }).limit(100).toArray();

        return {
          logId,
          logs: logs.map(log => ({
            timestamp: log.timestamp,
            level: log.level,
            message: log.message,
            data: log.data
          }))
        };

      } catch (error) {
        fastify.log.error('Get logs error:', error);
        reply.code(500);
        return { error: 'Failed to get logs' };
      }
    });
  });

  // WebSocket route for real-time logs
  fastify.register(async function (fastify) {
    fastify.get('/ws/logs/:logId', { websocket: true }, (connection, request) => {
      const { logId } = request.params;
      
      connection.socket.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          
          if (data.type === 'subscribe') {
            // Subscribe to log stream
            fastify.log.info(`Client subscribed to logs: ${logId}`);
            
            // Send initial connection message
            connection.socket.send(JSON.stringify({
              type: 'connected',
              logId,
              timestamp: new Date().toISOString()
            }));
          }
        } catch (error) {
          fastify.log.error('WebSocket message error:', error);
        }
      });
      
      connection.socket.on('close', () => {
        fastify.log.info(`Client disconnected from logs: ${logId}`);
      });
      
      connection.socket.on('error', (error) => {
        fastify.log.error('WebSocket error:', error);
      });
    });
  });

  // Performance monitoring route
  fastify.get('/performance', async (request, reply) => {
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();

    return {
      timestamp: new Date().toISOString(),
      uptime: uptime,
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      },
      cache: request.cacheService ? await request.cacheService.getStats() : null
    };
  });

  // Media detail page routes - serve the media.html for media detail pages
  const fs = require('fs');
  const path = require('path');

  const mediaHtmlPath = path.join(__dirname, '..', '..', 'public', 'media.html');

  fastify.get('/movies/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/series/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/anime/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/livetv/:id', async (request, reply) => {
    // Serve the media.html for media detail pages
    reply.type('text/html');
    const html = fs.readFileSync(mediaHtmlPath, 'utf8');
    return reply.send(html);
  });
}

module.exports = fastifyRoutes;
