// File: src/routes/fastifyRoutes.js
// Fastify API Routes - High Performance Migration
// Optimized routes with validation, caching, and rate limiting

const { ObjectId } = require('mongodb');

async function fastifyRoutes(fastify, options) {
  // Proxy image route with caching and validation
  fastify.get('/proxy-image', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          url: { type: 'string', format: 'uri' }
        },
        required: ['url']
      }
    },
    preHandler: fastify.rateLimit({
      max: 100,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    let { url } = request.query;

    try {
      // Get current base URLs from config
      const config = await request.db.collection('config').findOne({});
      const currentWiflixBase = config?.wiflixBase || 'flemmix.vip';

      // Replace outdated domains with current base URLs
      if (url.includes('flemmix.top') || url.includes('flemmix.ws') || url.includes('wiflix-max.')) {
        // Extract the path and query from the original URL
        const urlObj = new URL(url);
        const newUrl = `https://${currentWiflixBase}${urlObj.pathname}${urlObj.search}`;
        url = newUrl;
        fastify.log.info(`Updated domain in URL: ${url}`);
      }

      // Check cache first
      const cacheKey = `proxy-image:${Buffer.from(url).toString('base64')}`;

      if (request.cacheService) {
        const cached = await request.cacheService.get(cacheKey);
        if (cached) {
          reply.type(cached.contentType);
          return Buffer.from(cached.data, 'base64');
        }
      }

      // Fetch image
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/*,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Cache-Control': 'no-cache'
        },
        timeout: 10000
      });

      if (!response.ok) {
        reply.code(404);
        return { error: 'Image not found' };
      }

      const contentType = response.headers.get('content-type') || 'image/jpeg';
      const buffer = await response.arrayBuffer();
      const imageData = Buffer.from(buffer);

      // Cache the image for 1 hour
      if (request.cacheService && imageData.length < 1024 * 1024) { // Cache only if < 1MB
        await request.cacheService.set(cacheKey, {
          data: imageData.toString('base64'),
          contentType
        }, 3600);
      }

      reply.type(contentType);
      reply.header('Cache-Control', 'public, max-age=3600');
      return imageData;

    } catch (error) {
      fastify.log.error('Proxy image error:', error);
      reply.code(500);
      return { error: 'Failed to fetch image' };
    }
  });

  // Stream URL route with validation
  fastify.get('/stream/:id', {
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] },
          ep: { type: 'string' },
          lang: { type: 'string' }
        }
      }
    },
    preHandler: fastify.rateLimit({
      max: 50,
      timeWindow: '1 minute'
    })
  }, async (request, reply) => {
    const { id } = request.params;
    const { type, ep, lang } = request.query;

    try {
      if (!ObjectId.isValid(id)) {
        reply.code(400);
        return { error: 'Invalid ID format' };
      }

      const collectionMap = {
        MOVIE: 'movies',
        SERIES: 'series',
        ANIME: 'animes',
        LIVETV: 'livetv'
      };

      const collection = collectionMap[type] || 'movies';
      const item = await request.db.collection(collection).findOne({
        _id: new ObjectId(id)
      });

      if (!item) {
        reply.code(404);
        return { error: 'Item not found' };
      }

      let streamingUrl = null;

      // Find streaming URL based on type and episode
      if (type === 'MOVIE' || type === 'LIVETV') {
        if (item.streamingUrls && item.streamingUrls.length > 0) {
          const filteredUrls = lang ? 
            item.streamingUrls.filter(url => url.language === lang) :
            item.streamingUrls;
          streamingUrl = filteredUrls[0];
        }
      } else {
        if (item.episodes && ep) {
          const episode = item.episodes.find(e => 
            e.episodeNumber === ep || e.episodeNumber === ep.toString()
          );
          
          if (episode && episode.streamingUrls && episode.streamingUrls.length > 0) {
            const filteredUrls = lang ?
              episode.streamingUrls.filter(url => url.language === lang) :
              episode.streamingUrls;
            streamingUrl = filteredUrls[0];
          }
        }
      }

      if (!streamingUrl) {
        reply.code(404);
        return { error: 'No streaming URL found' };
      }

      return {
        url: streamingUrl.url || streamingUrl.sourceStreamUrl,
        type: streamingUrl.type,
        size: streamingUrl.size,
        method: streamingUrl.method
      };

    } catch (error) {
      fastify.log.error('Stream URL error:', error);
      reply.code(500);
      return { error: 'Failed to get stream URL' };
    }
  });

  // Admin routes
  fastify.register(async function (fastify) {
    // Admin authentication hook
    fastify.addHook('preHandler', async (request, reply) => {
      const adminToken = request.headers['x-admin-token'] || request.query.adminToken;
      
      if (!adminToken) {
        reply.code(401);
        throw new Error('Admin token required');
      }

      // Validate admin token
      const session = await request.db.collection('admin').findOne({
        type: 'session',
        token: adminToken,
        expiresAt: { $gt: new Date() }
      });

      if (!session) {
        reply.code(401);
        throw new Error('Invalid or expired admin token');
      }

      request.isAdmin = true;
    });

    // Delete item route
    fastify.delete('/admin/item/:id', {
      schema: {
        params: {
          type: 'object',
          properties: {
            id: { type: 'string' }
          },
          required: ['id']
        },
        querystring: {
          type: 'object',
          properties: {
            type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] }
          },
          required: ['type']
        }
      }
    }, async (request, reply) => {
      const { id } = request.params;
      const { type } = request.query;

      try {
        if (!ObjectId.isValid(id)) {
          reply.code(400);
          return { error: 'Invalid ID format' };
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          reply.code(400);
          return { error: 'Invalid item type' };
        }

        const result = await request.db.collection(collection).deleteOne({
          _id: new ObjectId(id)
        });

        if (result.deletedCount === 0) {
          reply.code(404);
          return { error: 'Item not found' };
        }

        // Clear related cache
        if (request.cacheService) {
          await request.cacheService.invalidateByTags([collection, 'items']);
        }

        return {
          success: true,
          message: 'Item deleted successfully',
          deletedCount: result.deletedCount
        };

      } catch (error) {
        fastify.log.error('Delete item error:', error);
        reply.code(500);
        return { error: 'Failed to delete item' };
      }
    });

    // Scrape URL manually route
    fastify.post('/admin/scrape', {
      schema: {
        body: {
          type: 'object',
          properties: {
            url: { type: 'string', format: 'uri' },
            type: { type: 'string', enum: ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'] }
          },
          required: ['url', 'type']
        }
      }
    }, async (request, reply) => {
      const { url, type } = request.body;

      try {
        // Add scraping job to queue
        const jobData = { url, type, manual: true };
        
        // This would integrate with the job queue
        // const job = await request.scrapeWorker.addScrapeJob(`scrape${type}`, jobData);

        return {
          success: true,
          message: 'Scraping job added to queue',
          url,
          type
          // jobId: job.id
        };

      } catch (error) {
        fastify.log.error('Manual scrape error:', error);
        reply.code(500);
        return { error: 'Failed to add scraping job' };
      }
    });

    // Update base URL route
    fastify.put('/admin/config/base-url', {
      schema: {
        body: {
          type: 'object',
          properties: {
            wiflixBase: { type: 'string' },
            frenchAnimeBase: { type: 'string' },
            witvBase: { type: 'string' }
          }
        }
      }
    }, async (request, reply) => {
      const updates = request.body;

      try {
        const result = await request.db.collection('config').updateOne(
          {},
          { $set: { ...updates, updatedAt: new Date() } },
          { upsert: true }
        );

        // Clear config cache
        if (request.cacheService) {
          await request.cacheService.del('config');
        }

        return {
          success: true,
          message: 'Base URLs updated successfully',
          updates
        };

      } catch (error) {
        fastify.log.error('Update base URL error:', error);
        reply.code(500);
        return { error: 'Failed to update base URLs' };
      }
    });

    // Get scraping logs route
    fastify.get('/admin/logs/:logId', {
      schema: {
        params: {
          type: 'object',
          properties: {
            logId: { type: 'string' }
          },
          required: ['logId']
        }
      }
    }, async (request, reply) => {
      const { logId } = request.params;

      try {
        // This would integrate with logging system
        const logs = await request.db.collection('scrape_logs').find({
          logId
        }).sort({ timestamp: -1 }).limit(100).toArray();

        return {
          logId,
          logs: logs.map(log => ({
            timestamp: log.timestamp,
            level: log.level,
            message: log.message,
            data: log.data
          }))
        };

      } catch (error) {
        fastify.log.error('Get logs error:', error);
        reply.code(500);
        return { error: 'Failed to get logs' };
      }
    });
  });

  // WebSocket route for real-time logs
  fastify.register(async function (fastify) {
    fastify.get('/ws/logs/:logId', { websocket: true }, (connection, request) => {
      const { logId } = request.params;
      
      connection.socket.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          
          if (data.type === 'subscribe') {
            // Subscribe to log stream
            fastify.log.info(`Client subscribed to logs: ${logId}`);
            
            // Send initial connection message
            connection.socket.send(JSON.stringify({
              type: 'connected',
              logId,
              timestamp: new Date().toISOString()
            }));
          }
        } catch (error) {
          fastify.log.error('WebSocket message error:', error);
        }
      });
      
      connection.socket.on('close', () => {
        fastify.log.info(`Client disconnected from logs: ${logId}`);
      });
      
      connection.socket.on('error', (error) => {
        fastify.log.error('WebSocket error:', error);
      });
    });
  });

  // Performance monitoring route
  fastify.get('/performance', async (request, reply) => {
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();

    return {
      timestamp: new Date().toISOString(),
      uptime: uptime,
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      },
      cache: request.cacheService ? await request.cacheService.getStats() : null
    };
  });

  // Media detail page routes - serve the main HTML for SPA routing
  const fs = require('fs');
  const path = require('path');

  const indexHtmlPath = path.join(__dirname, '..', '..', 'public', 'index.html');

  fastify.get('/movies/:id', async (request, reply) => {
    // Serve the main index.html for client-side routing
    reply.type('text/html');
    const html = fs.readFileSync(indexHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/series/:id', async (request, reply) => {
    // Serve the main index.html for client-side routing
    reply.type('text/html');
    const html = fs.readFileSync(indexHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/anime/:id', async (request, reply) => {
    // Serve the main index.html for client-side routing
    reply.type('text/html');
    const html = fs.readFileSync(indexHtmlPath, 'utf8');
    return reply.send(html);
  });

  fastify.get('/livetv/:id', async (request, reply) => {
    // Serve the main index.html for client-side routing
    reply.type('text/html');
    const html = fs.readFileSync(indexHtmlPath, 'utf8');
    return reply.send(html);
  });
}

module.exports = fastifyRoutes;
