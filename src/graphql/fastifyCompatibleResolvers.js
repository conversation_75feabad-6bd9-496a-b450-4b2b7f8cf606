// File: src/graphql/fastifyCompatibleResolvers.js
// Fastify-compatible resolvers that work with MongoDB driver directly
// Adapted from the original Mongoose-based resolvers

const { ObjectId } = require('mongodb');

// Helper function to fix image URLs using config-based base URLs
async function fixImageUrl(url, db) {
  if (!url) return '/default-thumbnail.jpg';

  try {
    // Get current base URLs from config
    const config = await db.collection('config').findOne({});
    const currentWiflixBase = config?.wiflixBase || 'wiflix-max.cam';

    // Replace outdated domains with current base URLs
    const oldWiflixDomainRegex = /(wiflix-max|flemmix|wiflix)\.(site|top|org|net|com|cam|ws|vip)/i;

    if (oldWiflixDomainRegex.test(url)) {
      const updatedUrl = url.replace(oldWiflixDomainRegex, currentWiflixBase);
      console.log(`Fixed image URL: ${url} -> ${updatedUrl}`);
      return updatedUrl;
    }

    return url;
  } catch (error) {
    console.error('Error fixing image URL:', error);
    return url;
  }
}

// Helper function to convert MongoDB documents to GraphQL format
async function formatDocument(doc, type = null, db = null) {
  if (!doc) return null;

  const imageUrl = db ? await fixImageUrl(doc.thumbnail || doc.image, db) : (doc.thumbnail || doc.image || '/default-thumbnail.jpg');

  const formatted = {
    ...doc,
    id: doc._id.toString(),
    displayTitle: doc.title,
    image: imageUrl,
    thumbnail: imageUrl,
    imagePath: imageUrl, // Add imagePath field
    createdAt: doc.createdAt ? doc.createdAt.toISOString() : new Date().toISOString(),
    updatedAt: doc.updatedAt ? doc.updatedAt.toISOString() : new Date().toISOString()
  };

  // Add type-specific formatting
  if (type) {
    formatted.__typename = type;
  }

  // Ensure episodes are properly formatted for series/anime
  if (formatted.episodes && Array.isArray(formatted.episodes)) {
    formatted.episodes = formatted.episodes.map(ep => ({
      episodeNumber: ep.episodeNumber || ep.episode || '1',
      season: ep.season || formatted.season || '1',
      language: ep.language || 'VF',
      streamingUrls: ep.streamingUrls || []
    }));
  }

  // Ensure streamingUrls exist
  if (!formatted.streamingUrls) {
    formatted.streamingUrls = [];
  }

  return formatted;
}

// Helper function for pagination
function getPaginationParams(page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  return { skip, limit };
}

// Helper function for sorting
function getSortOption(sort) {
  switch (sort) {
    case 'LATEST':
      return { updatedAt: -1 };
    case 'ALPHA':
      return { title: 1 };
    case 'RELEASE':
      return { 'tmdb.release_date': -1, createdAt: -1 };
    case 'TRENDING':
      return { updatedAt: -1 }; // Fallback for now
    default:
      return { updatedAt: -1 };
  }
}

const resolvers = {
  Query: {
    // Database stats
    databaseStats: async (parent, args, context) => {
      const { db } = context;
      try {
        const [movieCount, seriesCount, animeCount, livetvCount] = await Promise.all([
          db.collection('movies').countDocuments(),
          db.collection('series').countDocuments(),
          db.collection('animes').countDocuments(),
          db.collection('livetv').countDocuments()
        ]);
        
        return {
          movies: movieCount,
          series: seriesCount,
          anime: animeCount,
          livetv: livetvCount,
          totalItems: movieCount + seriesCount + animeCount + livetvCount
        };
      } catch (error) {
        console.error('Database stats error:', error);
        return {
          movies: 0,
          series: 0,
          anime: 0,
          livetv: 0,
          totalItems: 0
        };
      }
    },

    // Config
    config: async (parent, args, context) => {
      const { db } = context;
      try {
        const config = await db.collection('config').findOne({});
        return {
          tmdbApiKey: process.env.TMDB_API_KEY || null,
          wiflixBase: config?.wiflixBase || 'wiflix-max.cam',
          frenchAnimeBase: config?.frenchAnimeBase || 'french-anime.com',
          witvBase: config?.witvBase || 'witv.skin'
        };
      } catch (error) {
        console.error('Config error:', error);
        return {
          tmdbApiKey: null,
          wiflixBase: 'wiflix-max.cam',
          frenchAnimeBase: 'french-anime.com',
          witvBase: 'witv.skin'
        };
      }
    },

    // Available genres
    availableGenres: async (parent, args, context) => {
      const { db } = context;
      try {
        // Get unique genres from TMDB data
        const [movieGenres, seriesGenres, animeGenres] = await Promise.all([
          db.collection('movies').distinct('tmdb.genres'),
          db.collection('series').distinct('tmdb.genres'),
          db.collection('animes').distinct('tmdb.genres')
        ]);

        return {
          movies: movieGenres.filter(g => g).flat(),
          series: seriesGenres.filter(g => g).flat(),
          anime: animeGenres.filter(g => g).flat()
        };
      } catch (error) {
        console.error('Available genres error:', error);
        return {
          movies: [],
          series: [],
          anime: []
        };
      }
    },

    // Movies
    movies: async (parent, { sort, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);
      const sortOption = getSortOption(sort);

      try {
        const movies = await db.collection('movies')
          .find({})
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie'));
      } catch (error) {
        console.error('Movies query error:', error);
        return [];
      }
    },

    // Latest movies
    latestMovies: async (parent, { excludeAncien = true, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        let query = {};
        if (excludeAncien) {
          query = { detailUrl: { $not: { $regex: 'film-ancien', $options: 'i' } } };
        }

        const movies = await db.collection('movies')
          .find(query)
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie'));
      } catch (error) {
        console.error('Latest movies error:', error);
        return [];
      }
    },

    // Ancien movies
    ancienMovies: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const movies = await db.collection('movies')
          .find({ detailUrl: { $regex: 'film-ancien', $options: 'i' } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie'));
      } catch (error) {
        console.error('Ancien movies error:', error);
        return [];
      }
    },

    // Series
    series: async (parent, { sort, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);
      const sortOption = getSortOption(sort);

      try {
        const series = await db.collection('series')
          .find({})
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return series.map(item => formatDocument(item, 'Series'));
      } catch (error) {
        console.error('Series query error:', error);
        return [];
      }
    },

    // Latest series
    latestSeries: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const series = await db.collection('series')
          .find({})
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return series.map(item => formatDocument(item, 'Series'));
      } catch (error) {
        console.error('Latest series error:', error);
        return [];
      }
    },

    // Series by genre
    seriesByGenre: async (parent, { genre, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const series = await db.collection('series')
          .find({ 'tmdb.genres': { $in: [genre] } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return series.map(item => formatDocument(item, 'Series'));
      } catch (error) {
        console.error('Series by genre error:', error);
        return [];
      }
    },

    // Anime
    anime: async (parent, { sort, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);
      const sortOption = getSortOption(sort);

      try {
        const animes = await db.collection('animes')
          .find({})
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime'));
      } catch (error) {
        console.error('Anime query error:', error);
        return [];
      }
    },

    // Latest anime
    latestAnime: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const animes = await db.collection('animes')
          .find({})
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime'));
      } catch (error) {
        console.error('Latest anime error:', error);
        return [];
      }
    },

    // Anime movies
    animeMovies: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const animes = await db.collection('animes')
          .find({ detailUrlPath: { $regex: 'films-vf-vostfr', $options: 'i' } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime'));
      } catch (error) {
        console.error('Anime movies error:', error);
        return [];
      }
    },

    // Movies by genre
    moviesByGenre: async (parent, { genre, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const movies = await db.collection('movies')
          .find({ 'tmdb.genres': { $in: [genre] } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie'));
      } catch (error) {
        console.error('Movies by genre error:', error);
        return [];
      }
    },

    // Anime by genre
    animeByGenre: async (parent, { genre, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const animes = await db.collection('animes')
          .find({ 'tmdb.genres': { $in: [genre] } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime'));
      } catch (error) {
        console.error('Anime by genre error:', error);
        return [];
      }
    },

    // Validate admin token
    validateAdminToken: async (parent, { token }, context) => {
      const { db } = context;
      try {
        const session = await db.collection('admin').findOne({
          type: 'session',
          token,
          expiresAt: { $gt: new Date() }
        });
        
        return { isValid: !!session };
      } catch (error) {
        console.error('Validate admin token error:', error);
        return { isValid: false };
      }
    },

    // Content overview
    contentOverview: async (parent, args, context) => {
      const { db } = context;
      try {
        const recentlyAdded = await db.collection('movies').countDocuments({
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        });
        
        return {
          recentlyAdded,
          trending: 0,
          mostWatched: 0,
          totalViews: 0
        };
      } catch (error) {
        console.error('Content overview error:', error);
        return {
          recentlyAdded: 0,
          trending: 0,
          mostWatched: 0,
          totalViews: 0
        };
      }
    },

    // Display settings
    displaySettings: async (parent, { adminToken }, context) => {
      const { db } = context;
      try {
        const config = await db.collection('config').findOne({});
        return {
          gridItemsEnabled: config?.gridItemsEnabled !== false
        };
      } catch (error) {
        console.error('Display settings error:', error);
        return {
          gridItemsEnabled: true
        };
      }
    },

    // Search functionality
    search: async (parent, { query, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const searchRegex = new RegExp(query, 'i');
        const searchQuery = {
          $or: [
            { title: searchRegex },
            { cleanedTitle: searchRegex },
            { 'tmdb.title': searchRegex }
          ]
        };

        const [movies, series, animes, livetv] = await Promise.all([
          db.collection('movies').find(searchQuery).skip(skip).limit(limitNum).toArray(),
          db.collection('series').find(searchQuery).skip(skip).limit(limitNum).toArray(),
          db.collection('animes').find(searchQuery).skip(skip).limit(limitNum).toArray(),
          db.collection('livetv').find(searchQuery).skip(skip).limit(limitNum).toArray()
        ]);

        const items = [
          ...movies.map(m => formatDocument(m, 'Movie')),
          ...series.map(s => formatDocument(s, 'Series')),
          ...animes.map(a => formatDocument(a, 'Anime')),
          ...livetv.map(l => formatDocument(l, 'LiveTV'))
        ];

        return { items };
      } catch (error) {
        console.error('Search error:', error);
        return { items: [] };
      }
    },

    // Item by ID
    item: async (parent, { id, type }, context) => {
      const { db } = context;

      try {
        if (!ObjectId.isValid(id)) {
          throw new Error('Invalid ID format');
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          throw new Error('Invalid item type');
        }

        const item = await db.collection(collection).findOne({
          _id: new ObjectId(id)
        });

        if (!item) {
          return null;
        }

        return formatDocument(item, type.charAt(0) + type.slice(1).toLowerCase());
      } catch (error) {
        console.error('Item by ID error:', error);
        return null;
      }
    },

    // Stream URL
    stream: async (parent, { id, type, ep, lang }, context) => {
      const { db } = context;

      try {
        if (!ObjectId.isValid(id)) {
          throw new Error('Invalid ID format');
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          throw new Error('Invalid item type');
        }

        const item = await db.collection(collection).findOne({
          _id: new ObjectId(id)
        });

        if (!item) {
          throw new Error('Item not found');
        }

        let streamingUrl = null;

        if (type === 'MOVIE' || type === 'LIVETV') {
          if (item.streamingUrls && item.streamingUrls.length > 0) {
            const filteredUrls = lang ?
              item.streamingUrls.filter(url => url.language === lang) :
              item.streamingUrls;
            streamingUrl = filteredUrls[0];
          }
        } else {
          if (item.episodes && ep) {
            const episode = item.episodes.find(e =>
              e.episodeNumber === ep || e.episodeNumber === ep.toString()
            );

            if (episode && episode.streamingUrls && episode.streamingUrls.length > 0) {
              const filteredUrls = lang ?
                episode.streamingUrls.filter(url => url.language === lang) :
                episode.streamingUrls;
              streamingUrl = filteredUrls[0];
            }
          }
        }

        if (!streamingUrl) {
          throw new Error('No streaming URL found');
        }

        return {
          url: streamingUrl.url || streamingUrl.sourceStreamUrl,
          type: streamingUrl.type,
          size: streamingUrl.size,
          method: streamingUrl.method
        };
      } catch (error) {
        console.error('Stream error:', error);
        throw error;
      }
    },

    // Play URL (simplified version of stream)
    play: async (parent, { id, type, ep, lang }, context) => {
      const streamResult = await resolvers.Query.stream(parent, { id, type, ep, lang }, context);
      return {
        url: streamResult?.url || ''
      };
    },

    // Related seasons
    relatedSeasons: async (parent, { tmdbId, currentSeasonNumber }, context) => {
      const { db } = context;

      try {
        const relatedSeries = await db.collection('series').find({
          'tmdb.id': tmdbId,
          season: { $ne: currentSeasonNumber?.toString() }
        }).toArray();

        return relatedSeries.map(series => ({
          seasonNumber: parseInt(series.season) || 1,
          name: `Season ${series.season || 1}`,
          tmdbId: series.tmdb?.id || tmdbId,
          posterPath: series.tmdb?.poster_path || series.thumbnail
        }));
      } catch (error) {
        console.error('Related seasons error:', error);
        return [];
      }
    },

    // Duplicate detail URL paths
    duplicateDetailUrlPaths: async (parent, args, context) => {
      const { db } = context;

      try {
        const duplicates = await db.collection('movies').aggregate([
          { $group: { _id: '$detailUrl', count: { $sum: 1 }, items: { $push: '$$ROOT' } } },
          { $match: { count: { $gt: 1 } } },
          { $limit: 10 }
        ]).toArray();

        return duplicates.map(dup => ({
          detailUrl: dup._id,
          count: dup.count,
          items: dup.items.map(item => formatDocument(item, 'Movie'))
        }));
      } catch (error) {
        console.error('Duplicate detail URLs error:', error);
        return [];
      }
    },

    // LiveTV
    liveTV: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const channels = await db.collection('livetv')
          .find({})
          .sort({ title: 1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return channels.map(channel => formatDocument(channel, 'LiveTV'));
      } catch (error) {
        console.error('LiveTV error:', error);
        return [];
      }
    }
  },

  // Interface resolver
  Item: {
    __resolveType(obj) {
      if (obj.__typename) {
        return obj.__typename;
      }
      
      // Type resolution logic
      if (obj.animeLanguage || obj.jikan) {
        return 'Anime';
      }
      if (obj.episodes && obj.episodes.length > 0) {
        return 'Series';
      }
      if (obj.detailUrl && obj.detailUrl.includes('witv.skin')) {
        return 'LiveTV';
      }
      
      return 'Movie';
    }
  },

  // Mutation resolvers
  Mutation: {
    adminLogin: async (parent, { adminKey }, context) => {
      const { db } = context;
      try {
        const expectedKey = process.env.ADMIN_KEY;
        if (!expectedKey || adminKey !== expectedKey) {
          return {
            success: false,
            message: 'Invalid admin key'
          };
        }

        const token = Buffer.from(`${adminKey}:${Date.now()}`).toString('base64');

        await db.collection('admin').updateOne(
          { type: 'session' },
          {
            $set: {
              token,
              createdAt: new Date(),
              expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
            }
          },
          { upsert: true }
        );

        return {
          success: true,
          token,
          message: 'Login successful'
        };
      } catch (error) {
        console.error('Admin login error:', error);
        return {
          success: false,
          message: 'Login failed'
        };
      }
    },

    deleteItem: async (parent, { id, type, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token'
          };
        }

        if (!ObjectId.isValid(id)) {
          return {
            success: false,
            message: 'Invalid ID format'
          };
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          return {
            success: false,
            message: 'Invalid item type'
          };
        }

        const result = await db.collection(collection).deleteOne({
          _id: new ObjectId(id)
        });

        if (result.deletedCount === 0) {
          return {
            success: false,
            message: 'Item not found'
          };
        }

        return {
          success: true,
          message: 'Item deleted successfully'
        };
      } catch (error) {
        console.error('Delete item error:', error);
        return {
          success: false,
          message: 'Failed to delete item'
        };
      }
    },

    scrapeItem: async (parent, { id, type, adminToken }, context) => {
      return {
        success: false,
        message: 'Scraping functionality not implemented in Fastify migration'
      };
    },

    updateBaseUrl: async (parent, { wiflixBase, frenchAnimeBase, witvBase, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token'
          };
        }

        const updates = {};
        if (wiflixBase) updates.wiflixBase = wiflixBase;
        if (frenchAnimeBase) updates.frenchAnimeBase = frenchAnimeBase;
        if (witvBase) updates.witvBase = witvBase;
        updates.updatedAt = new Date();

        await db.collection('config').updateOne(
          {},
          { $set: updates },
          { upsert: true }
        );

        return {
          success: true,
          message: 'Base URLs updated successfully'
        };
      } catch (error) {
        console.error('Update base URL error:', error);
        return {
          success: false,
          message: 'Failed to update base URLs'
        };
      }
    },

    scrapeUrlManually: async (parent, { url, type, adminToken }, context) => {
      return {
        success: false,
        message: 'Manual scraping functionality not implemented in Fastify migration'
      };
    },

    updateDisplaySettings: async (parent, { gridItemsEnabled, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token'
          };
        }

        await db.collection('config').updateOne(
          {},
          {
            $set: {
              gridItemsEnabled,
              updatedAt: new Date()
            }
          },
          { upsert: true }
        );

        return {
          success: true,
          message: 'Display settings updated successfully'
        };
      } catch (error) {
        console.error('Update display settings error:', error);
        return {
          success: false,
          message: 'Failed to update display settings'
        };
      }
    }
  }
};

module.exports = resolvers;
