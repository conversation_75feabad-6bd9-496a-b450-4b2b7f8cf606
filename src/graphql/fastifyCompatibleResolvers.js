// File: src/graphql/fastifyCompatibleResolvers.js
// Fastify-compatible resolvers that work with MongoDB driver directly
// Adapted from the original Mongoose-based resolvers

const { ObjectId } = require('mongodb');

console.log('🚀 FASTIFY COMPATIBLE RESOLVERS LOADED - NEW VERSION WITH GEMINI AND SCRAPING CONFIG!');

// Helper function to fix image URLs using config-based base URLs
async function fixImageUrl(url, db) {
  if (!url) return '/default-thumbnail.jpg';

  try {
    // Get current base URLs from config (key-value structure)
    const wiflixConfig = await db.collection('config').findOne({ key: 'WIFLIX_BASE' });
    const currentWiflixBase = wiflixConfig?.value || 'flemmix.vip';

    // Comprehensive domain replacement for all known old domains
    const oldWiflixDomainRegex = /(wiflix-max|flemmix|wiflix)\.(site|top|org|net|com|cam|ws|vip|cc|tv|me|info)/i;

    if (oldWiflixDomainRegex.test(url)) {
      try {
        const urlObj = new URL(url);
        const oldDomain = urlObj.hostname;
        const updatedUrl = `https://${currentWiflixBase}${urlObj.pathname}${urlObj.search}`;
        console.log(`Fixed image URL: ${oldDomain} -> ${currentWiflixBase}`);
        return updatedUrl;
      } catch (urlError) {
        // Fallback to simple string replacement if URL parsing fails
        const updatedUrl = url.replace(oldWiflixDomainRegex, currentWiflixBase);
        console.log(`Fixed image URL (fallback): ${url} -> ${updatedUrl}`);
        return updatedUrl;
      }
    }

    return url;
  } catch (error) {
    console.error('Error fixing image URL:', error);
    return url;
  }
}

// Helper function to convert MongoDB documents to GraphQL format
async function formatDocument(doc, type = null, db = null) {
  if (!doc) return null;

  const imageUrl = db ? await fixImageUrl(doc.thumbnail || doc.image, db) : (doc.thumbnail || doc.image || '/default-thumbnail.jpg');

  const formatted = {
    ...doc,
    id: doc._id.toString(),
    displayTitle: doc.title,
    image: imageUrl,
    thumbnail: imageUrl,
    imagePath: imageUrl, // Add imagePath field
    createdAt: doc.createdAt ? doc.createdAt.toISOString() : new Date().toISOString(),
    updatedAt: doc.updatedAt ? doc.updatedAt.toISOString() : new Date().toISOString()
  };

  // Add type-specific formatting
  if (type) {
    formatted.__typename = type;
  }

  // Ensure episodes are properly formatted for series/anime
  if (formatted.episodes && Array.isArray(formatted.episodes)) {
    formatted.episodes = formatted.episodes.map(ep => ({
      episodeNumber: ep.episodeNumber || ep.episode || '1',
      season: ep.season || formatted.season || '1',
      language: ep.language || 'VF',
      streamingUrls: ep.streamingUrls || []
    }));
  }

  // Ensure streamingUrls exist and have proper IDs
  if (!formatted.streamingUrls) {
    formatted.streamingUrls = [];
  } else if (Array.isArray(formatted.streamingUrls)) {
    formatted.streamingUrls = formatted.streamingUrls
      .filter(url => url && typeof url === 'object') // Filter out null/undefined values
      .map((url, index) => ({
        ...url,
        id: url.id || url._id?.toString() || `${formatted.id}-stream-${index}`,
        type: url.type || 'HLS',
        size: url.size || 'HD',
        method: url.method || 'GET'
      }));
  }

  // Fix episode streaming URLs if episodes exist
  if (formatted.episodes && Array.isArray(formatted.episodes)) {
    formatted.episodes = formatted.episodes.map((episode, episodeIndex) => {
      if (episode.streamingUrls && Array.isArray(episode.streamingUrls)) {
        episode.streamingUrls = episode.streamingUrls
          .filter(url => url && typeof url === 'object')
          .map((url, urlIndex) => ({
            ...url,
            id: url.id || url._id?.toString() || `${formatted.id}-ep${episodeIndex}-stream-${urlIndex}`,
            type: url.type || 'HLS',
            size: url.size || 'HD',
            method: url.method || 'GET'
          }));
      } else {
        episode.streamingUrls = [];
      }
      return episode;
    });
  }

  return formatted;
}

// Helper function for pagination
function getPaginationParams(page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  return { skip, limit };
}

// Helper function for sorting
function getSortOption(sort) {
  switch (sort) {
    case 'LATEST':
      return { updatedAt: -1 };
    case 'ALPHA':
      return { title: 1 };
    case 'RELEASE':
      return { 'tmdb.release_date': -1, createdAt: -1 };
    case 'TRENDING':
      return { updatedAt: -1 }; // Fallback for now
    default:
      return { updatedAt: -1 };
  }
}

// Helper function to get trending items
async function getTrendingItems(mediaType, page, limit, db) {
  console.log(`🔥 TRENDING: Getting trending ${mediaType} items (page=${page}, limit=${limit})`);

  try {
    const skip = (page - 1) * limit;

    // Query trendingitems collection directly
    const trendingItems = await db.collection('trendingitems')
      .find({ mediaType })
      .sort({ rank: 1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    console.log(`📊 TRENDING: Found ${trendingItems.length} trending items:`, trendingItems.map(t => `${t.tmdbId}(rank:${t.rank})`));

    if (trendingItems.length === 0) {
      console.log(`⚠️ TRENDING: No trending items found, falling back to latest`);
      const collection = mediaType === 'movie' ? 'movies' :
                        mediaType === 'tv' ? 'series' : 'animes';

      const fallbackItems = await db.collection(collection)
        .find({})
        .sort({ updatedAt: -1 })
        .skip(skip)
        .limit(limit)
        .toArray();

      return await Promise.all(
        fallbackItems.map(item => formatDocument(item, getTypeName(collection), db))
      );
    }

    // Get the actual media items
    const tmdbIds = trendingItems.map(item => item.tmdbId);
    const collectionName = mediaType === 'movie' ? 'movies' :
                          mediaType === 'tv' ? 'series' : 'animes';

    console.log(`🎯 TRENDING: Looking in ${collectionName} for TMDB IDs:`, tmdbIds);

    const mediaItems = await db.collection(collectionName)
      .find({ 'tmdb.id': { $in: tmdbIds } })
      .project({
        _id: 1,
        title: 1,
        displayTitle: 1,
        thumbnail: 1,
        image: 1,
        detailUrl: 1,
        detailUrlPath: 1,
        streamingUrls: 1,
        metadata: 1,
        tmdb: 1, // Select entire tmdb object
        jikan: 1, // Select entire jikan object
        season: 1, // For series/anime
        episodes: 1, // For series/anime
        animeLanguage: 1, // For anime
        category: 1, // For livetv
        language: 1, // For livetv
        country: 1 // For livetv
      })
      .toArray();

    console.log(`📦 TRENDING: Found ${mediaItems.length} media items`);

    // Create rank map and add trendingRank to items
    const rankMap = {};
    trendingItems.forEach(item => {
      rankMap[item.tmdbId] = item.rank;
    });

    const result = await Promise.all(
      mediaItems.map(async item => {
        const formatted = await formatDocument(item, getTypeName(collectionName), db);
        const rank = rankMap[item.tmdb?.id] !== undefined ? rankMap[item.tmdb?.id] : null;
        return {
          ...formatted,
          trendingRank: rank,
          trendingRankDisplay: rank === 0 ? '★' : rank !== null ? (rank + 1).toString() : null
        };
      })
    );

    // Sort by trending rank: items with rank come first (0, 1, 2...), then items without rank
    result.sort((a, b) => {
      if (a.trendingRank !== null && b.trendingRank !== null) {
        return a.trendingRank - b.trendingRank;
      }
      if (a.trendingRank !== null && b.trendingRank === null) {
        return -1; // a comes first
      }
      if (a.trendingRank === null && b.trendingRank !== null) {
        return 1; // b comes first
      }
      return 0; // both null, maintain order
    });

    console.log(`✅ TRENDING: Returning ${result.length} items with ranks:`, result.map(r => `${r.title}(${r.trendingRank})`));
    return result;

  } catch (error) {
    console.error(`❌ TRENDING ERROR:`, error);
    // Fallback to latest
    const collection = mediaType === 'movie' ? 'movies' :
                      mediaType === 'tv' ? 'series' : 'animes';
    const skip = (page - 1) * limit;

    const fallbackItems = await db.collection(collection)
      .find({})
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    return await Promise.all(
      fallbackItems.map(item => formatDocument(item, getTypeName(collection), db))
    );
  }
}

// Helper function to get type name
function getTypeName(collection) {
  const typeMap = {
    movies: 'Movie',
    series: 'Series',
    animes: 'Anime',
    livetv: 'LiveTV'
  };
  return typeMap[collection] || 'Unknown';
}

// Helper function to generate trending rank icon information
function getTrendingRankIcon(rank) {
  if (rank === null || rank === undefined) {
    return null;
  }

  const displayRank = rank + 1; // Convert 0-based to 1-based for display

  if (rank === 0) {
    // Special star icon for rank 0 (1st place)
    return {
      rank,
      displayRank,
      icon: 'star',
      size: 'large',
      text: '★',
      className: 'trending-rank-star trending-rank-large'
    };
  } else if (rank <= 2) {
    // Top 3 (ranks 1-2, displayed as 2-3) get large numbers
    return {
      rank,
      displayRank,
      icon: 'number',
      size: 'large',
      text: displayRank.toString(),
      className: `trending-rank-number trending-rank-large trending-rank-${displayRank}`
    };
  } else {
    // Other ranks get smaller numbers
    return {
      rank,
      displayRank,
      icon: 'number',
      size: 'small',
      text: displayRank.toString(),
      className: `trending-rank-number trending-rank-small trending-rank-${displayRank}`
    };
  }
}

const resolvers = {
  Query: {
    // Database stats
    databaseStats: async (parent, args, context) => {
      const { db } = context;
      try {
        const [movieCount, seriesCount, animeCount, livetvCount, userCount] = await Promise.all([
          db.collection('movies').countDocuments(),
          db.collection('series').countDocuments(),
          db.collection('animes').countDocuments(),
          db.collection('livetv').countDocuments(),
          db.collection('users').countDocuments()
        ]);

        return {
          movies: movieCount,
          series: seriesCount,
          anime: animeCount,
          livetv: livetvCount,
          users: userCount,
          totalItems: movieCount + seriesCount + animeCount + livetvCount
        };
      } catch (error) {
        console.error('Database stats error:', error);
        return {
          movies: 0,
          series: 0,
          anime: 0,
          livetv: 0,
          users: 0,
          totalItems: 0
        };
      }
    },

    // Config - SIMPLE VERSION THAT COPIES TMDB LOGIC EXACTLY
    config: async (parent, args, context) => {
      const { db } = context;
      try {
        console.log('🔥 SIMPLE CONFIG RESOLVER - COPYING TMDB LOGIC EXACTLY 🔥');

        // Get config values from key-value structure - EXACT SAME WAY AS TMDB
        const [wiflixConfig, frenchAnimeConfig, witvConfig, tmdbConfig, geminiConfig, scrapingConfig] = await Promise.all([
          db.collection('config').findOne({ key: 'WIFLIX_BASE' }),
          db.collection('config').findOne({ key: 'FRENCH_ANIME_BASE' }),
          db.collection('config').findOne({ key: 'WITV_BASE' }),
          db.collection('config').findOne({ key: 'TMDB_API_KEY' }),
          db.collection('config').findOne({ key: 'GEMINI_API_KEY' }),
          db.collection('config').findOne({ key: 'SCRAPING_CONFIG' })
        ]);

        console.log('🔍 Database results:');
        console.log('TMDB found:', tmdbConfig ? 'YES' : 'NO');
        console.log('GEMINI found:', geminiConfig ? 'YES' : 'NO');
        console.log('SCRAPING found:', scrapingConfig ? 'YES' : 'NO');

        // Parse scraping config if it exists - SAME AS TMDB LOGIC
        let parsedScrapingConfig = null;
        if (scrapingConfig?.value) {
          try {
            parsedScrapingConfig = JSON.parse(scrapingConfig.value);
            console.log('✅ Scraping config parsed successfully');
          } catch (err) {
            console.error('❌ Error parsing scraping config:', err.message);
            // Fallback to default values
            parsedScrapingConfig = {
              pages: { movies: 2, series: 2, anime: 2, livetv: 4 },
              enrichment: true,
              gemini: true
            };
          }
        } else {
          // Default values if no scraping config exists
          parsedScrapingConfig = {
            pages: { movies: 2, series: 2, anime: 2, livetv: 4 },
            enrichment: true,
            gemini: true
          };
        }

        const result = {
          tmdbApiKey: tmdbConfig?.value || process.env.TMDB_API_KEY,
          wiflixBase: wiflixConfig?.value || 'flemmix.vip',
          frenchAnimeBase: frenchAnimeConfig?.value || 'french-anime.com',
          witvBase: witvConfig?.value || 'witv.skin',
          geminiApiKey: geminiConfig?.value || null,
          scrapingConfig: parsedScrapingConfig
        };

        console.log('🎯 FINAL RESULT:');
        console.log('tmdbApiKey:', result.tmdbApiKey ? 'SET' : 'NULL');
        console.log('geminiApiKey:', result.geminiApiKey ? 'SET' : 'NULL');
        console.log('scrapingConfig:', result.scrapingConfig ? 'SET' : 'NULL');

        return result;
      } catch (error) {
        console.error('❌ Config error:', error);
        return {
          tmdbApiKey: null,
          wiflixBase: 'wiflix-max.cam',
          frenchAnimeBase: 'french-anime.com',
          witvBase: 'witv.skin',
          geminiApiKey: null,
          scrapingConfig: {
            pages: { movies: 2, series: 2, anime: 2, livetv: 4 },
            enrichment: true,
            gemini: true
          }
        };
      }
    },

    // Available genres
    availableGenres: async (parent, args, context) => {
      const { db } = context;
      try {
        // Get unique genres from TMDB data
        const [movieGenres, seriesGenres, animeGenres] = await Promise.all([
          db.collection('movies').distinct('tmdb.genres'),
          db.collection('series').distinct('tmdb.genres'),
          db.collection('animes').distinct('tmdb.genres')
        ]);

        return {
          movies: movieGenres.filter(g => g).flat(),
          series: seriesGenres.filter(g => g).flat(),
          anime: animeGenres.filter(g => g).flat()
        };
      } catch (error) {
        console.error('Available genres error:', error);
        return {
          movies: [],
          series: [],
          anime: []
        };
      }
    },

    // Movies
    movies: async (parent, { sort, page = 1, limit = 20 }, context) => {
      const { db } = context;
      console.log(`🎬 MOVIES RESOLVER CALLED: sort=${sort}, page=${page}, limit=${limit}`);

      // Handle trending sort
      if (sort === 'TRENDING') {
        console.log(`🚀 MOVIES RESOLVER: Calling getTrendingItems for movies`);
        return await getTrendingItems('movie', page, limit, db);
      }

      const { skip, limit: limitNum } = getPaginationParams(page, limit);
      const sortOption = getSortOption(sort);

      try {
        const movies = await db.collection('movies')
          .find({})
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie', db));
      } catch (error) {
        console.error('Movies query error:', error);
        return [];
      }
    },

    // Latest movies
    latestMovies: async (parent, { excludeAncien = true, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        let query = {};
        if (excludeAncien) {
          query = { detailUrl: { $not: { $regex: 'film-ancien', $options: 'i' } } };
        }

        const movies = await db.collection('movies')
          .find(query)
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie', db));
      } catch (error) {
        console.error('Latest movies error:', error);
        return [];
      }
    },

    // Ancien movies
    ancienMovies: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const movies = await db.collection('movies')
          .find({ detailUrl: { $regex: 'film-ancien', $options: 'i' } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie', db));
      } catch (error) {
        console.error('Ancien movies error:', error);
        return [];
      }
    },

    // Series
    series: async (parent, { sort, page = 1, limit = 20 }, context) => {
      const { db } = context;
      console.log(`📺 SERIES RESOLVER CALLED: sort=${sort}, page=${page}, limit=${limit}`);

      // Handle trending sort
      if (sort === 'TRENDING') {
        console.log(`🚀 SERIES RESOLVER: Calling getTrendingItems for series`);
        return await getTrendingItems('tv', page, limit, db);
      }

      const { skip, limit: limitNum } = getPaginationParams(page, limit);
      const sortOption = getSortOption(sort);

      try {
        const series = await db.collection('series')
          .find({})
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return series.map(item => formatDocument(item, 'Series', db));
      } catch (error) {
        console.error('Series query error:', error);
        return [];
      }
    },

    // Latest series
    latestSeries: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const series = await db.collection('series')
          .find({})
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return series.map(item => formatDocument(item, 'Series', db));
      } catch (error) {
        console.error('Latest series error:', error);
        return [];
      }
    },

    // Series by genre
    seriesByGenre: async (parent, { genre, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const series = await db.collection('series')
          .find({ 'tmdb.genres': { $in: [genre] } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return series.map(item => formatDocument(item, 'Series', db));
      } catch (error) {
        console.error('Series by genre error:', error);
        return [];
      }
    },

    // Anime
    anime: async (parent, { sort, page = 1, limit = 20 }, context) => {
      const { db } = context;
      console.log(`🎌 ANIME RESOLVER CALLED: sort=${sort}, page=${page}, limit=${limit}`);

      // Handle trending sort
      if (sort === 'TRENDING') {
        console.log(`🚀 ANIME RESOLVER: Calling getTrendingItems for anime`);
        return await getTrendingItems('tv', page, limit, db);
      }

      const { skip, limit: limitNum } = getPaginationParams(page, limit);
      const sortOption = getSortOption(sort);

      try {
        const animes = await db.collection('animes')
          .find({})
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime', db));
      } catch (error) {
        console.error('Anime query error:', error);
        return [];
      }
    },

    // Latest anime
    latestAnime: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const animes = await db.collection('animes')
          .find({})
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime', db));
      } catch (error) {
        console.error('Latest anime error:', error);
        return [];
      }
    },

    // Anime movies
    animeMovies: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const animes = await db.collection('animes')
          .find({ detailUrlPath: { $regex: 'films-vf-vostfr', $options: 'i' } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime', db));
      } catch (error) {
        console.error('Anime movies error:', error);
        return [];
      }
    },

    // Movies by genre
    moviesByGenre: async (parent, { genre, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const movies = await db.collection('movies')
          .find({ 'tmdb.genres': { $in: [genre] } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return movies.map(movie => formatDocument(movie, 'Movie', db));
      } catch (error) {
        console.error('Movies by genre error:', error);
        return [];
      }
    },

    // Anime by genre
    animeByGenre: async (parent, { genre, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const animes = await db.collection('animes')
          .find({
            $or: [
              { 'tmdb.genres': { $regex: new RegExp(genre, 'i') } },
              { 'jikan.genres.name': { $regex: new RegExp(genre, 'i') } }
            ]
          })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return animes.map(item => formatDocument(item, 'Anime', db));
      } catch (error) {
        console.error('Anime by genre error:', error);
        return [];
      }
    },

    // Validate admin token
    validateAdminToken: async (parent, { token }, context) => {
      const { db } = context;
      try {
        const session = await db.collection('admin_sessions').findOne({
          type: 'session',
          token,
          expiresAt: { $gt: new Date() }
        });
        
        return { isValid: !!session };
      } catch (error) {
        console.error('Validate admin token error:', error);
        return { isValid: false };
      }
    },

    // Content overview
    contentOverview: async (parent, args, context) => {
      const { db } = context;
      try {
        const recentlyAdded = await db.collection('movies').countDocuments({
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        });
        
        return {
          recentlyAdded,
          trending: 0,
          mostWatched: 0,
          totalViews: 0
        };
      } catch (error) {
        console.error('Content overview error:', error);
        return {
          recentlyAdded: 0,
          trending: 0,
          mostWatched: 0,
          totalViews: 0
        };
      }
    },

    // Display settings
    displaySettings: async (parent, { adminToken }, context) => {
      const { db } = context;
      try {
        const config = await db.collection('config').findOne({});
        return {
          gridItemsEnabled: config?.gridItemsEnabled !== false
        };
      } catch (error) {
        console.error('Display settings error:', error);
        return {
          gridItemsEnabled: true
        };
      }
    },

    // Search functionality
    search: async (parent, { query, page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const searchRegex = new RegExp(query, 'i');
        const searchQuery = {
          $or: [
            { title: searchRegex },
            { cleanedTitle: searchRegex },
            { 'tmdb.title': searchRegex }
          ]
        };

        const [movies, series, animes, livetv] = await Promise.all([
          db.collection('movies').find(searchQuery)
            .project({
              _id: 1,
              title: 1,
              displayTitle: 1,
              thumbnail: 1,
              image: 1,
              detailUrl: 1,
              detailUrlPath: 1,
              streamingUrls: 1,
              metadata: 1,
              tmdb: 1, // Select entire tmdb object
              jikan: 1 // Select entire jikan object
            })
            .skip(skip).limit(limitNum).toArray(),
          db.collection('series').find(searchQuery)
            .project({
              _id: 1,
              title: 1,
              displayTitle: 1,
              thumbnail: 1,
              image: 1,
              season: 1,
              episodes: 1,
              detailUrl: 1,
              detailUrlPath: 1,
              streamingUrls: 1,
              metadata: 1,
              tmdb: 1, // Select entire tmdb object
              jikan: 1 // Select entire jikan object
            })
            .skip(skip).limit(limitNum).toArray(),
          db.collection('animes').find(searchQuery)
            .project({
              _id: 1,
              title: 1,
              displayTitle: 1,
              thumbnail: 1,
              image: 1,
              season: 1,
              animeLanguage: 1,
              episodes: 1,
              detailUrl: 1,
              detailUrlPath: 1,
              streamingUrls: 1,
              metadata: 1,
              tmdb: 1, // Select entire tmdb object
              jikan: 1 // Select entire jikan object
            })
            .skip(skip).limit(limitNum).toArray(),
          db.collection('livetv').find(searchQuery)
            .project({
              _id: 1,
              title: 1,
              displayTitle: 1,
              thumbnail: 1,
              image: 1,
              detailUrl: 1,
              detailUrlPath: 1,
              streamingUrls: 1,
              metadata: 1,
              category: 1,
              language: 1,
              country: 1,
              tmdb: 1, // Include for consistency, even if not primary source
              jikan: 1 // Include for consistency
            })
            .skip(skip).limit(limitNum).toArray()
        ]);

        const items = await Promise.all([
          ...movies.map(m => formatDocument(m, 'Movie', db)),
          ...series.map(s => formatDocument(s, 'Series', db)),
          ...animes.map(a => formatDocument(a, 'Anime', db)),
          ...livetv.map(l => formatDocument(l, 'LiveTV', db))
        ]);

        return { items };
      } catch (error) {
        console.error('Search error:', error);
        return { items: [] };
      }
    },

    // Item by ID
    item: async (parent, { id, type }, context) => {
      const { db } = context;

      try {
        if (!ObjectId.isValid(id)) {
          throw new Error('Invalid ID format');
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          throw new Error('Invalid item type');
        }

        const item = await db.collection(collection).findOne({
          _id: new ObjectId(id)
        });

        if (!item) {
          return null;
        }

        return formatDocument(item, type.charAt(0) + type.slice(1).toLowerCase(), db);
      } catch (error) {
        console.error('Item by ID error:', error);
        return null;
      }
    },

    // Enhanced Stream URL resolver with source fetching
    stream: async (parent, { itemId, type, streamId }, context) => {
      const { db } = context;
      const logger = context.logger || console;

      try {
        // Validate ObjectIds
        if (!ObjectId.isValid(itemId)) {
          throw new Error('Invalid item ID format');
        }
        if (!ObjectId.isValid(streamId)) {
          throw new Error('Invalid stream ID format');
        }

        logger.info(`GraphQL Stream: type=${type}, itemId=${itemId}, streamId=${streamId}`);

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          throw new Error('Unsupported item type');
        }

        const item = await db.collection(collection).findOne({
          _id: new ObjectId(itemId)
        });

        if (!item) {
          throw new Error('Item not found');
        }

        // Find the specific streaming URL
        let streamingUrl = null;
        let streamParentArrayPath = null;
        let specificEpisodeId = null;

        // Search in direct streaming URLs
        if (item.streamingUrls) {
          streamingUrl = item.streamingUrls.find(url =>
            url._id?.toString() === streamId || url.id === streamId
          );
          if (streamingUrl) {
            streamParentArrayPath = 'streamingUrls';
          }
        }

        // Search in episodes
        if (!streamingUrl && item.episodes) {
          for (const episode of item.episodes) {
            if (episode.streamingUrls) {
              const found = episode.streamingUrls.find(url =>
                url._id?.toString() === streamId || url.id === streamId
              );
              if (found) {
                streamingUrl = found;
                streamParentArrayPath = `episodes.${item.episodes.indexOf(episode)}.streamingUrls`;
                specificEpisodeId = episode._id;
                break;
              }
            }
          }
        }

        if (!streamingUrl) {
          throw new Error('Stream not found');
        }

        // Get current stream data
        let sourceStreamUrl = streamingUrl.sourceStreamUrl;
        let size = streamingUrl.size;
        let streamType = streamingUrl.type;
        let method = streamingUrl.method;
        const lastChecked = streamingUrl.lastChecked ? new Date(streamingUrl.lastChecked).getTime() : 0;
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

        // Fetch/Re-fetch if no source URL or cache is older than 5 minutes
        if (!sourceStreamUrl || lastChecked < fiveMinutesAgo) {
          logger.info(`Fetching/Re-fetching source URL for stream ${streamId} (Provider: ${streamingUrl.provider})`);

          try {
            // Import the source fetcher
            logger.info(`[COMPATIBLE DEBUG] Importing source fetcher...`);
            const { fetchSourceStreamUrl } = require('../utils/sourceStreamFetcher');
            logger.info(`[COMPATIBLE DEBUG] Source fetcher imported, type: ${typeof fetchSourceStreamUrl}`);

            logger.info(`[COMPATIBLE DEBUG] About to call fetchSourceStreamUrl`, {
              url: streamingUrl.url,
              provider: streamingUrl.provider,
              streamId: streamId
            });

            const result = await fetchSourceStreamUrl(streamingUrl.url, streamingUrl.provider);

            logger.info(`[COMPATIBLE DEBUG] fetchSourceStreamUrl completed`, {
              result: result,
              hasResult: !!result,
              url: result?.url,
              method: result?.method,
              streamId: streamId
            });

            if (result && result.url) {
              logger.info(`Fetched source URL: ${result.url}`, {
                streamId,
                provider: streamingUrl.provider,
                method: result.method,
                size: result.size,
                type: result.type
              });

              // Update the stream in the database
              sourceStreamUrl = result.url;
              size = result.size;
              streamType = result.type;
              method = result.method;

              const updateData = {
                sourceStreamUrl: result.url,
                size: result.size,
                type: result.type,
                method: result.method,
                lastChecked: new Date()
              };

              // Update the specific stream in the database
              const updateQuery = { _id: new ObjectId(itemId) };
              const updateOperation = {};

              if (streamParentArrayPath === 'streamingUrls') {
                // Update direct streaming URL
                updateOperation.$set = {};
                Object.keys(updateData).forEach(key => {
                  updateOperation.$set[`streamingUrls.$[stream].${key}`] = updateData[key];
                });
                updateOperation.arrayFilters = [{ 'stream._id': new ObjectId(streamId) }];
              } else {
                // Update episode streaming URL
                updateOperation.$set = {};
                Object.keys(updateData).forEach(key => {
                  updateOperation.$set[`episodes.$[episode].streamingUrls.$[stream].${key}`] = updateData[key];
                });
                updateOperation.arrayFilters = [
                  { 'episode._id': specificEpisodeId },
                  { 'stream._id': new ObjectId(streamId) }
                ];
              }

              await db.collection(collection).updateOne(updateQuery, updateOperation);
              logger.info(`Updated stream ${streamId} in database with new source URL`);
            } else {
              logger.warn(`Failed to fetch source URL for stream ${streamId}`, {
                provider: streamingUrl.provider,
                originalUrl: streamingUrl.url
              });
            }
          } catch (error) {
            logger.error(`Error fetching source URL for stream ${streamId}:`, {
              error: error.message,
              stack: error.stack,
              provider: streamingUrl.provider,
              originalUrl: streamingUrl.url,
              code: error.code,
              status: error.response?.status,
              statusText: error.response?.statusText
            });
          }
        }

        return {
          sourceStreamUrl,
          size,
          type: streamType,
          method
        };
      } catch (error) {
        console.error('Stream error:', error);
        throw error;
      }
    },

    // Play URL (simplified version of stream)
    play: async (parent, { id, type, ep, lang }, context) => {
      const streamResult = await resolvers.Query.stream(parent, { id, type, ep, lang }, context);
      return {
        url: streamResult?.url || ''
      };
    },

    // Related seasons
    relatedSeasons: async (parent, { tmdbId, currentItemId }, context) => {
      const { db } = context;

      try {
        // Search for all series and anime with the same TMDB ID, excluding the current item
        const [relatedSeries, relatedAnime] = await Promise.all([
          db.collection('series').find({
            'tmdb.id': tmdbId,
            _id: { $ne: new ObjectId(currentItemId) }
          }).sort({ season: 1 }).toArray(),
          db.collection('animes').find({
            'tmdb.id': tmdbId,
            _id: { $ne: new ObjectId(currentItemId) }
          }).sort({ season: 1 }).toArray()
        ]);

        // Combine and format results
        const allRelated = [...relatedSeries, ...relatedAnime];

        // Sort by season number (convert to number for proper sorting)
        allRelated.sort((a, b) => {
          const seasonA = parseInt(a.season || '1');
          const seasonB = parseInt(b.season || '1');
          return seasonA - seasonB;
        });

        // Format documents and add __typename
        return allRelated.map(item => {
          // Manually format the document to ensure proper field mapping
          const formatted = {
            id: item._id.toString(),
            title: item.title || item.displayTitle || 'Unknown Title',
            displayTitle: item.displayTitle || item.title,
            season: item.season ? parseInt(item.season) : 1,
            thumbnail: item.thumbnail || item.image || null,
            image: item.image || item.thumbnail || null,
            episodes: item.episodes || [],
            tmdb: item.tmdb || null,
            createdAt: item.createdAt || null,
            updatedAt: item.updatedAt || null
          };

          // Determine __typename based on collection and properties
          let typename = 'Movie'; // default

          if (item.animeLanguage !== undefined || item.jikan) {
            typename = 'Anime';
          } else if (item.episodes && Array.isArray(item.episodes) && item.episodes.length > 0) {
            typename = 'Series';
          } else if (item.detailUrl && item.detailUrl.includes('witv.skin')) {
            typename = 'LiveTV';
          }

          // Explicitly set __typename
          formatted.__typename = typename;

          return formatted;
        });
      } catch (error) {
        console.error('Related seasons error:', error);
        return [];
      }
    },

    // Duplicate detail URL paths
    duplicateDetailUrlPaths: async (parent, args, context) => {
      const { db } = context;

      try {
        const duplicates = await db.collection('movies').aggregate([
          { $group: { _id: '$detailUrl', count: { $sum: 1 }, items: { $push: '$$ROOT' } } },
          { $match: { count: { $gt: 1 } } },
          { $limit: 10 }
        ]).toArray();

        return duplicates.map(dup => ({
          detailUrl: dup._id,
          count: dup.count,
          items: dup.items.map(item => formatDocument(item, 'Movie', db))
        }));
      } catch (error) {
        console.error('Duplicate detail URLs error:', error);
        return [];
      }
    },

    // LiveTV
    liveTV: async (parent, { page = 1, limit = 20 }, context) => {
      const { db } = context;
      const { skip, limit: limitNum } = getPaginationParams(page, limit);

      try {
        const channels = await db.collection('livetv')
          .find({})
          .sort({ title: 1 })
          .skip(skip)
          .limit(limitNum)
          .toArray();

        return channels.map(channel => formatDocument(channel, 'LiveTV'));
      } catch (error) {
        console.error('LiveTV error:', error);
        return [];
      }
    }
  },

  // Interface resolver
  Item: {
    __resolveType(obj) {
      if (obj.__typename) {
        return obj.__typename;
      }
      
      // Type resolution logic
      if (obj.animeLanguage || obj.jikan) {
        return 'Anime';
      }
      if (obj.episodes && obj.episodes.length > 0) {
        return 'Series';
      }
      if (obj.detailUrl && obj.detailUrl.includes('witv.skin')) {
        return 'LiveTV';
      }
      
      return 'Movie';
    }
  },

  // Mutation resolvers
  Mutation: {
    adminLogin: async (parent, { adminKey }, context) => {
      const { db } = context;
      try {
        const expectedKey = process.env.ADMIN_KEY;
        if (!expectedKey || adminKey !== expectedKey) {
          return {
            success: false,
            message: 'Invalid admin key'
          };
        }

        const token = Buffer.from(`${adminKey}:${Date.now()}`).toString('base64');

        // Store token in a separate sessions collection to avoid conflicts with existing admin schema
        await db.collection('admin_sessions').updateOne(
          { type: 'session' },
          {
            $set: {
              token,
              createdAt: new Date(),
              expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
            }
          },
          { upsert: true }
        );

        return {
          success: true,
          token,
          message: 'Login successful'
        };
      } catch (error) {
        console.error('Admin login error:', error);
        return {
          success: false,
          message: 'Login failed'
        };
      }
    },

    deleteItem: async (parent, { id, type, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin_sessions').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token'
          };
        }

        if (!ObjectId.isValid(id)) {
          return {
            success: false,
            message: 'Invalid ID format'
          };
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          return {
            success: false,
            message: 'Invalid item type'
          };
        }

        const result = await db.collection(collection).deleteOne({
          _id: new ObjectId(id)
        });

        if (result.deletedCount === 0) {
          return {
            success: false,
            message: 'Item not found'
          };
        }

        return {
          success: true,
          message: 'Item deleted successfully'
        };
      } catch (error) {
        console.error('Delete item error:', error);
        return {
          success: false,
          message: 'Failed to delete item'
        };
      }
    },

    updateItem: async (parent, { id, type, input, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin_sessions').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token',
            item: null
          };
        }

        if (!ObjectId.isValid(id)) {
          return {
            success: false,
            message: 'Invalid ID format',
            item: null
          };
        }

        const collectionMap = {
          MOVIE: 'movies',
          SERIES: 'series',
          ANIME: 'animes',
          LIVETV: 'livetv'
        };

        const collection = collectionMap[type];
        if (!collection) {
          return {
            success: false,
            message: 'Invalid item type',
            item: null
          };
        }

        // Update the item
        const updateResult = await db.collection(collection).updateOne(
          { _id: new ObjectId(id) },
          { $set: { ...input, updatedAt: new Date() } }
        );

        console.log('Update result:', updateResult);
        console.log('Matched count:', updateResult.matchedCount);
        console.log('Modified count:', updateResult.modifiedCount);

        if (updateResult.matchedCount === 0) {
          console.log('No documents matched the query');
          return {
            success: false,
            message: 'Item not found',
            item: null
          };
        }

        // Get the updated item
        const updatedItem = await db.collection(collection).findOne({ _id: new ObjectId(id) });

        return {
          success: true,
          message: 'Item updated successfully',
          item: updatedItem
        };
      } catch (error) {
        console.error('Update item error:', error);
        return {
          success: false,
          message: 'Failed to update item',
          item: null
        };
      }
    },

    scrapeItem: async (parent, { id, type, adminToken }, context) => {
      return {
        success: false,
        message: 'Scraping functionality not implemented in Fastify migration'
      };
    },

    updateBaseUrl: async (parent, { key, value, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin_sessions').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token',
            key: null,
            value: null
          };
        }

        // Validate key - allow all config keys
        const validKeys = [
          'WIFLIX_BASE', 'FRENCH_ANIME_BASE', 'WITV_BASE',
          'TMDB_API_KEY', 'GEMINI_API_KEY', 'SCRAPING_CONFIG'
        ];

        if (!validKeys.includes(key)) {
          return {
            success: false,
            message: `Invalid key. Must be one of: ${validKeys.join(', ')}`,
            key,
            value: null
          };
        }

        // Update config in database using key-value structure
        await db.collection('config').updateOne(
          { key },
          {
            $set: {
              key,
              value,
              lastUpdated: new Date()
            }
          },
          { upsert: true }
        );

        console.log(`✅ Updated config ${key} to ${value}`);

        return {
          success: true,
          message: 'Configuration updated successfully',
          key,
          value
        };
      } catch (error) {
        console.error('Update base URL error:', error);
        return {
          success: false,
          message: 'Failed to update configuration',
          key: null,
          value: null
        };
      }
    },

    scrapeUrlManually: async (parent, { url, type, adminToken }, context) => {
      return {
        success: false,
        message: 'Manual scraping functionality not implemented in Fastify migration'
      };
    },

    updateDisplaySettings: async (parent, { gridItemsEnabled, adminToken }, context) => {
      const { db } = context;

      try {
        // Validate admin token
        const session = await db.collection('admin_sessions').findOne({
          type: 'session',
          token: adminToken,
          expiresAt: { $gt: new Date() }
        });

        if (!session) {
          return {
            success: false,
            message: 'Invalid or expired admin token'
          };
        }

        await db.collection('config').updateOne(
          {},
          {
            $set: {
              gridItemsEnabled,
              updatedAt: new Date()
            }
          },
          { upsert: true }
        );

        return {
          success: true,
          message: 'Display settings updated successfully'
        };
      } catch (error) {
        console.error('Update display settings error:', error);
        return {
          success: false,
          message: 'Failed to update display settings'
        };
      }
    }
  },

  // Type resolvers for trending rank icons
  Movie: {
    trendingRankIcon: (parent) => {
      return getTrendingRankIcon(parent.trendingRank);
    }
  },

  Series: {
    trendingRankIcon: (parent) => {
      return getTrendingRankIcon(parent.trendingRank);
    }
  },

  Anime: {
    trendingRankIcon: (parent) => {
      return getTrendingRankIcon(parent.trendingRank);
    }
  }
};

module.exports = resolvers;
