// File: src/graphql/fastifyResolvers.js
// Fastify + Mercurius GraphQL Resolvers
// Optimized for performance with DataLoader and caching

const { ObjectId } = require('mongodb');
const FastifyDataLoaders = require('./dataLoaders');

class FastifyResolvers {
  constructor() {
    this.resolvers = this.createResolvers();
  }

  createResolvers() {
    return {
      Query: {
        // Item resolver with DataLoader optimization
        item: {
          async handler(parent, args, context, info) {
            const { id, type } = args;
            const { db, dataLoaders } = context;

            if (!ObjectId.isValid(id)) {
              throw new Error('Invalid ID format');
            }

            const collectionMap = {
              MOVIE: 'movies',
              SERIES: 'series', 
              ANIME: 'animes',
              LIVETV: 'livetv'
            };

            const collection = collectionMap[type];
            if (!collection) {
              throw new Error('Unsupported item type');
            }

            const loaderName = `${collection.slice(0, -1)}ById`;
            const item = await dataLoaders[loaderName].load(id);
            if (!item) {
              throw new Error('Item not found');
            }

            return {
              ...item,
              __typename: this.getTypeName(collection)
            };
          },
          cache: {
            ttl: 300, // 5 minutes
            key: (parent, args) => `item:${args.type}:${args.id}`
          }
        },

        // Optimized search with DataLoader
        search: {
          async handler(parent, args, context, info) {
            const { query, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const searchQuery = {
              query,
              collections: ['movies', 'series', 'animes', 'livetv'],
              limit
            };

            const items = await dataLoaders.searchResults.load(searchQuery);
            
            return { items };
          },
          cache: {
            ttl: 180, // 3 minutes
            key: (parent, args) => `search:${args.query}:${args.page}:${args.limit}`
          }
        },

        // Movies with trending optimization
        movies: {
          async handler(parent, args, context, info) {
            const { sort, page = 1, limit = 20 } = args;
            const { db, dataLoaders } = context;

            if (sort === 'TRENDING') {
              return await this.getTrendingItems('movie', page, limit, dataLoaders);
            }

            const skip = (page - 1) * limit;
            const sortOption = this.getSortOption(sort);

            return await db.collection('movies')
              .find({})
              .sort(sortOption)
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 300,
            key: (parent, args) => `movies:${args.sort}:${args.page}:${args.limit}`
          }
        },

        // Series resolver
        series: {
          async handler(parent, args, context, info) {
            const { sort, page = 1, limit = 20 } = args;
            const { db, dataLoaders } = context;

            if (sort === 'TRENDING') {
              return await this.getTrendingItems('tv', page, limit, dataLoaders);
            }

            const skip = (page - 1) * limit;
            const sortOption = this.getSortOption(sort);

            return await db.collection('series')
              .find({})
              .sort(sortOption)
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 300,
            key: (parent, args) => `series:${args.sort}:${args.page}:${args.limit}`
          }
        },

        // Anime resolver
        anime: {
          async handler(parent, args, context, info) {
            const { sort, page = 1, limit = 20 } = args;
            const { db, dataLoaders } = context;

            if (sort === 'TRENDING') {
              return await this.getTrendingItems('tv', page, limit, dataLoaders);
            }

            const skip = (page - 1) * limit;
            const sortOption = this.getAnimeSortOption(sort);

            return await db.collection('animes')
              .find({})
              .sort(sortOption)
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 300,
            key: (parent, args) => `anime:${args.sort}:${args.page}:${args.limit}`
          }
        },

        // LiveTV resolver
        liveTV: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;

            const channels = await db.collection('livetv')
              .find({})
              .sort({ title: 1 })
              .skip(skip)
              .limit(limit)
              .toArray();

            return channels.map(channel => ({
              ...channel,
              __typename: 'LiveTV'
            }));
          },
          cache: {
            ttl: 600, // 10 minutes
            key: (parent, args) => `livetv:${args.page}:${args.limit}`
          }
        },

        // Latest movies with exclusion logic
        latestMovies: {
          async handler(parent, args, context, info) {
            const { excludeAncien = true, page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;
            let query = {};

            if (excludeAncien) {
              query = { 
                detailUrl: { 
                  $not: { $regex: 'film-ancien', $options: 'i' } 
                } 
              };
            }

            return await db.collection('movies')
              .find(query)
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 600, // 10 minutes
            key: (parent, args) => `latestMovies:${args.excludeAncien}:${args.page}:${args.limit}`
          }
        },

        // Ancien movies
        ancienMovies: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;
            const query = { 
              detailUrl: { $regex: 'film-ancien', $options: 'i' } 
            };

            return await db.collection('movies')
              .find(query)
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 600,
            key: (parent, args) => `ancienMovies:${args.page}:${args.limit}`
          }
        },

        // Latest series
        latestSeries: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;

            return await db.collection('series')
              .find({})
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 600,
            key: (parent, args) => `latestSeries:${args.page}:${args.limit}`
          }
        },

        // Latest anime
        latestAnime: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;

            return await db.collection('animes')
              .find({})
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 600,
            key: (parent, args) => `latestAnime:${args.page}:${args.limit}`
          }
        },

        // Genre-based queries
        moviesByGenre: {
          async handler(parent, args, context, info) {
            const { genre, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const genreQuery = {
              collection: 'movies',
              genre,
              limit
            };

            return await dataLoaders.itemsByGenre.load(genreQuery);
          },
          cache: {
            ttl: 900, // 15 minutes
            key: (parent, args) => `moviesByGenre:${args.genre}:${args.page}:${args.limit}`
          }
        },

        seriesByGenre: {
          async handler(parent, args, context, info) {
            const { genre, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const genreQuery = {
              collection: 'series',
              genre,
              limit
            };

            return await dataLoaders.itemsByGenre.load(genreQuery);
          },
          cache: {
            ttl: 900,
            key: (parent, args) => `seriesByGenre:${args.genre}:${args.page}:${args.limit}`
          }
        },

        animeByGenre: {
          async handler(parent, args, context, info) {
            const { genre, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const genreQuery = {
              collection: 'animes',
              genre,
              limit
            };

            return await dataLoaders.itemsByGenre.load(genreQuery);
          },
          cache: {
            ttl: 900,
            key: (parent, args) => `animeByGenre:${args.genre}:${args.page}:${args.limit}`
          }
        },

        // Available genres
        availableGenres: {
          async handler(parent, args, context, info) {
            const { db } = context;
            const dbService = context.dbService;

            return await dbService.getAvailableGenres();
          },
          cache: {
            ttl: 3600, // 1 hour
            key: () => 'availableGenres'
          }
        },

        // Related seasons
        relatedSeasons: {
          async handler(parent, args, context, info) {
            const { tmdbId, currentItemId } = args;
            const { dataLoaders } = context;

            const relatedItems = await dataLoaders.relatedSeasons.load(tmdbId);

            // Filter out the current item
            return relatedItems.filter(item =>
              item._id.toString() !== currentItemId
            );
          },
          cache: {
            ttl: 1800, // 30 minutes
            key: (parent, args) => `relatedSeasons:${args.tmdbId}:${args.currentItemId}`
          }
        },

        // Database stats for admin
        databaseStats: {
          async handler(parent, args, context, info) {
            const { dbService } = context;
            return await dbService.getDatabaseStats();
          },
          cache: {
            ttl: 300, // 5 minutes
            key: () => 'databaseStats'
          }
        },

        // Stream URL resolver
        stream: {
          async handler(parent, args, context, info) {
            const { itemId, type, streamId } = args;
            const { db } = context;

            const collectionMap = {
              MOVIE: 'movies',
              SERIES: 'series',
              ANIME: 'animes',
              LIVETV: 'livetv'
            };

            const collection = collectionMap[type];
            if (!collection) {
              throw new Error('Unsupported item type');
            }

            const item = await db.collection(collection).findOne({
              _id: new ObjectId(itemId)
            });

            if (!item) {
              throw new Error('Item not found');
            }

            // Find the specific streaming URL
            let streamingUrl = null;

            if (item.streamingUrls) {
              streamingUrl = item.streamingUrls.find(url =>
                url._id?.toString() === streamId || url.id === streamId
              );
            }

            if (item.episodes) {
              for (const episode of item.episodes) {
                if (episode.streamingUrls) {
                  const found = episode.streamingUrls.find(url =>
                    url._id?.toString() === streamId || url.id === streamId
                  );
                  if (found) {
                    streamingUrl = found;
                    break;
                  }
                }
              }
            }

            if (!streamingUrl) {
              throw new Error('Stream not found');
            }

            return {
              sourceStreamUrl: streamingUrl.sourceStreamUrl,
              size: streamingUrl.size,
              type: streamingUrl.type,
              method: streamingUrl.method
            };
          }
        },

        // Play URL resolver
        play: {
          async handler(parent, args, context, info) {
            const { type, id, ep, lang } = args;
            const { db } = context;

            const collectionMap = {
              MOVIE: 'movies',
              SERIES: 'series',
              ANIME: 'animes',
              LIVETV: 'livetv'
            };

            const collection = collectionMap[type];
            if (!collection) {
              throw new Error('Unsupported item type');
            }

            const item = await db.collection(collection).findOne({
              _id: new ObjectId(id)
            });

            if (!item) {
              throw new Error('Item not found');
            }

            let streamingUrl = null;

            // For movies and LiveTV, get direct streaming URL
            if (type === 'MOVIE' || type === 'LIVETV') {
              if (item.streamingUrls && item.streamingUrls.length > 0) {
                // Filter by language if specified
                const filteredUrls = lang ?
                  item.streamingUrls.filter(url => url.language === lang) :
                  item.streamingUrls;

                streamingUrl = filteredUrls[0];
              }
            } else {
              // For series and anime, find the specific episode
              if (item.episodes && ep) {
                const episode = item.episodes.find(e =>
                  e.episodeNumber === ep || e.episodeNumber === ep.toString()
                );

                if (episode && episode.streamingUrls && episode.streamingUrls.length > 0) {
                  const filteredUrls = lang ?
                    episode.streamingUrls.filter(url => url.language === lang) :
                    episode.streamingUrls;

                  streamingUrl = filteredUrls[0];
                }
              }
            }

            if (!streamingUrl) {
              throw new Error('No streaming URL found');
            }

            return {
              url: streamingUrl.url || streamingUrl.sourceStreamUrl
            };
          }
        },

        // Config resolver
        config: {
          async handler(parent, args, context, info) {
            const { db } = context;

            const config = await db.collection('config').findOne({});

            return {
              tmdbApiKey: config?.tmdbApiKey || null,
              wiflixBase: config?.wiflixBase || 'wiflix-max.cam',
              frenchAnimeBase: config?.frenchAnimeBase || 'french-anime.com',
              witvBase: config?.witvBase || 'witv.skin'
            };
          },
          cache: {
            ttl: 1800, // 30 minutes
            key: () => 'config'
          }
        }
      },

      // Interface resolvers
      Item: {
        __resolveType(obj, context, info) {
          if (obj.__typename) {
            return obj.__typename;
          }

          // Fallback type resolution based on object properties
          if (obj.animeLanguage || obj.jikan) {
            return 'Anime';
          }
          if (obj.episodes && obj.episodes.length > 0) {
            return 'Series';
          }
          if (obj.streamingUrls && obj.streamingUrls.length > 0) {
            // Could be Movie or LiveTV, check for other properties
            if (obj.cleanedTitle && !obj.season) {
              return 'Movie';
            }
            return 'LiveTV';
          }

          // Default fallback
          return 'Movie';
        }
      },

      // Mutation resolvers
      Mutation: {
        // Admin login
        adminLogin: {
          async handler(parent, args, context, info) {
            const { adminKey } = args;
            const { db } = context;

            const expectedKey = process.env.ADMIN_KEY;
            if (!expectedKey || adminKey !== expectedKey) {
              return {
                success: false,
                message: 'Invalid admin key'
              };
            }

            // Generate a simple token (in production, use JWT)
            const token = Buffer.from(`${adminKey}:${Date.now()}`).toString('base64');

            // Store token in database with expiration
            await db.collection('admin').updateOne(
              { type: 'session' },
              {
                $set: {
                  token,
                  createdAt: new Date(),
                  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
                }
              },
              { upsert: true }
            );

            return {
              success: true,
              token,
              message: 'Login successful'
            };
          }
        },

        // Validate admin token
        validateAdminToken: {
          async handler(parent, args, context, info) {
            const { token } = args;
            const { db } = context;

            const session = await db.collection('admin').findOne({
              type: 'session',
              token,
              expiresAt: { $gt: new Date() }
            });

            return {
              isValid: !!session
            };
          }
        }
      }
    };
  }

  // Helper methods
  async getTrendingItems(mediaType, page, limit, dataLoaders) {
    const trendingItems = await dataLoaders.trendingByMediaType.load(mediaType);
    
    if (trendingItems.length === 0) {
      // Fallback to latest items
      const skip = (page - 1) * limit;
      const collection = mediaType === 'movie' ? 'movies' : 
                        mediaType === 'tv' ? 'series' : 'animes';
      
      return await dataLoaders.db.collection(collection)
        .find({})
        .sort({ updatedAt: -1 })
        .skip(skip)
        .limit(limit)
        .toArray();
    }

    const tmdbIds = trendingItems.slice((page - 1) * limit, page * limit)
                                .map(item => item.tmdbId);
    
    const collectionName = mediaType === 'movie' ? 'movies' : 'series';
    const items = await dataLoaders[`${collectionName.slice(0, -1)}ByTmdbId`]
                                  .loadMany(tmdbIds);
    
    return items.flat().filter(Boolean);
  }

  getSortOption(sort) {
    switch (sort) {
      case 'LATEST':
        return { updatedAt: -1 };
      case 'ALPHA':
        return { title: 1 };
      case 'RELEASE':
        return { 'tmdb.release_date': -1, createdAt: -1 };
      default:
        return { updatedAt: -1 };
    }
  }

  getAnimeSortOption(sort) {
    switch (sort) {
      case 'LATEST':
        return { updatedAt: -1 };
      case 'ALPHA':
        return { title: 1 };
      case 'RELEASE':
        return { 
          'jikan.year': -1, 
          'tmdb.release_date': -1, 
          createdAt: -1 
        };
      default:
        return { updatedAt: -1 };
    }
  }

  getTypeName(collection) {
    const typeMap = {
      movies: 'Movie',
      series: 'Series',
      animes: 'Anime',
      livetv: 'LiveTV'
    };
    return typeMap[collection] || 'Unknown';
  }
}

module.exports = FastifyResolvers;
