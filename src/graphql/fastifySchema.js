// File: src/graphql/fastifySchema.js
// GraphQL Schema for Fastify + Mercurius
// Optimized for performance with JIT compilation

const fs = require('fs');
const path = require('path');

// Read the GraphQL schema file
const schemaPath = path.join(__dirname, '../../schema.graphql');
let baseTypeDefs;

try {
  baseTypeDefs = fs.readFileSync(schemaPath, 'utf8');
} catch (error) {
  console.error('Failed to read schema.graphql:', error);
  throw error;
}

// Schema validation and optimization for Mercurius
const optimizedTypeDefs = `
  # Optimized GraphQL Schema for Fastify + Mercurius
  ${baseTypeDefs}

  # Additional types for subscriptions and real-time features
  type Subscription {
    # Real-time scraping progress
    scrapeProgress(logId: String!): ScrapeProgressUpdate!

    # New content notifications
    newContent(mediaType: ItemType): ContentUpdate!

    # Admin notifications
    adminNotification(adminToken: String!): AdminNotification!
  }

  type ScrapeProgressUpdate {
    logId: String!
    progress: Float!
    message: String!
    status: String!
    timestamp: String!
  }

  type ContentUpdate {
    id: ID!
    type: ItemType!
    title: String!
    action: String! # ADDED, UPDATED, DELETED
    timestamp: String!
  }

  type AdminNotification {
    type: String!
    message: String!
    data: String # JSON string for additional data
    timestamp: String!
  }
`;

// Schema directives for caching and performance
const schemaDirectives = {
  // Cache directive for resolver-level caching
  cache: {
    typeDefs: `
      directive @cache(
        ttl: Int = 300
        key: String
        tags: [String!]
      ) on FIELD_DEFINITION
    `,
    transformer: (schema) => {
      // Transform schema to add caching metadata
      return schema;
    }
  },
  
  // Rate limiting directive
  rateLimit: {
    typeDefs: `
      directive @rateLimit(
        max: Int = 100
        window: String = "1m"
        message: String = "Rate limit exceeded"
      ) on FIELD_DEFINITION
    `,
    transformer: (schema) => {
      return schema;
    }
  }
};

// Schema configuration for Mercurius
const schemaConfig = {
  typeDefs: optimizedTypeDefs,
  
  // Enable JIT compilation for better performance
  jit: 1,
  
  // Enable caching
  cache: true,
  
  // Query depth limiting
  queryDepth: 12,
  
  // Enable subscriptions
  subscription: true,
  
  // Schema validation
  schemaValidation: true,
  
  // Error formatting
  errorFormatter: (execution, context) => {
    const { errors } = execution;
    
    if (process.env.NODE_ENV === 'production') {
      // Don't leak sensitive information in production
      return {
        errors: errors.map(error => ({
          message: error.message,
          locations: error.locations,
          path: error.path,
          extensions: {
            code: error.extensions?.code || 'INTERNAL_ERROR'
          }
        }))
      };
    }
    
    return execution;
  },
  
  // Context function
  context: (request, reply) => {
    return {
      request,
      reply,
      db: request.db,
      mongoClient: request.mongoClient,
      // Add user context if authenticated
      user: request.user || null,
      // Add admin context
      isAdmin: request.isAdmin || false,
      // Add request tracking
      requestId: request.id,
      startTime: request.startTime
    };
  },
  
  // Validation rules
  validationRules: [
    // Add custom validation rules here
  ]
};

// Schema introspection configuration
const introspectionConfig = {
  // Disable introspection in production for security
  introspection: process.env.NODE_ENV !== 'production',
  
  // GraphiQL configuration
  graphiql: process.env.NODE_ENV !== 'production' ? {
    endpoint: '/graphql',
    subscriptionEndpoint: '/graphql',
    headerEditorEnabled: true,
    shouldPersistHeaders: true
  } : false
};

// Performance monitoring hooks
const performanceHooks = {
  // Pre-execution hook
  preExecution: async (schema, document, context) => {
    context.executionStartTime = Date.now();
  },
  
  // Post-execution hook
  onResolution: async (execution, context) => {
    const executionTime = Date.now() - context.executionStartTime;
    
    // Log slow queries
    if (executionTime > 1000) {
      context.request.log.warn({
        executionTime,
        query: execution.document,
        variables: execution.variableValues
      }, 'Slow GraphQL query detected');
    }
    
    // Add execution time to response headers
    context.reply.header('X-GraphQL-Execution-Time', `${executionTime}ms`);
  }
};

// Schema complexity analysis
const complexityAnalysis = {
  // Maximum query complexity
  maximumComplexity: 1000,
  
  // Complexity calculation
  estimateComplexity: (node, args, context, info) => {
    // Basic complexity calculation
    let complexity = 1;
    
    // Add complexity for pagination
    if (args.limit) {
      complexity += Math.min(args.limit, 100);
    }
    
    // Add complexity for search operations
    if (args.query || args.search) {
      complexity += 10;
    }
    
    return complexity;
  },
  
  // Complexity error handler
  onComplexityError: (complexity, context) => {
    context.request.log.warn({
      complexity,
      maxComplexity: 1000,
      query: context.document
    }, 'Query complexity exceeded');
    
    throw new Error(`Query complexity ${complexity} exceeds maximum allowed complexity of 1000`);
  }
};

// Export schema configuration
module.exports = {
  typeDefs: optimizedTypeDefs,
  schemaConfig,
  introspectionConfig,
  performanceHooks,
  complexityAnalysis,
  schemaDirectives
};
