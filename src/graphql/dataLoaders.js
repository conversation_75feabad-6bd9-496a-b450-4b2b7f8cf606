// File: src/graphql/dataLoaders.js
// DataLoader implementation for efficient batch loading
// Prevents N+1 query problems in GraphQL resolvers

const DataLoader = require('dataloader');
const { ObjectId } = require('mongodb');

class FastifyDataLoaders {
  constructor(db) {
    this.db = db;
    this.collections = {
      movies: db.collection('movies'),
      series: db.collection('series'),
      animes: db.collection('animes'),
      livetv: db.collection('livetv'),
      trending: db.collection('trending_items'),
      config: db.collection('config')
    };
    
    // Initialize all DataLoaders
    this.initializeLoaders();
  }

  initializeLoaders() {
    // Movie loaders
    this.movieById = new DataLoader(
      async (ids) => this.batchLoadById('movies', ids),
      { cache: true, maxBatchSize: 100 }
    );

    this.moviesByTmdbId = new DataLoader(
      async (tmdbIds) => this.batchLoadByTmdbId('movies', tmdbIds),
      { cache: true, maxBatchSize: 50 }
    );

    // Series loaders
    this.seriesById = new DataLoader(
      async (ids) => this.batchLoadById('series', ids),
      { cache: true, maxBatchSize: 100 }
    );

    this.seriesByTmdbId = new DataLoader(
      async (tmdbIds) => this.batchLoadByTmdbId('series', tmdbIds),
      { cache: true, maxBatchSize: 50 }
    );

    // Anime loaders
    this.animeById = new DataLoader(
      async (ids) => this.batchLoadById('animes', ids),
      { cache: true, maxBatchSize: 100 }
    );

    this.animeByJikanId = new DataLoader(
      async (jikanIds) => this.batchLoadByJikanId(jikanIds),
      { cache: true, maxBatchSize: 50 }
    );

    // LiveTV loaders
    this.livetvById = new DataLoader(
      async (ids) => this.batchLoadById('livetv', ids),
      { cache: true, maxBatchSize: 100 }
    );

    // Trending loaders
    this.trendingByMediaType = new DataLoader(
      async (mediaTypes) => this.batchLoadTrendingByMediaType(mediaTypes),
      { cache: true, maxBatchSize: 10, cacheKeyFn: (key) => `trending:${key}` }
    );

    // Genre loaders
    this.itemsByGenre = new DataLoader(
      async (genreQueries) => this.batchLoadByGenre(genreQueries),
      { 
        cache: true, 
        maxBatchSize: 20,
        cacheKeyFn: (query) => `genre:${query.collection}:${query.genre}:${query.limit}`
      }
    );

    // Related seasons loader
    this.relatedSeasons = new DataLoader(
      async (tmdbIds) => this.batchLoadRelatedSeasons(tmdbIds),
      { cache: true, maxBatchSize: 20 }
    );

    // Search loader for caching search results
    this.searchResults = new DataLoader(
      async (queries) => this.batchLoadSearchResults(queries),
      { 
        cache: true, 
        maxBatchSize: 10,
        cacheKeyFn: (query) => `search:${query.query}:${query.collections.join(',')}`
      }
    );
  }

  // Generic batch load by ID
  async batchLoadById(collectionName, ids) {
    try {
      const objectIds = ids.map(id => {
        try {
          return ObjectId.isValid(id) ? new ObjectId(id) : null;
        } catch {
          return null;
        }
      }).filter(Boolean);

      if (objectIds.length === 0) {
        return ids.map(() => null);
      }

      const items = await this.collections[collectionName]
        .find({ _id: { $in: objectIds } })
        .toArray();

      // Create a map for O(1) lookup
      const itemMap = new Map();
      items.forEach(item => {
        itemMap.set(item._id.toString(), item);
      });

      // Return items in the same order as requested IDs
      return ids.map(id => itemMap.get(id) || null);
    } catch (error) {
      console.error(`Error in batchLoadById for ${collectionName}:`, error);
      return ids.map(() => null);
    }
  }

  // Batch load by TMDB ID
  async batchLoadByTmdbId(collectionName, tmdbIds) {
    try {
      const validTmdbIds = tmdbIds.filter(id => id != null && !isNaN(id));
      
      if (validTmdbIds.length === 0) {
        return tmdbIds.map(() => []);
      }

      const items = await this.collections[collectionName]
        .find({ 'tmdb.id': { $in: validTmdbIds } })
        .toArray();

      // Group items by TMDB ID
      const itemMap = new Map();
      items.forEach(item => {
        const tmdbId = item.tmdb?.id;
        if (tmdbId) {
          if (!itemMap.has(tmdbId)) {
            itemMap.set(tmdbId, []);
          }
          itemMap.get(tmdbId).push(item);
        }
      });

      return tmdbIds.map(tmdbId => itemMap.get(tmdbId) || []);
    } catch (error) {
      console.error(`Error in batchLoadByTmdbId for ${collectionName}:`, error);
      return tmdbIds.map(() => []);
    }
  }

  // Batch load by Jikan ID (anime specific)
  async batchLoadByJikanId(jikanIds) {
    try {
      const validJikanIds = jikanIds.filter(id => id != null && !isNaN(id));
      
      if (validJikanIds.length === 0) {
        return jikanIds.map(() => null);
      }

      const items = await this.collections.animes
        .find({ 'jikan.mal_id': { $in: validJikanIds } })
        .toArray();

      const itemMap = new Map();
      items.forEach(item => {
        const jikanId = item.jikan?.mal_id;
        if (jikanId) {
          itemMap.set(jikanId, item);
        }
      });

      return jikanIds.map(jikanId => itemMap.get(jikanId) || null);
    } catch (error) {
      console.error('Error in batchLoadByJikanId:', error);
      return jikanIds.map(() => null);
    }
  }

  // Batch load trending items by media type
  async batchLoadTrendingByMediaType(mediaTypes) {
    try {
      const pipeline = [
        { $match: { mediaType: { $in: mediaTypes } } },
        { $sort: { rank: 1 } },
        { $limit: 100 }
      ];

      const trendingItems = await this.collections.trending
        .aggregate(pipeline)
        .toArray();

      // Group by media type
      const trendingMap = new Map();
      trendingItems.forEach(item => {
        if (!trendingMap.has(item.mediaType)) {
          trendingMap.set(item.mediaType, []);
        }
        trendingMap.get(item.mediaType).push(item);
      });

      return mediaTypes.map(mediaType => trendingMap.get(mediaType) || []);
    } catch (error) {
      console.error('Error in batchLoadTrendingByMediaType:', error);
      return mediaTypes.map(() => []);
    }
  }

  // Batch load items by genre
  async batchLoadByGenre(genreQueries) {
    try {
      const results = await Promise.all(
        genreQueries.map(async ({ collection, genre, limit = 20 }) => {
          try {
            return await this.collections[collection]
              .find({ 'tmdb.genres': genre })
              .limit(limit)
              .sort({ updatedAt: -1 })
              .toArray();
          } catch (error) {
            console.error(`Error loading genre ${genre} for ${collection}:`, error);
            return [];
          }
        })
      );

      return results;
    } catch (error) {
      console.error('Error in batchLoadByGenre:', error);
      return genreQueries.map(() => []);
    }
  }

  // Batch load related seasons
  async batchLoadRelatedSeasons(tmdbIds) {
    try {
      const validTmdbIds = tmdbIds.filter(id => id != null && !isNaN(id));
      
      if (validTmdbIds.length === 0) {
        return tmdbIds.map(() => []);
      }

      // Search across all collections for related seasons
      const [seriesResults, animeResults] = await Promise.all([
        this.collections.series.find({ 'tmdb.id': { $in: validTmdbIds } }).toArray(),
        this.collections.animes.find({ 'tmdb.id': { $in: validTmdbIds } }).toArray()
      ]);

      const allResults = [...seriesResults, ...animeResults];
      
      // Group by TMDB ID
      const resultsMap = new Map();
      allResults.forEach(item => {
        const tmdbId = item.tmdb?.id;
        if (tmdbId) {
          if (!resultsMap.has(tmdbId)) {
            resultsMap.set(tmdbId, []);
          }
          resultsMap.get(tmdbId).push(item);
        }
      });

      return tmdbIds.map(tmdbId => resultsMap.get(tmdbId) || []);
    } catch (error) {
      console.error('Error in batchLoadRelatedSeasons:', error);
      return tmdbIds.map(() => []);
    }
  }

  // Batch load search results
  async batchLoadSearchResults(queries) {
    try {
      const results = await Promise.all(
        queries.map(async ({ query, collections, limit = 20 }) => {
          try {
            const searchPromises = collections.map(async (collectionName) => {
              const regexQuery = new RegExp(query, 'i');
              const searchQuery = {
                $or: [
                  { title: regexQuery },
                  { cleanedTitle: regexQuery },
                  { 'metadata.actors': regexQuery },
                  { 'tmdb.title': regexQuery }
                ]
              };

              const items = await this.collections[collectionName]
                .find(searchQuery)
                .limit(Math.ceil(limit / collections.length))
                .sort({ updatedAt: -1 })
                .toArray();

              return items.map(item => ({
                ...item,
                __typename: this.getTypeName(collectionName)
              }));
            });

            const allResults = await Promise.all(searchPromises);
            return allResults.flat().slice(0, limit);
          } catch (error) {
            console.error(`Error in search for query "${query}":`, error);
            return [];
          }
        })
      );

      return results;
    } catch (error) {
      console.error('Error in batchLoadSearchResults:', error);
      return queries.map(() => []);
    }
  }

  // Helper method to get type name
  getTypeName(collectionName) {
    const typeMap = {
      movies: 'Movie',
      series: 'Series',
      animes: 'Anime',
      livetv: 'LiveTV'
    };
    return typeMap[collectionName] || 'Unknown';
  }

  // Clear all caches
  clearAll() {
    this.movieById.clearAll();
    this.moviesByTmdbId.clearAll();
    this.seriesById.clearAll();
    this.seriesByTmdbId.clearAll();
    this.animeById.clearAll();
    this.animeByJikanId.clearAll();
    this.livetvById.clearAll();
    this.trendingByMediaType.clearAll();
    this.itemsByGenre.clearAll();
    this.relatedSeasons.clearAll();
    this.searchResults.clearAll();
  }

  // Clear specific cache
  clearCache(loaderName) {
    if (this[loaderName] && typeof this[loaderName].clearAll === 'function') {
      this[loaderName].clearAll();
    }
  }

  // Get cache statistics
  getCacheStats() {
    const loaders = [
      'movieById', 'moviesByTmdbId', 'seriesById', 'seriesByTmdbId',
      'animeById', 'animeByJikanId', 'livetvById', 'trendingByMediaType',
      'itemsByGenre', 'relatedSeasons', 'searchResults'
    ];

    const stats = {};
    loaders.forEach(loaderName => {
      const loader = this[loaderName];
      if (loader) {
        stats[loaderName] = {
          cacheSize: loader._cache ? loader._cache.size : 0,
          maxBatchSize: loader._maxBatchSize || 0
        };
      }
    });

    return stats;
  }
}

module.exports = FastifyDataLoaders;
