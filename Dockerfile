# Combined NetStream Docker Image
# Builds both frontend and backend in a single container for Render.com deployment

# Stage 1: Build Frontend (Next.js)
FROM node:20-alpine AS frontend-builder

WORKDIR /app

# Install system dependencies for better compatibility
RUN apk add --no-cache libc6-compat python3 make g++

# Set build-time environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1
ENV NEXT_PUBLIC_API_URL=""
ENV NEXT_PUBLIC_GRAPHQL_ENDPOINT="/graphql"
ENV NEXT_PUBLIC_API_BASE_URL=""
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Copy frontend package files
COPY netstream-nextjs/package.json netstream-nextjs/package-lock.json* ./

# Clear npm cache and install dependencies
RUN npm cache clean --force
RUN npm install --legacy-peer-deps --no-audit --no-fund

# Copy frontend source code
COPY netstream-nextjs/ .

# Build the Next.js application with memory optimization
RUN NODE_OPTIONS="--max-old-space-size=4096" npm run build

# Stage 2: Production Runtime (Backend + Frontend)
FROM node:18-slim AS production

# Install system dependencies for Chromium and Node apps
RUN apt-get update && apt-get install -yq --no-install-recommends \
    chromium \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm-dev \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxshmfence-dev \
    libxtst6 \
    ca-certificates \
    fonts-liberation \
    lsb-release \
    wget \
    xdg-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /usr/src/app

# Set basic environment variables (secrets will come from docker-compose env_file)
ENV NODE_ENV=production
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV DOCKER=true

# Copy backend package files and install dependencies
COPY package.json package-lock.json* ./
RUN npm install --omit=dev --no-audit --no-fund

# Copy backend source code (excluding netstream-nextjs to avoid conflicts)
COPY server-fastify.js ./
COPY server-combined.js ./
COPY src/ ./src/
COPY resolvers.js ./
COPY schema.graphql ./
COPY scripts/ ./scripts/
COPY public/ ./public/

# Copy built frontend from frontend-builder stage
COPY --from=frontend-builder /app/.next/standalone ./frontend/
COPY --from=frontend-builder /app/.next/static ./frontend/.next/static
COPY --from=frontend-builder /app/public ./frontend/public

# Expose the main port
EXPOSE 3000

# Start the combined server
CMD ["node", "server-combined.js"]