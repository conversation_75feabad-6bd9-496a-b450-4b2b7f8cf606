# 1. Use an official Node.js LTS slim image
FROM node:18-slim

# 2. Install necessary dependencies for Chromium and Node apps
# Using a more comprehensive list found to work well in container environments
RUN apt-get update && apt-get install -yq --no-install-recommends \
    # --- System Chromium. ---
    chromium \
    # --- Dependencies for Chromium ---
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm-dev \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxshmfence-dev \
    libxtst6 \
    ca-certificates \ 
    fonts-liberation \ 
    lsb-release \
    wget \
    xdg-utils \
    # --- End Dependencies ---
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 3. Set the working directory
WORKDIR /usr/src/app

# 4. Copy package files (leverages Docker cache)
COPY package.json package-lock.json* ./
# If using yarn: COPY package.json yarn.lock ./

# 5. Install dependencies using npm ci for reproducible builds
# This respects PUPPETEER_SKIP_CHROMIUM_DOWNLOAD env var later
RUN npm ci
# If using yarn: RUN yarn install --frozen-lockfile

# 6. Set environment variables *before* copying code, potentially useful earlier
# Tell Puppeteer where the system Chromium is and not to download its own
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
# Set Node environment to production (good practice)
ENV NODE_ENV=production

# 7. Copy the rest of your application code
COPY . .

# 8. Define the command to run your application server
# This should be your main server file
CMD [ "node", "server.js" ]

# --- Important Reminder ---
# Your Node.js code STILL needs the correct launch arguments for container environments:
# e.g., puppeteer.launch({
#   executablePath: process.env.PUPPETEER_EXECUTABLE_PATH, // Redundant if env var is set, but explicit
#   headless: true, // Or 'new'
#   args: [
#     '--no-sandbox',
#     '--disable-setuid-sandbox',
#     '--disable-dev-shm-usage',
#     '--disable-gpu',
#     '--no-zygote'
#   ]
# });

EXPOSE 3000