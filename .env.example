# NetStream GraphQL Environment Configuration - EXAMPLE ONLY
# This is just a template - you already have a working .env file
# No need to use this if your current .env is working

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Connection String
# For local MongoDB: mongodb://localhost:27017/NetStream
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/NetStream?retryWrites=true&w=majority
MONGO_URI=mongodb://mongo:27017/NetStream

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis connection settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379
REDIS_SERVICE_URL=

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Application ports
PORT=3000
FASTIFY_PORT=3001
GRAPHQL_PORT=4000

# Node environment
NODE_ENV=production

# =============================================================================
# EXTERNAL API KEYS
# =============================================================================

# TMDB API Key (get from https://www.themoviedb.org/settings/api)
TMDB_API_KEY=your_tmdb_api_key_here

# Google Gemini AI API Key (get from https://makersuite.google.com/app/apikey)
GEMINI_API_KEY=your_gemini_api_key_here

# OneUpload API Key
ONEUPLOAD_API_KEY=your_oneupload_api_key_here

# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================

# Telegram Bot Token (get from @BotFather)
TELEGRAM_TOKEN=your_telegram_bot_token_here

# Telegram Group/Channel IDs
PRIVATE_GROUP_ID=your_private_group_id_here
TELEGRAM_API_ID=your_telegram_api_id_here
TELEGRAM_API_HASH=your_telegram_api_hash_here
WIFLIX_CHANNEL=@your_channel_here

# =============================================================================
# SCRAPING CONFIGURATION
# =============================================================================

# Scraping behavior
SCRAPE_ON_START=true
SCRAPE_MODE=latest

# Scraping source base URLs (these can be updated via admin panel)
WIFLIX_BASE=wiflix-max.cam
FRENCH_ANIME_BASE=french-anime.com
WITV_BASE=witv.skin

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Puppeteer settings
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
MAX_CONCURRENT_PAGES=2
MAX_CONCURRENT_PUPPETEER_TASKS=1
MAX_CONCURRENT_AXIOS_TASKS=2

# Retry settings
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_BASE=3000

# Rate limiting (requests per minute)
GEMINI_RATE_LIMIT=28
TMDB_RATE_LIMIT=40
JIKAN_RATE_LIMIT=50

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
USE_ADVANCED_ENRICHMENT=true
FETCH_SEASONS=true
ENABLE_CACHING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Admin access key (change this to a secure random string)
ADMIN_KEY=your_secure_admin_key_here

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Set to true when running in Docker
DOCKER=true

# Disable proxy settings for local deployment
NO_PROXY=*

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Next.js API URLs (for frontend to communicate with backend)
API_URL=http://backend:3001/graphql
NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# Next.js build optimization
NEXT_TELEMETRY_DISABLED=1
SKIP_ENV_VALIDATION=1
NODE_OPTIONS=--max-old-space-size=4096
