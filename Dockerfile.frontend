# NetStream Frontend Dockerfile - Optimized for Ubuntu/Proxmox
# Multi-stage build for Next.js application

# Stage 1: Dependencies and build
FROM node:20-alpine AS builder

WORKDIR /app

# Accept build arguments for API URLs
ARG NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
ARG NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
ARG NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# Set environment variables from build args
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_GRAPHQL_ENDPOINT=$NEXT_PUBLIC_GRAPHQL_ENDPOINT
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL

# Install system dependencies for better compatibility
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++

# Copy package files
COPY netstream-nextjs/package.json netstream-nextjs/package-lock.json* ./

# Clear npm cache and install dependencies
RUN npm cache clean --force && \
    if [ -f package-lock.json ]; then npm ci --legacy-peer-deps --no-audit --no-fund; else npm install --legacy-peer-deps --no-audit --no-fund; fi

# Copy source code
COPY netstream-nextjs/ .

# Set build environment variables (will be overridden by docker-compose)
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1
ENV NODE_OPTIONS="--max-old-space-size=2048"

# Build the Next.js application
RUN npm run build

# Stage 2: Production runtime
FROM node:20-alpine AS runner

WORKDIR /app

# Install wget for health checks (more reliable than curl in Alpine)
RUN apk add --no-cache wget

# Set production environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy built application from builder stage
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Set runtime environment
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Health check with much longer start period and simpler check
HEALTHCHECK --interval=45s --timeout=20s --start-period=120s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

# Start the Next.js server
CMD ["node", "server.js"]
