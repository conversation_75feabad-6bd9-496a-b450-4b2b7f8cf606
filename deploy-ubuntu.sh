#!/bin/bash

# NetStream Docker Deployment Script for Ubuntu/Proxmox
# This script helps deploy and manage the NetStream application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
MINIMAL_COMPOSE_FILE="docker-compose.minimal.yml"
ENV_FILE=".env"
ENV_EXAMPLE=".env.ubuntu"

# Check if user wants minimal setup (no local MongoDB/Redis)
if [ "$1" = "minimal" ] || [ "$2" = "minimal" ]; then
    COMPOSE_FILE="$MINIMAL_COMPOSE_FILE"
    print_status "Using minimal setup (external MongoDB Atlas, no local Redis)"
fi

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Run: curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Function to check system requirements
check_system() {
    print_status "Checking system requirements..."
    
    # Check available memory
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [ "$TOTAL_MEM" -lt 2048 ]; then
        print_warning "System has less than 2GB RAM ($TOTAL_MEM MB). Performance may be affected."
    else
        print_success "System has sufficient memory ($TOTAL_MEM MB)"
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$AVAILABLE_SPACE" -lt 5 ]; then
        print_warning "Less than 5GB disk space available. Consider freeing up space."
    else
        print_success "Sufficient disk space available (${AVAILABLE_SPACE}GB)"
    fi
}

# Function to setup environment file
setup_env() {
    if [ ! -f "$ENV_FILE" ]; then
        if [ -f "$ENV_EXAMPLE" ]; then
            print_status "Creating .env file from template..."
            cp "$ENV_EXAMPLE" "$ENV_FILE"
            print_warning "Please edit .env file and update the values marked with 'CHANGE_ME'"
            print_warning "Required changes:"
            echo "  - TMDB_API_KEY"
            echo "  - GEMINI_API_KEY"
            echo "  - TELEGRAM_TOKEN (if using Telegram features)"
            echo "  - ADMIN_KEY (change to a secure random string)"
            echo ""
            read -p "Press Enter after updating the .env file..."
        else
            print_error ".env file not found and no template available"
            exit 1
        fi
    else
        print_success ".env file exists with your configuration"

        # Check if using MongoDB Atlas
        if grep -q "mongodb+srv://" .env; then
            print_success "Using MongoDB Atlas (external database)"
        elif grep -q "mongodb://mongo:" .env; then
            print_success "Using local MongoDB container"
        else
            print_warning "MongoDB configuration detected - will use as configured"
        fi
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p logs provider_url_output
    print_success "Directories created"
}

# Function to pull Docker images
pull_images() {
    print_status "Pulling Docker images..."
    docker-compose pull
    print_success "Docker images pulled"
}

# Function to build Docker images
build_images() {
    print_status "Building Docker images..."
    docker-compose build --no-cache
    print_success "Docker images built"
}

# Function to start services
start_services() {
    print_status "Starting NetStream services..."
    docker-compose up -d
    print_success "Services started"
    
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_health
}

# Function to check service health
check_health() {
    print_status "Checking service health..."
    
    # Check if containers are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Containers are running"
    else
        print_error "Some containers are not running"
        docker-compose ps
        return 1
    fi
    
    # Check backend health
    if curl -f http://localhost:3001/graphql?query={__typename} &> /dev/null; then
        print_success "Backend is healthy"
    else
        print_warning "Backend health check failed"
    fi
    
    # Check frontend health
    if curl -f http://localhost:3000 &> /dev/null; then
        print_success "Frontend is healthy"
    else
        print_warning "Frontend health check failed"
    fi
}

# Function to show logs
show_logs() {
    docker-compose logs -f
}

# Function to stop services
stop_services() {
    print_status "Stopping NetStream services..."
    docker-compose down
    print_success "Services stopped"
}

# Function to restart services
restart_services() {
    print_status "Restarting NetStream services..."
    docker-compose restart
    print_success "Services restarted"
}

# Function to show status
show_status() {
    print_status "NetStream Service Status:"
    docker-compose ps
    echo ""
    print_status "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down -v
    docker system prune -f
    print_success "Cleanup completed"
}

# Function to show help
show_help() {
    echo "NetStream Docker Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  deploy     - Full deployment (setup, build, start)"
    echo "  start      - Start services"
    echo "  stop       - Stop services"
    echo "  restart    - Restart services"
    echo "  status     - Show service status"
    echo "  logs       - Show service logs"
    echo "  health     - Check service health"
    echo "  cleanup    - Stop services and cleanup"
    echo "  help       - Show this help message"
    echo ""
    echo "Options:"
    echo "  minimal    - Use minimal setup (external MongoDB Atlas, no local Redis)"
    echo "               Example: $0 deploy minimal"
    echo ""
    echo "Configuration:"
    echo "  - Uses your existing .env file with MongoDB Atlas URI"
    echo "  - Default: Includes local Redis for caching"
    echo "  - Minimal: Only frontend and backend containers"
    echo ""
}

# Main script logic
case "${1:-deploy}" in
    "deploy")
        print_status "Starting NetStream deployment..."
        check_docker
        check_system
        setup_env
        create_directories
        build_images
        start_services
        print_success "Deployment completed!"
        echo ""
        print_status "Access your NetStream application at:"
        echo "  Frontend: http://localhost:3000"
        echo "  GraphQL API: http://localhost:3001/graphql"
        ;;
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "health")
        check_health
        ;;
    "cleanup")
        cleanup
        ;;
    "help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
