// File: resolvers.js
// FINAL - INCORPORATES PATH-BASED URL RESOLUTION & MOVIE-SPECIFIC THUMBNAIL LOGIC

const { GraphQLError } = require("graphql");
const Movie = require("./src/db/models/Movie"); // Adjust path if needed
const Series = require("./src/db/models/Series"); // Adjust path if needed
const Anime = require("./src/db/models/Anime"); // Adjust path if needed
const LiveTV = require("./src/db/models/LiveTV"); // Adjust path if needed
const TrendingItem = require("./src/db/models/TrendingItem");
const Admin = require("./src/db/models/Admin"); // Import Admin model
const Config = require("./src/db/models/Config"); // Import Config model
const mongoose = require("mongoose");
const logger = require("./src/utils/logger"); // Adjust path if needed
const { fetchSourceStreamUrl } = require("./src/utils/sourceStreamFetcher"); // Adjust path if needed
const { cache: unifiedCache } = require("./src/utils/unifiedCache"); // Import unified cache
const { WIFLIX_BASE } = require("./src/config/constants"); // <<< Import WIFLIX_BASE
const { scrapeWiflixDetail } = require("./src/scrapers/sites/wiflix/detail"); // For scraping
const { scrapeFrenchAnimeDetail } = require("./src/scrapers/sites/frenchAnime/detail"); // For anime scraping
const { enrichItemWithOptions } = require("./src/enrichment/services/enrichService"); // For enrichment
const crypto = require("crypto"); // For generating log IDs
const { sendLogToSubscribers } = require("./src/utils/websocketLogger"); // For WebSocket logging

// --- Helper Function to Construct Base URL ---
const getWiflixBaseUrl = () => {
    const baseDomain = WIFLIX_BASE || 'wiflix-max.cam'; // Use imported constant with fallback
    return `https://${baseDomain}`;
};
// ---

// Helper function for Trending sort
async function getTrendingSortedItems(Model, mediaType, page, limit) {
  const skip = (page - 1) * limit;
  logger.info(
    `Fetching trending items: type=${mediaType}, page=${page}, limit=${limit}`
  );
  try {
    // 1. Get trending TMDb IDs and ranks for the current page
    const trendingIdsWithRank = await TrendingItem.find({
      mediaType,
      source: "tmdb",
    }) // Ensure source is tmdb
      .sort({ rank: 1 }) // Sort by rank ascending
      .skip(skip)
      .limit(limit)
      .select("tmdbId rank") // Select only needed fields
      .lean(); // Use lean for performance

    if (trendingIdsWithRank.length === 0) {
      logger.warn(
        `No trending items found in DB for ${mediaType} page ${page}. Falling back to latest updated.`
      );
      // Fallback to latest updated if no trending data available for this specific page range
      return await Model.find().sort({ updatedAt: -1 }).skip(skip).limit(limit);
    }

    const trendingTmdbIds = trendingIdsWithRank.map((t) => t.tmdbId);
    const rankMap = new Map(trendingIdsWithRank.map((t) => [t.tmdbId, t.rank]));

    // 2. Fetch the actual items from your DB using the TMDB IDs
    // Ensure the Model has an indexed 'tmdb.id' field for performance
    const items = await Model.find({ "tmdb.id": { $in: trendingTmdbIds } });

    // 3. Sort the fetched items based on the original TMDb rank
    items.sort((a, b) => {
      const rankA = rankMap.get(a.tmdb?.id) ?? Infinity; // Use Infinity if ID not in map
      const rankB = rankMap.get(b.tmdb?.id) ?? Infinity;
      return rankA - rankB;
    });

    logger.info(
      `Returning ${items.length} trending items sorted by rank for ${mediaType} page ${page}.`
    );
    return items;
  } catch (error) {
    logger.error(
      `Error fetching or sorting trending items for ${mediaType}: ${error.message}`,
      { stack: error.stack }
    );
    // Fallback to latest updated on error during trending fetch/sort
    logger.warn(`Falling back to latest updated due to trending error.`);
    return await Model.find().sort({ updatedAt: -1 }).skip(skip).limit(limit);
  }
}

const resolvers = {
  Query: {
    validateAdminToken: async (_, { token }, context) => {
      try {
        // Use direct database access instead of Mongoose model (which is timing out)
        const { db } = context || {};

        if (db) {
          // Check in admin collection (matches fastifyResolvers approach)
          const session = await db.collection('admin').findOne({
            type: 'session',
            token,
            expiresAt: { $gt: new Date() }
          });

          logger.info(`Admin token validation: ${token ? 'TOKEN_PROVIDED' : 'NO_TOKEN'}, valid: ${!!session}`);
          return { isValid: !!session };
        } else {
          // Fallback to Mongoose model if no db context
          const isValid = await Admin.validateToken(token);
          return { isValid };
        }
      } catch (error) {
        logger.error(`Admin token validation error: ${error.message}`);
        return { isValid: false };
      }
    },

    item: async (_, { id, type }) => {
      const Model = {
        MOVIE: Movie,
        SERIES: Series,
        ANIME: Anime,
        LIVETV: LiveTV,
      }[type];
      if (!Model)
        throw new GraphQLError("Unsupported item type", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new GraphQLError("Invalid ID format", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      }
      const item = await Model.findById(id);
      if (!item)
        throw new GraphQLError("Item not found", {
          extensions: { code: "NOT_FOUND" },
        });
      return item; // Return the Mongoose document directly
    },

    search: async (_, { query, page = 1, limit = 20 }) => {
      logger.info(
        `GraphQL Search: query=${query}, page=${page}, limit=${limit}`
      );
      const skip = (page - 1) * limit;

      // Use MongoDB text search for better performance
      const textSearchQuery = { $text: { $search: query } };
      const regexQuery = new RegExp(query, "i");

      // Fallback conditions for collections without text index
      const fallbackConditions = [
        { title: regexQuery },
        { cleanedTitle: regexQuery },
        { "metadata.actors": regexQuery },
        { "metadata.creator": regexQuery },
        { "tmdb.title": regexQuery },
        { "jikan.title.default": regexQuery },
        { "jikan.title.english": regexQuery },
        { "jikan.title.japanese": regexQuery },
      ];

      try {
        // Use lean() for better performance and select only needed fields
        const [movies, series, anime, livetv] = await Promise.all([
          Movie.find(textSearchQuery)
            .select('id title displayTitle thumbnail image detailUrl detailUrlPath streamingUrls metadata tmdb jikan') // Expanded TMDB and Jikan selection
            .sort({ score: { $meta: "textScore" }, updatedAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean()
            .catch(() =>
              Movie.find({ $or: fallbackConditions.slice(0, 5) })
                .select('id title displayTitle thumbnail image detailUrl detailUrlPath streamingUrls metadata tmdb jikan') // Expanded TMDB and Jikan selection
                .sort({ updatedAt: -1 })
                .skip(skip)
                .limit(limit)
                .lean()
            ),
          Series.find({ $or: fallbackConditions.slice(0, 5) })
            .select('id title displayTitle thumbnail image detailUrl detailUrlPath season episodes streamingUrls metadata tmdb jikan') // Expanded TMDB and Jikan selection, and episodes
            .sort({ updatedAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean(),
          Anime.find({ $or: fallbackConditions })
            .select('id title displayTitle thumbnail image detailUrl detailUrlPath season animeLanguage episodes streamingUrls metadata tmdb jikan') // Expanded TMDB and Jikan selection, animeLanguage and episodes
            .sort({ updatedAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean(),
          LiveTV.find({ title: regexQuery })
            .select('id title displayTitle thumbnail image detailUrl detailUrlPath streamingUrls metadata category language country') // Expanded LiveTV specific fields
            .sort({ updatedAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean(),
        ]);

        // --- Explicitly map _id to id ---
        const mapItem = (item, typename) => {
          // Since we're using .lean(), item is already a plain object
          return {
            ...item, // Spread the original object properties (already plain object from .lean())
            id: item._id?.toString(), // Ensure ID is mapped and stringified
            __typename: typename, // Add typename for interface resolution
             // Include path fields needed by resolvers if not already present
            detailUrlPath: item.detailUrlPath,
            imagePath: item.imagePath,
            thumbnailPath: item.thumbnailPath,
          };
        };

        const items = [
          ...movies.map((m) => mapItem(m, "Movie")),
          ...series.map((s) => mapItem(s, "Series")),
          ...anime.map((a) => mapItem(a, "Anime")),
          ...livetv.map((l) => mapItem(l, "LiveTV")),
        ];
        // --- End of explicit mapping ---

        logger.info(
          `GraphQL Search: Found ${items.length} results for query "${query}"`
        );
        return { items }; // Return the object containing the items array
      } catch (error) {
        logger.error(`GraphQL Search Error: ${error.message}`, {
          query,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Search failed", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    movies: async (_, { sort, page = 1, limit = 20 }) => {
      logger.info(`GraphQL Movies: sort=${sort}, page=${page}, limit=${limit}`);
      const skip = (page - 1) * limit;
      try {
        // *** ADDED: Handle TRENDING sort ***
        if (sort === "TRENDING") {
          return await getTrendingSortedItems(Movie, "movie", page, limit);
        }

        // Handle other sorts
        let sortOption = {};
        switch (sort) {
          case "LATEST":
            sortOption = { updatedAt: -1 };
            break;
          case "ALPHA":
            sortOption = { title: 1 };
            break;
          case "RELEASE":
            sortOption = { "tmdb.release_date": -1, createdAt: -1 };
            break;
          default: // Default fallback if sort is null/undefined or invalid
            sortOption = { updatedAt: -1 }; // Fallback to LATEST
            if (sort)
              logger.warn(
                `Movies query: Unhandled sort '${sort}', defaulting to LATEST.`
              );
        }
        // Return Mongoose documents for sub-resolvers to work
        return await Movie.find().sort(sortOption).skip(skip).limit(limit);
      } catch (error) {
        logger.error(`GraphQL Movies Error: ${error.message}`, {
          sort,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch movies", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    series: async (_, { sort, page = 1, limit = 20 }) => {
      logger.info(`GraphQL Series: sort=${sort}, page=${page}, limit=${limit}`);
      const skip = (page - 1) * limit;
      try {
        // *** ADDED: Handle TRENDING sort ***
        if (sort === "TRENDING") {
          return await getTrendingSortedItems(Series, "tv", page, limit); // Use 'tv' for Series
        }

        let sortOption = {};
        switch (sort) {
          case "LATEST":
            sortOption = { updatedAt: -1 };
            break;
          case "ALPHA":
            sortOption = { title: 1 };
            break;
          case "RELEASE":
            sortOption = { "tmdb.release_date": -1, createdAt: -1 };
            break; // Assuming release_date holds first_air_date
          default:
            sortOption = { updatedAt: -1 }; // Fallback to LATEST
            if (sort)
              logger.warn(
                `Series query: Unhandled sort '${sort}', defaulting to LATEST.`
              );
        }
        return await Series.find().sort(sortOption).skip(skip).limit(limit);
      } catch (error) {
        logger.error(`GraphQL Series Error: ${error.message}`, {
          sort,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch series", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    anime: async (_, { sort, page = 1, limit = 20 }) => {
      logger.info(`GraphQL Anime: sort=${sort}, page=${page}, limit=${limit}`);
      const skip = (page - 1) * limit;
      try {
        // *** ADDED: Handle TRENDING sort ***
        if (sort === "TRENDING") {
          // Assuming Anime might use TMDB TV IDs for trending correlation
          return await getTrendingSortedItems(Anime, "tv", page, limit);
        }

        let sortOption = {};
        switch (sort) {
          case "LATEST":
            sortOption = { updatedAt: -1 };
            break;
          case "ALPHA":
            sortOption = { title: 1 };
            break;
          case "RELEASE":
            sortOption = {
              "jikan.year": -1, // Prioritize Jikan year if available
              "tmdb.release_date": -1, // Fallback to TMDB release date
              createdAt: -1, // Final fallback
            };
            break;
          default:
            sortOption = { updatedAt: -1 }; // Fallback to LATEST
            if (sort)
              logger.warn(
                `Anime query: Unhandled sort '${sort}', defaulting to LATEST.`
              );
        }
        return await Anime.find().sort(sortOption).skip(skip).limit(limit);
      } catch (error) {
        logger.error(`GraphQL Anime Error: ${error.message}`, {
          sort,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch anime", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // liveTV remains unchanged (no TRENDING sort)
    liveTV: async (_, { page = 1, limit = 20 }) => {
      logger.info(`GraphQL LiveTV: page=${page}, limit=${limit}`);
      const skip = (page - 1) * limit;
      try {
        const liveTVChannels = await LiveTV.find().sort({ title: 1 }).skip(skip).limit(limit);

        // Explicitly set __typename for each LiveTV object
        return liveTVChannels.map(channel => ({
          ...channel.toObject(),
          __typename: 'LiveTV'
        }));
      } catch (error) {
        logger.error(`GraphQL LiveTV Error: ${error.message}`, {
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch live TV channels", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // New resolver for latest movies (excluding film-ancien) - no cache for real-time updates
    latestMovies: async (_, { excludeAncien = true, page = 1, limit = 20 }) => {
      logger.info(`GraphQL latestMovies: excludeAncien=${excludeAncien}, page=${page}, limit=${limit}`);

      const skip = (page - 1) * limit;
      try {
        let query = {};

        // If excludeAncien is true, filter out movies with "film-ancien" in the detailUrl
        if (excludeAncien) {
          query = { detailUrl: { $not: { $regex: 'film-ancien', $options: 'i' } } };
        }

        const result = await Movie.find(query)
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);

        return result;
      } catch (error) {
        logger.error(`GraphQL latestMovies Error: ${error.message}`, {
          excludeAncien,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch latest movies", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // New resolver for ancien movies
    ancienMovies: async (_, { page = 1, limit = 20 }) => {
      logger.info(`GraphQL ancienMovies: page=${page}, limit=${limit}`);
      const skip = (page - 1) * limit;
      try {
        // Find movies with "film-ancien" in the detailUrl
        return await Movie.find({ detailUrl: { $regex: 'film-ancien', $options: 'i' } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);
      } catch (error) {
        logger.error(`GraphQL ancienMovies Error: ${error.message}`, {
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch ancien movies", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // New resolver for latest series
    latestSeries: async (_, { page = 1, limit = 20 }) => {
      logger.info(`GraphQL latestSeries: page=${page}, limit=${limit}`);

      // Generate cache key
      const cacheKey = unifiedCache.generateKey('graphql', 'latestSeries', { page, limit });

      // Try to get from cache first
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for latestSeries: page=${page}`);
        return cachedResult;
      }

      const skip = (page - 1) * limit;
      try {
        const result = await Series.find()
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);

        // Cache the result for 10 minutes
        unifiedCache.set('graphql', cacheKey, result, 600000); // 10 minutes

        return result;
      } catch (error) {
        logger.error(`GraphQL latestSeries Error: ${error.message}`, {
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch latest series", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // New resolver for latest anime
    latestAnime: async (_, { page = 1, limit = 20 }) => {
      logger.info(`GraphQL latestAnime: page=${page}, limit=${limit}`);

      // Generate cache key
      const cacheKey = unifiedCache.generateKey('graphql', 'latestAnime', { page, limit });

      // Try to get from cache first
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for latestAnime: page=${page}`);
        return cachedResult;
      }

      const skip = (page - 1) * limit;
      try {
        const result = await Anime.find()
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);

        // Cache the result for 10 minutes
        unifiedCache.set('graphql', cacheKey, result, 600000); // 10 minutes

        return result;
      } catch (error) {
        logger.error(`GraphQL latestAnime Error: ${error.message}`, {
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch latest anime", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // New resolver for anime movies from films-vf-vostfr
    animeMovies: async (_, { page = 1, limit = 20 }) => {
      logger.info(`GraphQL animeMovies: page=${page}, limit=${limit}`);
      const skip = (page - 1) * limit;
      try {
        // Find anime with "films-vf-vostfr" in the detailUrlPath
        return await Anime.find({ detailUrlPath: { $regex: 'films-vf-vostfr', $options: 'i' } })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);
      } catch (error) {
        logger.error(`GraphQL animeMovies Error: ${error.message}`, {
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch anime movies", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // Genre-based resolvers
    moviesByGenre: async (_, { genre, page = 1, limit = 20 }) => {
      logger.info(`GraphQL moviesByGenre: genre=${genre}, page=${page}, limit=${limit}`);

      // Generate cache key
      const cacheKey = unifiedCache.generateKey('graphql', 'moviesByGenre', { genre, page, limit });

      // Try to get from cache first
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for moviesByGenre: ${genre}, page=${page}`);
        return cachedResult;
      }

      const skip = (page - 1) * limit;
      try {
        // Search for movies with the specified genre in TMDB genres
        const result = await Movie.find({
          'tmdb.genres': { $regex: new RegExp(genre, 'i') }
        })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);

        // Cache the result for 30 minutes
        unifiedCache.set('graphql', cacheKey, result, 1800000); // 30 minutes

        return result;
      } catch (error) {
        logger.error(`GraphQL moviesByGenre Error: ${error.message}`, {
          genre,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch movies by genre", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    seriesByGenre: async (_, { genre, page = 1, limit = 20 }) => {
      logger.info(`GraphQL seriesByGenre: genre=${genre}, page=${page}, limit=${limit}`);

      // Generate cache key
      const cacheKey = unifiedCache.generateKey('graphql', 'seriesByGenre', { genre, page, limit });

      // Try to get from cache first
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for seriesByGenre: ${genre}, page=${page}`);
        return cachedResult;
      }

      const skip = (page - 1) * limit;
      try {
        // Search for series with the specified genre in TMDB genres
        const result = await Series.find({
          'tmdb.genres': { $regex: new RegExp(genre, 'i') }
        })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);

        // Cache the result for 30 minutes
        unifiedCache.set('graphql', cacheKey, result, 1800000); // 30 minutes

        return result;
      } catch (error) {
        logger.error(`GraphQL seriesByGenre Error: ${error.message}`, {
          genre,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch series by genre", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    animeByGenre: async (_, { genre, page = 1, limit = 20 }) => {
      logger.info(`GraphQL animeByGenre: genre=${genre}, page=${page}, limit=${limit}`);

      const cacheKey = unifiedCache.generateKey('graphql', 'animeByGenre', { genre, page, limit });
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for animeByGenre: ${genre}, page=${page}`);
        return cachedResult;
      }

      const skip = (page - 1) * limit;
      try {
        // Search for anime with the specified genre in TMDB or Jikan genres
        const result = await Anime.find({
          $or: [
            { 'tmdb.genres': { $regex: new RegExp(genre, 'i') } },
            { 'jikan.genres.name': { $regex: new RegExp(genre, 'i') } }
          ]
        })
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit);

        unifiedCache.set('graphql', cacheKey, result, 1800000); // 30 minutes
        return result;
      } catch (error) {
        logger.error(`GraphQL animeByGenre Error: ${error.message}`, {
          genre,
          page,
          limit,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch anime by genre", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    availableGenres: async () => {
      logger.info('GraphQL availableGenres: Fetching available genres');

      // Generate cache key for available genres
      const cacheKey = unifiedCache.generateKey('graphql', 'availableGenres', {});

      // Try to get from cache first
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info('Cache hit for availableGenres');
        return cachedResult;
      }

      try {
        // Get top genres for each type
        const [movieGenres, seriesGenres, animeGenres] = await Promise.all([
          // Movie genres from TMDB
          Movie.aggregate([
            { $match: { 'tmdb.genres': { $exists: true, $ne: [] } } },
            { $unwind: '$tmdb.genres' },
            { $group: { _id: '$tmdb.genres', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 15 },
            { $project: { _id: 1 } }
          ]),
          // Series genres from TMDB
          Series.aggregate([
            { $match: { 'tmdb.genres': { $exists: true, $ne: [] } } },
            { $unwind: '$tmdb.genres' },
            { $group: { _id: '$tmdb.genres', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 15 },
            { $project: { _id: 1 } }
          ]),
          // Anime genres from Jikan
          Anime.aggregate([
            { $match: { 'jikan.genres': { $exists: true, $ne: [] } } },
            { $unwind: '$jikan.genres' },
            { $group: { _id: '$jikan.genres.name', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 15 },
            { $project: { _id: 1 } }
          ])
        ]);

        const result = {
          movies: movieGenres.map(g => g._id).filter(Boolean),
          series: seriesGenres.map(g => g._id).filter(Boolean),
          anime: animeGenres.map(g => g._id).filter(Boolean)
        };

        // Cache the result for 1 hour (this is expensive to compute)
        unifiedCache.set('graphql', cacheKey, result, 3600000); // 1 hour

        return result;
      } catch (error) {
        logger.error(`GraphQL availableGenres Error: ${error.message}`, {
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch available genres", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    relatedSeasons: async (_, { tmdbId, currentItemId }) => {
      logger.info(`GraphQL relatedSeasons: tmdbId=${tmdbId}, currentItemId=${currentItemId}`);

      // Generate cache key
      const cacheKey = unifiedCache.generateKey('graphql', 'relatedSeasons', { tmdbId, currentItemId });

      // Try to get from cache first
      const cachedResult = unifiedCache.get('graphql', cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for relatedSeasons: tmdbId=${tmdbId}`);
        return cachedResult;
      }

      try {
        // Search for all series and anime with the same TMDB ID, excluding the current item
        const [relatedSeries, relatedAnime] = await Promise.all([
          Series.find({
            'tmdb.id': tmdbId,
            _id: { $ne: new mongoose.Types.ObjectId(currentItemId) }
          }).sort({ season: 1 }),
          Anime.find({
            'tmdb.id': tmdbId,
            _id: { $ne: new mongoose.Types.ObjectId(currentItemId) }
          }).sort({ season: 1 })
        ]);

        // Combine and sort by season number
        const allRelated = [...relatedSeries, ...relatedAnime];

        // Sort by season number (convert to number for proper sorting)
        allRelated.sort((a, b) => {
          const seasonA = parseInt(a.season || '1');
          const seasonB = parseInt(b.season || '1');
          return seasonA - seasonB;
        });

        // Cache the result for 1 hour
        unifiedCache.set('graphql', cacheKey, allRelated, 3600000); // 1 hour

        logger.info(`Found ${allRelated.length} related seasons for TMDB ID ${tmdbId}`);
        return allRelated;
      } catch (error) {
        logger.error(`GraphQL relatedSeasons Error: ${error.message}`, {
          tmdbId,
          currentItemId,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to fetch related seasons", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },

    // Admin-specific queries
    databaseStats: async () => {
      try {
        const [moviesCount, seriesCount, animeCount, livetvCount] = await Promise.all([
          Movie.countDocuments(),
          Series.countDocuments(),
          Anime.countDocuments(),
          LiveTV.countDocuments()
        ]);

        return {
          movies: moviesCount,
          series: seriesCount,
          anime: animeCount,
          livetv: livetvCount,
          totalItems: moviesCount + seriesCount + animeCount + livetvCount
        };
      } catch (error) {
        logger.error('Error fetching database stats:', error);
        return {
          movies: 0,
          series: 0,
          anime: 0,
          livetv: 0,
          totalItems: 0
        };
      }
    },

    contentOverview: async () => {
      try {
        // Get recent content (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const [recentMovies, recentSeries, recentAnime, recentLiveTV] = await Promise.all([
          Movie.countDocuments({ updatedAt: { $gte: sevenDaysAgo } }),
          Series.countDocuments({ updatedAt: { $gte: sevenDaysAgo } }),
          Anime.countDocuments({ updatedAt: { $gte: sevenDaysAgo } }),
          LiveTV.countDocuments({ updatedAt: { $gte: sevenDaysAgo } })
        ]);

        return {
          recentlyAdded: recentMovies + recentSeries + recentAnime + recentLiveTV,
          trending: 0, // Placeholder
          mostWatched: 0, // Placeholder
          totalViews: 0 // Placeholder
        };
      } catch (error) {
        logger.error('Error fetching content overview:', error);
        return {
          recentlyAdded: 0,
          trending: 0,
          mostWatched: 0,
          totalViews: 0
        };
      }
    },

    displaySettings: async (_, { adminToken }) => {
      try {
        // Validate admin token
        const isValid = await Admin.validateToken(adminToken);
        if (!isValid) {
          throw new Error('Invalid admin token');
        }

        // Get display settings from config
        const gridItemsEnabled = await Config.getValue('GRID_ITEMS_ENABLED', 'true');

        return {
          gridItemsEnabled: gridItemsEnabled === 'true'
        };
      } catch (error) {
        logger.error(`Error fetching display settings: ${error.message}`);
        throw new Error('Failed to fetch display settings');
      }
    },

    stream: async (_, { itemId, type, streamId }) => {
      console.log('[DEBUG STREAM RESOLVER] Incoming args:', { itemId, type, streamId, typeofType: typeof type });
      logger.info(
        `GraphQL Stream: type=${type}, itemId=${itemId}, streamId=${streamId}`
      );
      const Model = {
        MOVIE: Movie,
        SERIES: Series,
        ANIME: Anime,
        LIVETV: LiveTV,
      }[type];
      if (!Model)
        throw new GraphQLError("Unsupported item type", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      if (
        !mongoose.Types.ObjectId.isValid(itemId) ||
        !mongoose.Types.ObjectId.isValid(streamId)
      ) {
        throw new GraphQLError("Invalid ID format", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      }

      try {
        const itemDoc = await Model.findById(itemId);
        if (!itemDoc)
          throw new GraphQLError("Item not found", {
            extensions: { code: "NOT_FOUND" },
          });
        let stream;
        let streamParentArrayPath;
        let specificEpisodeId = null;

        // Locate the stream subdocument
        if (type === "MOVIE" || type === "LIVETV") {
          stream = itemDoc.streamingUrls.id(streamId);
          streamParentArrayPath = "streamingUrls";
        } else if (type === "SERIES" || type === "ANIME") {
            // Search within episodes first
            for (const episode of itemDoc.episodes || []) {
                stream = episode.streamingUrls.id(streamId);
                if (stream) {
                streamParentArrayPath = `episodes`;
                specificEpisodeId = episode._id; // Store the episode's _id
                break;
                }
            }
            // If not found in episodes (e.g., Anime movie/OVA), check direct streamingUrls
            if (!stream && type === "ANIME") {
                stream = itemDoc.streamingUrls?.id(streamId);
                if (stream) {
                streamParentArrayPath = "streamingUrls";
                }
            }
        }

        if (!stream)
          throw new GraphQLError("Stream not found", {
            extensions: { code: "NOT_FOUND" },
          });

        const streamObj = stream.toObject();
        let sourceStreamUrl = streamObj.sourceStreamUrl;
        let size = streamObj.size;
        let streamType = streamObj.type;
        let method = streamObj.method;
        const lastChecked = streamObj.lastChecked
          ? new Date(streamObj.lastChecked).getTime()
          : 0;
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

        // Fetch/Re-fetch if no source URL or cache is older than 5 minutes
        if (!sourceStreamUrl || lastChecked < fiveMinutesAgo) {
          logger.info(
            `Fetching/Re-fetching source URL for stream ${streamId} (Provider: ${streamObj.provider})`
          );
          try {
            const result = await fetchSourceStreamUrl(
              streamObj.url,
              streamObj.provider
            );
            if (result && result.url) {
              logger.info(`Fetched source URL: ${result.url}`, {
                method: result.method,
              });
              sourceStreamUrl = result.url;
              size = result.size;
              streamType = result.type;
              method = result.method;
              const updateSet = { $set: {} };
              const streamObjectId = new mongoose.Types.ObjectId(streamId); // Ensure streamId is ObjectId
              let arrayFilters = [];
              let updatePathPrefix = "";

              // Determine the correct path prefix and array filters for the update
              if (streamParentArrayPath === "streamingUrls") {
                updatePathPrefix = "streamingUrls.$[streamElem]";
                arrayFilters = [{ "streamElem._id": streamObjectId }];
              } else if (
                streamParentArrayPath === "episodes" &&
                specificEpisodeId // Ensure we have the episode ID
              ) {
                updatePathPrefix =
                  "episodes.$[epElem].streamingUrls.$[streamElem]";
                arrayFilters = [
                  { "epElem._id": specificEpisodeId }, // Use the specific episode's _id
                  { "streamElem._id": streamObjectId },
                ];
              }

              if (updatePathPrefix) {
                  // Construct the $set object dynamically
                  updateSet.$set[`${updatePathPrefix}.sourceStreamUrl`] = sourceStreamUrl;
                  updateSet.$set[`${updatePathPrefix}.size`] = size;
                  updateSet.$set[`${updatePathPrefix}.type`] = streamType;
                  updateSet.$set[`${updatePathPrefix}.method`] = method;
                  updateSet.$set[`${updatePathPrefix}.lastChecked`] = new Date();

                  // Perform the update operation
                  Model.updateOne({ _id: itemId }, updateSet, { arrayFilters })
                      .then((res) =>
                      logger.info(`DB Update Result (Stream ${streamId}):`, {
                          matched: res.matchedCount,
                          modified: res.modifiedCount,
                      })
                      )
                      .catch((err) =>
                      logger.error(
                          `Failed DB Update (Stream ${streamId}): ${err.message}`
                      )
                      );
              } else {
                   logger.error(`Could not determine update path for stream ${streamId}`);
              }
              // Return immediately after successful fetch and update
              return {
                sourceStreamUrl,
                size: size || null,
                type: streamType || null,
                method: method || null
              };
            } else {
              logger.warn(
                `Failed fetch for stream ${streamId}. Returning original URL.`
              );
              // Keep original values but maybe default method to iframe if unsure
              sourceStreamUrl = streamObj.url;
              method = "iframe";
              return {
                sourceStreamUrl,
                size: size || null,
                type: streamType || null,
                method: method || null
              };
            }
          } catch (error) {
            logger.error(`Error fetching source URL for stream ${streamId}:`, error);
            // Fallback to original URL
            sourceStreamUrl = streamObj.url;
            method = "iframe";
            return {
              sourceStreamUrl,
              size: size || null,
              type: streamType || null,
              method: method || null
            };
          }
        } else {
          logger.info(`Using cached source URL for stream ${streamId}`);
        }
        // Return cached or previously fetched values
        return {
          sourceStreamUrl: sourceStreamUrl,
          size: size || null,
          type: streamType || null,
          method: method || null, // Return resolved or default method
        };
      } catch (error) {
        logger.error(`GraphQL Stream Error: ${error.message}`, {
          type,
          itemId,
          streamId,
          stack: error.stack,
        });
        throw new GraphQLError("Failed to get stream details", {
          extensions: {
            code: "INTERNAL_SERVER_ERROR",
            originalError: error.message,
          },
        });
      }
    },

    play: async (_, { type, id, ep, lang }) => {
      logger.info(
        `GraphQL Play resolver called: type=${type}, id=${id}, ep=${ep}, lang=${lang}`
      );
      const Model = {
        MOVIE: Movie,
        SERIES: Series,
        ANIME: Anime,
        LIVETV: LiveTV,
      }[type];
      if (!Model)
        throw new GraphQLError("Unsupported item type", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new GraphQLError("Invalid ID format", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      }
      const item = await Model.findById(id)
        .select( // Select only necessary fields for performance
          "streamingUrls episodes.episodeNumber episodes.streamingUrls title"
        )
        .lean(); // Use lean for plain JS object
      if (!item)
        throw new GraphQLError("Item not found", {
          extensions: { code: "NOT_FOUND" },
        });

      let stream;
      if (type === "MOVIE" || type === "LIVETV") {
        stream =
          (item.streamingUrls || []).find(
            (s) => s.isActive && (!lang || s.language === lang)
          ) || item.streamingUrls?.[0];
      } else if (type === "SERIES" || type === "ANIME") {
        const episode = ep
          ? item.episodes?.find((e) => String(e.episodeNumber) === String(ep))
          : item.episodes?.[0]; // Default to first episode if 'ep' not provided

        if (!episode && type === "ANIME") {
            // Fallback check for direct streamingUrls on Anime (e.g., movies/OVAs)
            stream =
                (item.streamingUrls || []).find(
                (s) => s.isActive && (!lang || s.language === lang)
                ) || item.streamingUrls?.[0];
        } else if (episode) {
          stream =
            (episode.streamingUrls || []).find(
              (s) => s.isActive && (!lang || s.language === lang) // Match language if provided
            ) || episode.streamingUrls?.[0]; // Fallback to first stream of the episode
        }

        // If still no stream after checking episode and direct URLs (for Anime)
        if (!stream && !episode && type !== "ANIME") {
             throw new GraphQLError(
                `Episode "${ep}" not found for ${type} ${id}`,
                { extensions: { code: "NOT_FOUND" } }
            );
        } else if (!stream) {
             // No stream found even if episode exists or it's an Anime without direct streams
              throw new GraphQLError(
                `No suitable stream found for ${type} ${id}` + (ep ? ` episode ${ep}` : '') + (lang ? ` language ${lang}` : ''),
                { extensions: { code: "NOT_FOUND" } }
            );
        }
      }

      // Final check if a stream was found for any type
      if (!stream)
        throw new GraphQLError(
          "No stream available for the selected criteria",
          { extensions: { code: "NOT_FOUND" } }
        );

      // Prioritize sourceStreamUrl if available (likely resolved via stream query)
      const bestUrl = stream.sourceStreamUrl || stream.url;
      logger.info(`GraphQL Play: Returning URL ${bestUrl} for ${type} ${id}`);
      return { url: bestUrl };
    },

    config: async (parent, args, context) => {
      logger.info("GraphQL Config query accessed");

      // Use direct database context instead of broken Mongoose Config model
      const { db } = context || {};

      if (db) {
        logger.info("Config resolver: Using direct database access");

        // Get all config values directly from database
        const [wiflixConfig, frenchAnimeConfig, witvConfig, tmdbConfig, geminiConfig, scrapingConfig] = await Promise.all([
          db.collection('config').findOne({ key: 'WIFLIX_BASE' }),
          db.collection('config').findOne({ key: 'FRENCH_ANIME_BASE' }),
          db.collection('config').findOne({ key: 'WITV_BASE' }),
          db.collection('config').findOne({ key: 'TMDB_API_KEY' }),
          db.collection('config').findOne({ key: 'GEMINI_API_KEY' }),
          db.collection('config').findOne({ key: 'SCRAPING_CONFIG' })
        ]);

        const wiflixBase = wiflixConfig?.value || WIFLIX_BASE || "wiflix-max.cam";
        const frenchAnimeBase = frenchAnimeConfig?.value || process.env.FRENCH_ANIME_BASE || "french-anime.com";
        const witvBase = witvConfig?.value || process.env.WITV_BASE || "witv.skin";
        const tmdbApiKey = tmdbConfig?.value || process.env.TMDB_API_KEY || null;
        const geminiApiKey = geminiConfig?.value || process.env.GEMINI_API_KEY || null;
        const scrapingConfigStr = scrapingConfig?.value;

        logger.info(`Config resolver: Retrieved from DB - tmdb: ${tmdbApiKey ? 'SET' : 'NULL'}, gemini: ${geminiApiKey ? 'SET' : 'NULL'}, scraping: ${scrapingConfigStr ? 'SET' : 'NULL'}`);

        // Parse scraping config if it exists
        let parsedScrapingConfig = null;
        if (scrapingConfigStr) {
          try {
            parsedScrapingConfig = JSON.parse(scrapingConfigStr);
          } catch (err) {
            logger.error(`Error parsing scraping config: ${err.message}`);
          }
        }

        return {
          tmdbApiKey,
          wiflixBase,
          frenchAnimeBase,
          witvBase,
          geminiApiKey,
          scrapingConfig: parsedScrapingConfig
        };
      } else {
        // Fallback to Config model if no db context
        logger.info("Config resolver: No db context, using Config model fallback");

        const wiflixBase = await Config.getValue("WIFLIX_BASE", WIFLIX_BASE || "wiflix-max.cam");
        const frenchAnimeBase = await Config.getValue("FRENCH_ANIME_BASE", process.env.FRENCH_ANIME_BASE || "french-anime.com");
        const witvBase = await Config.getValue("WITV_BASE", process.env.WITV_BASE || "witv.skin");
        const geminiApiKey = await Config.getValue("GEMINI_API_KEY", process.env.GEMINI_API_KEY || null);
        const scrapingConfigStr = await Config.getValue("SCRAPING_CONFIG", null);

        // Parse scraping config if it exists
        let parsedScrapingConfig = null;
        if (scrapingConfigStr) {
          try {
            parsedScrapingConfig = JSON.parse(scrapingConfigStr);
          } catch (err) {
            logger.error(`Error parsing scraping config: ${err.message}`);
          }
        }

        return {
          tmdbApiKey: process.env.TMDB_API_KEY || null,
          wiflixBase,
          frenchAnimeBase,
          witvBase,
          geminiApiKey,
          scrapingConfig: parsedScrapingConfig
        };
      }
    },

    duplicateDetailUrlPaths: async () => {
      logger.info("GraphQL duplicateDetailUrlPaths query accessed");
      try {
        const models = [
          { model: Movie, typename: "Movie" },
          { model: Series, typename: "Series" },
          { model: Anime, typename: "Anime" },
          { model: LiveTV, typename: "LiveTV" },
        ];
        const results = await Promise.all(
          models.map(async ({ model, typename }) =>
            model.aggregate([
              { $match: { detailUrlPath: { $ne: null, $ne: "" } } }, // Ensure path exists
              {
                $group: {
                  _id: "$detailUrlPath", // Group by the path
                  count: { $sum: 1 },
                  items: { $push: { id: "$_id", title: "$title" } }, // Collect IDs and titles
                },
              },
              { $match: { count: { $gt: 1 } } }, // Filter for groups with more than one item
              {
                $addFields: {
                  // Add __typename to each item in the items array
                  items: {
                    $map: {
                      input: "$items",
                      as: "item",
                      in: {
                        id: "$$item.id",
                        title: "$$item.title",
                        __typename: typename, // Add the correct type name
                      },
                    },
                  },
                },
              },
            ])
          )
        );
        // Flatten results from all models and map to the expected output structure
        const duplicates = results
          .flat()
          .map((group) => ({ detailUrlPath: group._id, items: group.items }));
        logger.info(
          `Found ${duplicates.length} detailUrlPaths with duplicates.`
        );
        return duplicates;
      } catch (error) {
        logger.error(
          `GraphQL duplicateDetailUrlPaths Error: ${error.message}`,
          { stack: error.stack }
        );
        throw new GraphQLError("Failed to find duplicate paths", {
          extensions: { code: "INTERNAL_SERVER_ERROR" },
        });
      }
    },
  }, // End Query

  // --- Interface Resolver ---
  Item: {
    __resolveType(obj) {
      // Determine type based on unique fields or structure
      if (obj.__typename) return obj.__typename; // If typename already set (e.g., from search mapping)

      // Check for specific model types
      if (obj.jikan) return "Anime"; // Jikan data is specific to Anime

      // Check for collection name if available
      if (obj.collection && typeof obj.collection === 'string') {
        if (obj.collection === 'movies') return "Movie";
        if (obj.collection === 'series') return "Series";
        if (obj.collection === 'animes') return "Anime";
        if (obj.collection === 'livetv') return "LiveTV";
      }

      // Check for episodes (Series or Anime)
      if (obj.episodes && Array.isArray(obj.episodes)) {
        // Could be Series or Anime with episodes, check for animeLanguage as differentiator
        if (obj.animeLanguage !== undefined) return "Anime";
        return "Series";
      }

      // If it has streamingUrls but no episodes/jikan
      if (obj.streamingUrls) {
        // Check for Anime type based on language
        if (obj.animeLanguage !== undefined) return "Anime";
        // Heuristic for LiveTV based on title
        if (obj.title?.match(/TV|News|Channel|Direct|Live/i)) return "LiveTV";
        // Default to Movie if none of the above match
        return "Movie";
      }

      // Last resort - try to determine by model-specific fields
      if (obj.season && !obj.episodes) return "Series";
      if (obj.cleanedTitle && !obj.episodes) return "Movie";

      logger.warn("Could not resolve type for item:", {
        id: obj._id || obj.id,
        title: obj.title,
      });

      // Default to Movie as fallback
      return "Movie";
    },
    // Generic thumbnail resolver (used by Series, Anime, LiveTV unless overridden)
    thumbnail: (parent) => {
        const baseUrl = getWiflixBaseUrl();

        // 1. Jikan (for Anime primarily) - Use higher quality image
        const jikanThumb = parent.jikan?.images?.jpg?.large_image_url || parent.jikan?.images?.jpg?.image_url;
        if (jikanThumb) return jikanThumb;

        // 2. TMDB Poster (Better Quality for Thumbnails)
        if (parent.tmdb?.poster_path) {
            return `https://image.tmdb.org/t/p/w342${parent.tmdb.poster_path}`;
        }

        // --- Scraped Images (Prioritize Detail Thumbnail for non-movies) ---

        // 3. Detail Thumbnail Path (thumbnailPath) - From detail scrape
        if (parent.thumbnailPath && typeof parent.thumbnailPath === 'string' && parent.thumbnailPath.startsWith('/')) {
             const fullUrl = baseUrl + parent.thumbnailPath;
            return `/proxy-image?url=${encodeURIComponent(fullUrl)}`;
        }
        // 4. Detail Thumbnail Full URL (thumbnail - fallback)
        if (parent.thumbnail && typeof parent.thumbnail === 'string' && parent.thumbnail.startsWith('http')) {
             return `/proxy-image?url=${encodeURIComponent(parent.thumbnail)}`;
        }

        // 5. List Image Path (imagePath) - From list scrape
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            const fullUrl = baseUrl + parent.imagePath;
            return `/proxy-image?url=${encodeURIComponent(fullUrl)}`;
        }
        // 6. List Image Full URL (image - fallback)
        if (parent.image && typeof parent.image === 'string' && parent.image.startsWith('http')) {
            return `/proxy-image?url=${encodeURIComponent(parent.image)}`;
        }

        // 7. Default
        return "/default-thumbnail.jpg";
    },
  },

  // --- Concrete Type Resolvers ---
  Movie: {
    __isTypeOf(obj) {
      // Check if this is a Movie type
      if (obj.__typename === 'Movie') return true;
      if (obj.collection === 'movies') return true;

      // Exclude LiveTV objects first
      if (obj.detailUrl && obj.detailUrl.includes('witv.skin')) return false;
      if (obj.detailUrlPath && obj.detailUrlPath.includes('/chaines-live/')) return false;

      // Movie has streamingUrls but no episodes
      if (obj.streamingUrls && (!obj.episodes || !obj.episodes.length) && !obj.jikan && !obj.animeLanguage) return true;
      return false;
    },
    id: (parent) => parent._id?.toString() || parent.id,
    title: (parent) => parent.title || "Untitled",
    displayTitle: (parent) => parent.tmdb?.title || parent.title || "Untitled",
    detailUrl: (parent) => { // Resolve using path first
        const baseUrl = getWiflixBaseUrl();
        if (parent.detailUrlPath && typeof parent.detailUrlPath === 'string' && parent.detailUrlPath.startsWith('/')) {
            return baseUrl + parent.detailUrlPath;
        }
        return parent.detailUrl || ''; // Fallback
    },
    image: (parent) => { // Resolve using path first (List Image)
        const baseUrl = getWiflixBaseUrl();
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            return baseUrl + parent.imagePath;
        }
        return parent.image || null; // Fallback
    },
    // Specific Movie Thumbnail logic - Overrides Item.thumbnail
    thumbnail: (parent) => {
        const baseUrl = getWiflixBaseUrl();

        // 1. TMDB Poster (Better Quality for Thumbnails)
        if (parent.tmdb?.poster_path) {
            return `https://image.tmdb.org/t/p/w342${parent.tmdb.poster_path}`;
        }

        // 2. List Image Path (imagePath)
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            const fullUrl = baseUrl + parent.imagePath;
            return `/proxy-image?url=${encodeURIComponent(fullUrl)}`;
        }
        // 3. List Image Full URL (image - fallback)
        if (parent.image && typeof parent.image === 'string' && parent.image.startsWith('http')) {
            return `/proxy-image?url=${encodeURIComponent(parent.image)}`;
        }

        // 4. Detail Thumbnail Path (thumbnailPath)
        if (parent.thumbnailPath && typeof parent.thumbnailPath === 'string' && parent.thumbnailPath.startsWith('/')) {
            const fullUrl = baseUrl + parent.thumbnailPath;
            return `/proxy-image?url=${encodeURIComponent(fullUrl)}`;
        }
        // 5. Detail Thumbnail Full URL (thumbnail - fallback)
        if (parent.thumbnail && typeof parent.thumbnail === 'string' && parent.thumbnail.startsWith('http')) {
            return `/proxy-image?url=${encodeURIComponent(parent.thumbnail)}`;
        }

        // 6. Default
        return "/default-thumbnail.jpg";
    },
    streamingUrls: (parent) => parent.streamingUrls || [],
    metadata: (parent) => parent.metadata || {},
    tmdb: (parent) => parent.tmdb || null,
    // detailUrlPath, imagePath, thumbnailPath are used internally, not exposed directly unless needed
  },
  Series: {
    __isTypeOf(obj) {
      // Check if this is a Series type
      if (obj.__typename === 'Series') return true;
      if (obj.collection === 'series') return true;

      // Exclude LiveTV objects first
      if (obj.detailUrl && obj.detailUrl.includes('witv.skin')) return false;
      if (obj.detailUrlPath && obj.detailUrlPath.includes('/chaines-live/')) return false;

      // Series has episodes but no jikan or animeLanguage
      if (obj.episodes && Array.isArray(obj.episodes) && !obj.jikan && obj.animeLanguage === undefined) return true;
      return false;
    },
    id: (parent) => parent._id?.toString() || parent.id,
    title: (parent) => parent.title || "Untitled",
    displayTitle: (parent) => parent.tmdb?.title || parent.title || "Untitled",
    detailUrl: (parent) => { // Resolve using path first
        const baseUrl = getWiflixBaseUrl();
        if (parent.detailUrlPath && typeof parent.detailUrlPath === 'string' && parent.detailUrlPath.startsWith('/')) {
            return baseUrl + parent.detailUrlPath;
        }
        return parent.detailUrl || ''; // Fallback
    },
     image: (parent) => { // Resolve using path first (List Image)
        const baseUrl = getWiflixBaseUrl();
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            return baseUrl + parent.imagePath;
        }
        return parent.image || null; // Fallback
    },

    // ADD THIS thumbnail RESOLVER FOR SERIES (Copied from Movie logic)
    thumbnail: (parent) => {
        const baseUrl = getWiflixBaseUrl();

        // 1. TMDB Poster (Better Quality for Thumbnails)
        if (parent.tmdb?.poster_path) {
            return `https://image.tmdb.org/t/p/w342${parent.tmdb.poster_path}`;
        }

        // 2. List Image Path (imagePath)
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            const fullUrl = baseUrl + parent.imagePath;
            return `/proxy-image?url=${encodeURIComponent(fullUrl)}`;
        }
        // 3. List Image Full URL (image - fallback)
        if (parent.image && typeof parent.image === 'string' && parent.image.startsWith('http')) {
            return `/proxy-image?url=${encodeURIComponent(parent.image)}`;
        }

        // 4. Detail Thumbnail Path (thumbnailPath)
        if (parent.thumbnailPath && typeof parent.thumbnailPath === 'string' && parent.thumbnailPath.startsWith('/')) {
            const fullUrl = baseUrl + parent.thumbnailPath;
            return `/proxy-image?url=${encodeURIComponent(fullUrl)}`;
        }
        // 5. Detail Thumbnail Full URL (thumbnail - fallback)
        if (parent.thumbnail && typeof parent.thumbnail === 'string' && parent.thumbnail.startsWith('http')) {
            return `/proxy-image?url=${encodeURIComponent(parent.thumbnail)}`;
        }

        // 6. Default
        return "/default-thumbnail.jpg";
    },
    // END OF ADDED thumbnail RESOLVER

    episodes: (parent) => parent.episodes || [],
    metadata: (parent) => parent.metadata || {},
    tmdb: (parent) => parent.tmdb || null,
    tmdbSeason: (parent) => parent.tmdbSeason || null,
    tmdbSeasons: (parent) => parent.tmdbSeasons || [],
    season: (parent) => parent.season || "1",
  },
  Anime: {
    __isTypeOf(obj) {
      // Check if this is an Anime type
      if (obj.__typename === 'Anime') return true;
      if (obj.collection === 'animes') return true;

      // Exclude LiveTV objects first
      if (obj.detailUrl && obj.detailUrl.includes('witv.skin')) return false;
      if (obj.detailUrlPath && obj.detailUrlPath.includes('/chaines-live/')) return false;

      // Anime has jikan data or animeLanguage
      if (obj.jikan || obj.animeLanguage !== undefined) return true;
      return false;
    },
    id: (parent) => parent._id?.toString() || parent.id,
    title: (parent) => parent.title || "Untitled",
    displayTitle: (parent) => // Prioritize Jikan, then TMDB, then scraped title
      parent.jikan?.title?.default ||
      parent.tmdb?.title ||
      parent.title ||
      "Untitled",
    detailUrl: (parent) => { // Resolve using path first
        const baseUrl = getWiflixBaseUrl();
        if (parent.detailUrlPath && typeof parent.detailUrlPath === 'string' && parent.detailUrlPath.startsWith('/')) {
            return baseUrl + parent.detailUrlPath;
        }
        return parent.detailUrl || ''; // Fallback
    },
     image: (parent) => { // Resolve using path first (List Image)
        const baseUrl = getWiflixBaseUrl();
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            return baseUrl + parent.imagePath;
        }
        return parent.image || null; // Fallback
    },
     // thumbnail will use Item.thumbnail resolver by default
    episodes: (parent) => parent.episodes || [], // For episodic Anime
    streamingUrls: (parent) => parent.streamingUrls || [], // For Anime movies/OVAs
    animeLanguage: (parent) => parent.animeLanguage || "unknown",
    metadata: (parent) => parent.metadata || {},
    tmdb: (parent) => parent.tmdb || null,
    tmdbSeason: (parent) => parent.tmdbSeason || null,
    tmdbSeasons: (parent) => parent.tmdbSeasons || [],
    jikan: (parent) => parent.jikan || null,
    season: (parent) => parent.season || "1",
  },
  LiveTV: {
    __isTypeOf(obj) {
      // Most explicit check first - if __typename is set
      if (obj.__typename === 'LiveTV') return true;

      // Check for witv.skin URLs (most reliable indicator)
      if (obj.detailUrl && obj.detailUrl.includes('witv.skin')) return true;
      if (obj.detailUrlPath && obj.detailUrlPath.includes('/chaines-live/')) return true;

      // Check collection name if available
      if (obj.collection === 'livetv') return true;

      return false;
    },
    id: (parent) => parent._id?.toString() || parent.id,
    title: (parent) => parent.title || "Untitled",
    displayTitle: (parent) => parent.title || "Untitled", // LiveTV usually doesn't have TMDB title
     detailUrl: (parent) => { // Resolve using path first
        const baseUrl = getWiflixBaseUrl();
        if (parent.detailUrlPath && typeof parent.detailUrlPath === 'string' && parent.detailUrlPath.startsWith('/')) {
            return baseUrl + parent.detailUrlPath;
        }
        // LiveTV might not always have a detailUrl from Wiflix structure
        return parent.detailUrl || ''; // Fallback or empty
    },
     image: (parent) => { // Resolve using path first (List Image)
        const baseUrl = getWiflixBaseUrl();
        if (parent.imagePath && typeof parent.imagePath === 'string' && parent.imagePath.startsWith('/')) {
            return baseUrl + parent.imagePath;
        }
         // LiveTV might use a different image source or the detail thumbnail
        return parent.image || null; // Fallback
    },
    // thumbnail will use Item.thumbnail resolver by default
    streamingUrls: (parent) => parent.streamingUrls || [],
    metadata: (parent) => parent.metadata || {}, // Often minimal for LiveTV
    tmdb: (parent) => parent.tmdb || null, // Usually null for LiveTV
  },

  // --- Other Type Resolvers ---
  StreamingUrl: {
    id: (parent) => parent._id?.toString() || parent.id, // Ensure ID is resolved
    language: (parent) => parent.language || "unknown",
    lastChecked: (parent) =>
      parent.lastChecked ? parent.lastChecked.toISOString() : null,
    provider: (parent) => parent.provider || "unknown",
    size: (parent) => parent.size || null,
    type: (parent) => parent.type || null,
    method: (parent) => parent.method || null, // Ensure method is resolved
    url: (parent) => parent.url || "", // Original scraped URL
    isActive: (parent) =>
      parent.isActive !== undefined ? parent.isActive : true, // Default to true if not set
    // sourceStreamUrl is resolved by the 'stream' query, not directly here
  },

  // Interface resolver for Item
  Item: {
    __resolveType(obj, context, info) {
      // Check for explicit __typename first
      if (obj.__typename) {
        return obj.__typename;
      }

      // Determine type based on object properties
      if (obj.animeLanguage !== undefined || obj.jikan) {
        return 'Anime';
      }
      if (obj.episodes && Array.isArray(obj.episodes) && obj.episodes.length > 0) {
        return 'Series';
      }
      if (obj.detailUrl && obj.detailUrl.includes('witv')) {
        return 'LiveTV';
      }
      if (obj.streamingUrls && Array.isArray(obj.streamingUrls)) {
        return 'Movie';
      }

      // Default fallback
      return 'Movie';
    }
  },

  Episode: {
    episodeNumber: (parent) => String(parent.episodeNumber || "N/A"), // Ensure string
    season: (parent) => String(parent.season || "1"), // Ensure string
    language: (parent) => parent.language || "unknown",
    streamingUrls: (parent) => parent.streamingUrls || [],
  },
  TMDB: {
    genres: (parent) => { // Ensure consistent string array output
      if (!parent.genres) return [];
      if (parent.genres.length === 0) return [];
      // Handle array of strings
      if (typeof parent.genres[0] === "string") return parent.genres;
      // Handle array of objects { id, name }
      if (
        typeof parent.genres[0] === "object" &&
        parent.genres[0]?.name !== undefined
      ) {
        return parent.genres.map((g) => g.name).filter(Boolean);
      }
      return []; // Fallback for unexpected format
    },
    cast: (parent) => parent.cast || [],
    crew: (parent) => parent.crew || [],
     // Ensure other TMDB fields resolve correctly or return null/defaults
    id: (parent) => parent.id || null,
    title: (parent) => parent.title || null,
    overview: (parent) => parent.overview || null,
    release_date: (parent) => parent.release_date || null,
    poster_path: (parent) => parent.poster_path || null,
    vote_average: (parent) => parent.vote_average || null,
  },
  JikanAired: {
    from: (parent) => (parent.from ? new Date(parent.from).toISOString() : null), // Ensure ISO string
    to: (parent) => (parent.to ? new Date(parent.to).toISOString() : null), // Ensure ISO string
    string: (parent) => parent.string || null,
  },
  Jikan: {
     // Ensure all Jikan fields resolve correctly or return null/defaults
    mal_id: (parent) => parent.mal_id || null,
    title: (parent) => parent.title || null,
    type: (parent) => parent.type || null,
    source: (parent) => parent.source || null,
    episodes: (parent) => parent.episodes || null,
    status: (parent) => parent.status || null,
    airing: (parent) => parent.airing !== undefined ? parent.airing : null,
    aired: (parent) => parent.aired || null,
    duration: (parent) => parent.duration || null,
    rating: (parent) => parent.rating || null,
    score: (parent) => parent.score || null,
    scored_by: (parent) => parent.scored_by || null,
    rank: (parent) => parent.rank || null,
    popularity: (parent) => parent.popularity || null,
    members: (parent) => parent.members || null,
    favorites: (parent) => parent.favorites || null,
    synopsis: (parent) => parent.synopsis || null,
    background: (parent) => parent.background || null,
    season: (parent) => parent.season || null,
    year: (parent) => parent.year || null,
    studios: (parent) => parent.studios || [],
    genres: (parent) => parent.genres || [],
    themes: (parent) => parent.themes || [],
    demographics: (parent) => parent.demographics || [],
    images: (parent) => parent.images || null,
    trailer: (parent) => parent.trailer || null,
    approved: (parent) => parent.approved !== undefined ? parent.approved : null,
    relations: (parent) => parent.relations || [],
    streaming_platforms: (parent) => parent.streaming_platforms || [],
    lastUpdated: (parent) => // Ensure ISO string
      parent.lastUpdated ? new Date(parent.lastUpdated).toISOString() : null,
  },
   JikanTitle: {
    default: (parent) => parent.default || null,
    english: (parent) => parent.english || null,
    japanese: (parent) => parent.japanese || null,
    synonyms: (parent) => parent.synonyms || []
  },
  JikanRelation: {
      relation: (parent) => parent.relation || null,
      entry: (parent) => parent.entry || []
  },
  // Add resolvers for other Jikan sub-types if needed, ensuring defaults
   JikanStudio: { mal_id: (p) => p.mal_id, name: (p) => p.name },
   JikanGenre: { mal_id: (p) => p.mal_id, name: (p) => p.name, type: (p) => p.type },
   JikanTheme: { mal_id: (p) => p.mal_id, name: (p) => p.name, type: (p) => p.type },
   JikanDemographic: { mal_id: (p) => p.mal_id, name: (p) => p.name, type: (p) => p.type },
   JikanRelationEntry: { mal_id: (p) => p.mal_id, type: (p) => p.type, name: (p) => p.name, url: (p) => p.url },
   JikanStreamingPlatform: { name: (p) => p.name, url: (p) => p.url },
   JikanImages: { jpg: (p) => p.jpg, webp: (p) => p.webp },
   JikanImageSet: { image_url: (p) => p.image_url, small_image_url: (p) => p.small_image_url, large_image_url: (p) => p.large_image_url },
   JikanTrailer: { youtube_id: (p) => p.youtube_id, url: (p) => p.url, embed_url: (p) => p.embed_url },

  Metadata: {
    actors: (parent) => parent.actors || [],
    synopsis: (parent) => parent.synopsis || null,
    year: (parent) => parent.year || null,
    genre: (parent) => parent.genre || null,
    origin: (parent) => parent.origin || null,
    creator: (parent) => parent.creator || null,
    duration: (parent) => parent.duration || null,
  },
   // Add resolvers for TmdbSeason and TmdbEpisode
  TmdbSeason: {
    air_date: (parent) => parent.air_date || null,
    tmdb_season_id: (parent) => parent.tmdb_season_id || null,
    name: (parent) => parent.name || null,
    overview: (parent) => parent.overview || null,
    poster_path: (parent) => parent.poster_path || null,
    season_number: (parent) => parent.season_number || null,
    vote_average: (parent) => parent.vote_average || null,
    episodes: (parent) => parent.episodes || [],
  },
  TmdbEpisode: {
    air_date: (parent) => parent.air_date || null,
    episode_number: (parent) => parent.episode_number || null,
    tmdb_episode_id: (parent) => parent.tmdb_episode_id || null,
    name: (parent) => parent.name || null,
    overview: (parent) => parent.overview || null,
    still_path: (parent) => parent.still_path || null,
    vote_average: (parent) => parent.vote_average || null,
  },
  // Add resolvers for StreamURL and PlayURL if needed (usually simple pass-through)
    StreamURL: {
        sourceStreamUrl: (parent) => parent.sourceStreamUrl || null,
        size: (parent) => parent.size || null,
        type: (parent) => parent.type || null,
        method: (parent) => parent.method || null,
    },
    PlayURL: {
        url: (parent) => parent.url || '',
    },
    // Resolver for DuplicateDetailUrlPath (items field needs mapping if not done in query)
    DuplicateDetailUrlPath: {
        detailUrlPath: (parent) => parent.detailUrlPath,
        // Items should already be mapped with __typename in the query resolver
        items: (parent) => parent.items || [],
    },

  // Admin Mutations
  Mutation: {
    adminLogin: async (_, { adminKey }, context) => {
      try {
        // Validate admin key
        const expectedKey = process.env.ADMIN_KEY;
        if (!expectedKey || adminKey !== expectedKey) {
          return {
            success: false,
            token: null,
            message: 'Invalid admin key'
          };
        }

        // Generate token (same format as fastifyResolvers)
        const token = Buffer.from(`${adminKey}:${Date.now()}`).toString('base64');

        // Use direct database access instead of Mongoose model
        const { db } = context || {};

        if (db) {
          // Store token in admin collection (matches fastifyResolvers approach)
          await db.collection('admin').updateOne(
            { type: 'session' },
            {
              $set: {
                token,
                createdAt: new Date(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
              }
            },
            { upsert: true }
          );
        } else {
          // Fallback to Mongoose model if no db context
          await Admin.create({ token });
        }

        // Initialize default config values if they don't exist
        await Config.initializeDefaults({
          "WIFLIX_BASE": WIFLIX_BASE || "wiflix-max.cam",
          "FRENCH_ANIME_BASE": process.env.FRENCH_ANIME_BASE || "french-anime.com",
          "WITV_BASE": process.env.WITV_BASE || "witv.skin",
          "GRID_ITEMS_ENABLED": "true"
        });

        logger.info(`Admin login successful, token generated: ${token.substring(0, 20)}...`);

        return {
          success: true,
          token,
          message: "Admin login successful"
        };
      } catch (error) {
        logger.error(`Admin login error: ${error.message}`);
        return {
          success: false,
          token: null,
          message: "Invalid admin key"
        };
      }
    },

    updateBaseUrl: async (_, { key, value, adminToken }) => {
      try {
        // Validate admin token
        const isValid = await Admin.validateToken(adminToken);
        if (!isValid) {
          return {
            success: false,
            message: "Invalid admin token",
            key: null,
            value: null
          };
        }

        // Validate key
        const validKeys = ["WIFLIX_BASE", "FRENCH_ANIME_BASE", "WITV_BASE"];
        if (!validKeys.includes(key)) {
          return {
            success: false,
            message: `Invalid key. Must be one of: ${validKeys.join(", ")}`,
            key,
            value: null
          };
        }

        // Update config in database
        await Config.setValue(key, value);

        // Update in-memory constant if it's WIFLIX_BASE
        if (key === "WIFLIX_BASE") {
          require("./src/config/constants").WIFLIX_BASE = value;
        }

        logger.info(`Updated ${key} to ${value}`);

        return {
          success: true,
          message: `Successfully updated ${key} to ${value}`,
          key,
          value
        };
      } catch (error) {
        logger.error(`Error updating base URL: ${error.message}`);
        return {
          success: false,
          message: `Error: ${error.message}`,
          key,
          value
        };
      }
    },

    updateDisplaySettings: async (_, { gridItemsEnabled, adminToken }) => {
      try {
        // Validate admin token
        const isValid = await Admin.validateToken(adminToken);
        if (!isValid) {
          return {
            success: false,
            message: "Invalid admin token",
            key: null,
            value: null
          };
        }

        // Update display settings in database
        await Config.setValue('GRID_ITEMS_ENABLED', gridItemsEnabled.toString());

        logger.info(`Updated GRID_ITEMS_ENABLED to ${gridItemsEnabled}`);

        return {
          success: true,
          message: `Grid items display ${gridItemsEnabled ? 'enabled' : 'disabled'} successfully`,
          key: 'GRID_ITEMS_ENABLED',
          value: gridItemsEnabled.toString()
        };
      } catch (error) {
        logger.error(`Error updating display settings: ${error.message}`);
        return {
          success: false,
          message: `Error: ${error.message}`,
          key: 'GRID_ITEMS_ENABLED',
          value: null
        };
      }
    },

    deleteItem: async (_, { id, type, adminToken }) => {
      try {
        // Validate admin token
        const isValid = await Admin.validateToken(adminToken);
        if (!isValid) {
          return {
            success: false,
            message: "Invalid admin token",
            deletedId: null,
            type: null
          };
        }

        // Check if ID is valid
        if (!mongoose.Types.ObjectId.isValid(id)) {
          return {
            success: false,
            message: "Invalid ID format",
            deletedId: null,
            type: null
          };
        }

        // Get the appropriate model
        const Model = {
          MOVIE: Movie,
          SERIES: Series,
          ANIME: Anime,
          LIVETV: LiveTV,
        }[type];

        if (!Model) {
          return {
            success: false,
            message: "Unsupported item type",
            deletedId: null,
            type: null
          };
        }

        // Delete the item
        const result = await Model.findByIdAndDelete(id);

        if (!result) {
          return {
            success: false,
            message: "Item not found",
            deletedId: null,
            type: null
          };
        }

        logger.info(`Admin deleted ${type} with ID: ${id}`);

        return {
          success: true,
          message: "Item deleted successfully",
          deletedId: id,
          type: type
        };
      } catch (error) {
        logger.error(`Delete item error: ${error.message}`, {
          id,
          type,
          stack: error.stack
        });

        return {
          success: false,
          message: `Error deleting item: ${error.message}`,
          deletedId: null,
          type: null
        };
      }
    },

    scrapeItem: async (_, { id, type, adminToken }) => {
      try {
        // Validate admin token
        const isValid = await Admin.validateToken(adminToken);
        if (!isValid) {
          return {
            success: false,
            message: "Invalid admin token",
            itemId: null,
            type: null,
            logId: null
          };
        }

        // Check if ID is valid
        if (!mongoose.Types.ObjectId.isValid(id)) {
          return {
            success: false,
            message: "Invalid ID format",
            itemId: null,
            type: null,
            logId: null
          };
        }

        // Get the appropriate model
        const Model = {
          MOVIE: Movie,
          SERIES: Series,
          ANIME: Anime,
          LIVETV: LiveTV,
        }[type];

        if (!Model) {
          return {
            success: false,
            message: "Unsupported item type",
            itemId: null,
            type: null,
            logId: null
          };
        }

        // Find the item
        const item = await Model.findById(id);

        if (!item) {
          return {
            success: false,
            message: "Item not found",
            itemId: null,
            type: null,
            logId: null
          };
        }

        // Generate a unique log ID for this scrape operation
        const logId = crypto.randomUUID();

        // Start the scraping process in the background
        process.nextTick(async () => {
          try {
            // Log to both file and WebSocket
            logger.info(`[${logId}] Starting scrape for ${type} with ID: ${id}`, {
              title: item.title,
              detailUrl: item.detailUrl
            });
            sendLogToSubscribers(logId, 'info', `Starting scrape for ${type} with ID: ${id}`, {
              title: item.title,
              detailUrl: item.detailUrl
            });

            // Different scraping logic based on item type
            let details;
            if (type === 'ANIME') {
              logger.info(`[${logId}] Scraping anime details from: ${item.detailUrl}`);
              sendLogToSubscribers(logId, 'info', `Scraping anime details from: ${item.detailUrl}`);

              // Log the title we're scraping
              sendLogToSubscribers(logId, 'info', `Scraping anime: ${item.title}`);

              details = await scrapeFrenchAnimeDetail(item.detailUrl);

              // Log the results
              if (details.episodes && details.episodes.length > 0) {
                sendLogToSubscribers(logId, 'info', `Found ${details.episodes.length} episodes`);

                // Log episode details
                const episodeSample = details.episodes.slice(0, 3);
                const episodeInfo = episodeSample.map(ep =>
                  `S${ep.season || '1'}:E${ep.episodeNumber} - ${ep.streamingUrls?.length || 0} streams`
                ).join(', ');

                sendLogToSubscribers(logId, 'info', `Episode sample: ${episodeInfo}${details.episodes.length > 3 ? '...' : ''}`);
              } else if (details.streamingUrls && details.streamingUrls.length > 0) {
                sendLogToSubscribers(logId, 'info', `Found ${details.streamingUrls.length} streaming URLs`);

                // Log streaming URL details
                const streamSample = details.streamingUrls.slice(0, 3);
                const streamInfo = streamSample.map(s => s.type || 'unknown').join(', ');
                sendLogToSubscribers(logId, 'info', `Stream types: ${streamInfo}${details.streamingUrls.length > 3 ? '...' : ''}`);
              } else {
                sendLogToSubscribers(logId, 'warn', `No episodes or streaming URLs found`);
              }

              // Log metadata if available
              if (details.metadata) {
                const metaInfo = [];
                if (details.metadata.year) metaInfo.push(`Year: ${details.metadata.year}`);
                if (details.metadata.genre) metaInfo.push(`Genre: ${details.metadata.genre}`);
                if (details.metadata.synopsis) metaInfo.push(`Synopsis available`);

                if (metaInfo.length > 0) {
                  sendLogToSubscribers(logId, 'info', `Metadata: ${metaInfo.join(', ')}`);
                }
              }

            } else if (type === 'MOVIE' || type === 'SERIES') {
              logger.info(`[${logId}] Scraping Wiflix details from: ${item.detailUrl}`);
              sendLogToSubscribers(logId, 'info', `Scraping Wiflix details from: ${item.detailUrl}`);

              // Log the title we're scraping
              sendLogToSubscribers(logId, 'info', `Scraping ${type.toLowerCase()}: ${item.title}`);

              details = await scrapeWiflixDetail(item.detailUrl);

              // Log the results
              if (type === 'MOVIE' && details.streamingUrls) {
                sendLogToSubscribers(logId, 'info', `Found ${details.streamingUrls.length} streaming URLs`);

                // Log streaming URL details
                const streamSample = details.streamingUrls.slice(0, 3);
                const streamInfo = streamSample.map(s => s.type || 'unknown').join(', ');
                sendLogToSubscribers(logId, 'info', `Stream types: ${streamInfo}${details.streamingUrls.length > 3 ? '...' : ''}`);
              } else if (type === 'SERIES' && details.episodes) {
                sendLogToSubscribers(logId, 'info', `Found ${details.episodes.length} episodes`);

                // Log episode details
                const episodeSample = details.episodes.slice(0, 3);
                const episodeInfo = episodeSample.map(ep =>
                  `S${ep.season || '1'}:E${ep.episodeNumber} - ${ep.streamingUrls?.length || 0} streams`
                ).join(', ');

                sendLogToSubscribers(logId, 'info', `Episode sample: ${episodeInfo}${details.episodes.length > 3 ? '...' : ''}`);
              } else {
                sendLogToSubscribers(logId, 'warn', `No ${type === 'MOVIE' ? 'streaming URLs' : 'episodes'} found`);
              }

              // Log metadata if available
              if (details.metadata) {
                const metaInfo = [];
                if (details.metadata.year) metaInfo.push(`Year: ${details.metadata.year}`);
                if (details.metadata.genre) metaInfo.push(`Genre: ${details.metadata.genre}`);
                if (details.metadata.synopsis) metaInfo.push(`Synopsis available`);

                if (metaInfo.length > 0) {
                  sendLogToSubscribers(logId, 'info', `Metadata: ${metaInfo.join(', ')}`);
                }
              }

            } else {
              logger.info(`[${logId}] LiveTV scraping not supported`);
              sendLogToSubscribers(logId, 'info', `LiveTV scraping not supported`);
              details = { streamingUrls: item.streamingUrls || [] };
            }

            // Combine the existing item with the new details
            const combinedItem = { ...item.toObject(), ...details };

            // Enrich the item with TMDB/Jikan data
            logger.info(`[${logId}] Enriching ${type} with TMDB/Jikan data`);
            sendLogToSubscribers(logId, 'info', `Enriching ${type} with TMDB/Jikan data`);

            // Store the original state to compare after enrichment
            const hasTmdbBefore = !!combinedItem.tmdb?.id;
            const hasJikanBefore = !!combinedItem.jikan?.mal_id;
            const tmdbSeasonsCountBefore = combinedItem.tmdbSeasons?.length || 0;
            const jikanSeasonsBefore = combinedItem.jikan?.seasons?.length || 0;

            // Perform the enrichment
            const enriched = await enrichItemWithOptions(combinedItem, type.toLowerCase(), {
              fetchSeasons: true,
              useAdvanced: process.env.USE_ADVANCED_ENRICHMENT === 'true'
            });

            // Log the enrichment results
            if (enriched.tmdb?.id) {
              const isNew = !hasTmdbBefore;
              sendLogToSubscribers(logId, 'info', `${isNew ? 'Added' : 'Updated'} TMDB data: ID ${enriched.tmdb.id}`);

              if (enriched.tmdb.title || enriched.tmdb.name) {
                sendLogToSubscribers(logId, 'info', `TMDB title: ${enriched.tmdb.title || enriched.tmdb.name}`);
              }

              if (enriched.tmdbSeasons && enriched.tmdbSeasons.length > 0) {
                const newSeasons = enriched.tmdbSeasons.length - tmdbSeasonsCountBefore;
                sendLogToSubscribers(logId, 'info', `TMDB seasons: ${enriched.tmdbSeasons.length}${newSeasons > 0 ? ` (${newSeasons} new)` : ''}`);

                // Log some season details
                const seasonSample = enriched.tmdbSeasons.slice(0, 3);
                const seasonInfo = seasonSample.map(s =>
                  `S${s.season_number}: ${s.episodes?.length || 0} episodes`
                ).join(', ');

                if (seasonInfo) {
                  sendLogToSubscribers(logId, 'info', `TMDB season details: ${seasonInfo}${enriched.tmdbSeasons.length > 3 ? '...' : ''}`);
                }
              }
            }

            if (enriched.jikan?.mal_id) {
              const isNew = !hasJikanBefore;
              sendLogToSubscribers(logId, 'info', `${isNew ? 'Added' : 'Updated'} Jikan data: ID ${enriched.jikan.mal_id}`);

              if (enriched.jikan.title) {
                sendLogToSubscribers(logId, 'info', `Jikan title: ${enriched.jikan.title}`);
              }

              if (enriched.jikan.seasons && enriched.jikan.seasons.length > 0) {
                const newSeasons = enriched.jikan.seasons.length - jikanSeasonsBefore;
                sendLogToSubscribers(logId, 'info', `Jikan seasons: ${enriched.jikan.seasons.length}${newSeasons > 0 ? ` (${newSeasons} new)` : ''}`);
              }

              // Log some additional Jikan data
              const jikanInfo = [];
              if (enriched.jikan.score) jikanInfo.push(`Score: ${enriched.jikan.score}`);
              if (enriched.jikan.episodes) jikanInfo.push(`Episodes: ${enriched.jikan.episodes}`);
              if (enriched.jikan.status) jikanInfo.push(`Status: ${enriched.jikan.status}`);

              if (jikanInfo.length > 0) {
                sendLogToSubscribers(logId, 'info', `Jikan details: ${jikanInfo.join(', ')}`);
              }
            }

            if (!enriched.tmdb?.id && !enriched.jikan?.mal_id) {
              sendLogToSubscribers(logId, 'warn', `No TMDB or Jikan data found for ${type.toLowerCase()}: ${combinedItem.title}`);
            }

            // Update the item in the database
            logger.info(`[${logId}] Updating ${type} in database`);
            sendLogToSubscribers(logId, 'info', `Updating ${type} in database`);
            await Model.findByIdAndUpdate(id, enriched, { new: true });

            logger.info(`[${logId}] Scrape completed successfully for ${type} with ID: ${id}`);
            sendLogToSubscribers(logId, 'success', `Scrape completed successfully for ${type} with ID: ${id}`);
          } catch (error) {
            logger.error(`[${logId}] Error during scrape: ${error.message}`, {
              id,
              type,
              stack: error.stack
            });
            sendLogToSubscribers(logId, 'error', `Error during scrape: ${error.message}`);
          }
        });

        return {
          success: true,
          message: "Scrape operation started",
          itemId: id,
          type: type,
          logId: logId
        };
      } catch (error) {
        logger.error(`Scrape item error: ${error.message}`, {
          id,
          type,
          stack: error.stack
        });

        return {
          success: false,
          message: `Error starting scrape: ${error.message}`,
          itemId: null,
          type: null,
          logId: null
        };
      }
    },

    scrapeUrlManually: async (_, { url, type, adminToken }) => {
      try {
        // Validate admin token
        const isValid = await Admin.validateToken(adminToken);
        if (!isValid) {
          return {
            success: false,
            message: "Invalid admin token",
            url: null,
            type: null,
            logId: null
          };
        }

        // Validate URL
        if (!url || typeof url !== 'string') {
          return {
            success: false,
            message: "Invalid URL",
            url: null,
            type: null,
            logId: null
          };
        }

        // Validate type
        const validTypes = ['MOVIE', 'SERIES', 'ANIME', 'LIVETV'];
        if (!validTypes.includes(type)) {
          return {
            success: false,
            message: "Unsupported item type",
            url: null,
            type: null,
            logId: null
          };
        }

        // Generate a unique log ID for this scrape operation
        const logId = crypto.randomUUID();

        // Start the scraping process in the background
        process.nextTick(async () => {
          try {
            // Log to both file and WebSocket
            logger.info(`[${logId}] Starting manual scrape for URL: ${url}`, { type });
            sendLogToSubscribers(logId, 'info', `Starting manual scrape for URL: ${url}`, { type });

            // Different scraping logic based on URL and type
            let details;
            let Model;
            let newItem;

            // Check for special "latest://" URL format first
            if (url.startsWith('latest://')) {
              // Extract the section from the URL (e.g., 'latest://movies' -> 'movies')
              const section = url.split('//')[1];
              logger.info(`[${logId}] Detected latest:// URL, scraping latest ${section} content`);
              sendLogToSubscribers(logId, 'info', `Detected latest:// URL, scraping latest ${section} content`);

              // Import the scrapeService
              const { scrapeAll, SCRAPE_MODE } = require('./src/scrapers/services/scrapeService');

              // Set up page limits for the specific section - all set to 0 by default
              const pageLimits = { movies: 0, series: 0, anime: 0, witv: 0 };

              // Always set the default limits first
              pageLimits.movies = 2;  // Scrape 2 pages of movies
              pageLimits.series = 0;  // Don't scrape series
              pageLimits.anime = 0;   // Don't scrape anime
              pageLimits.witv = 0;    // Don't scrape live TV

              // Then override based on the requested type
              switch (type) {
                case 'MOVIE':
                  // Already set to 2 pages
                  break;
                case 'SERIES':
                  pageLimits.series = 1;  // Scrape 1 page of series when specifically requested
                  break;
                case 'ANIME':
                  pageLimits.anime = 1;   // Scrape 1 page of anime when specifically requested
                  break;
                case 'LIVETV':
                  pageLimits.witv = 1;    // Scrape 1 page of live TV when specifically requested
                  break;
              }

              // Override the environment variables for page limits
              Object.keys(pageLimits).forEach(key => {
                process.env[`PAGE_LIMIT_${key.toUpperCase()}`] = pageLimits[key].toString();
              });

              logger.info(`[${logId}] Starting scrape with page limits: ${JSON.stringify(pageLimits)}`);
              sendLogToSubscribers(logId, 'info', `Starting scrape with page limits: ${JSON.stringify(pageLimits)}`);

              // Run the scrape in latest mode
              const result = await scrapeAll(SCRAPE_MODE.LATEST);

              logger.info(`[${logId}] Scrape completed with result: ${JSON.stringify(result)}`);
              sendLogToSubscribers(logId, 'success', `Scrape completed with result: ${JSON.stringify(result)}`);

              // No need to create a new item or update the database, as scrapeAll handles that
              return;
            }

            // Helper function to extract path from URL
            function extractPathFromUrl(fullUrl) {
              // Skip URL validation for special URLs
              if (fullUrl.startsWith('latest://')) {
                return '/latest';
              }

              if (!fullUrl || typeof fullUrl !== 'string' || !fullUrl.startsWith('http')) {
                return '';
              }
              try {
                const urlObject = new URL(fullUrl);
                return urlObject.pathname + urlObject.search + urlObject.hash;
              } catch (e) {
                logger.warn(`[${logId}] Could not parse path from URL "${fullUrl}": ${e.message}`);
                return ''; // Return empty string on error
              }
            }

            // Extract detailUrlPath from the URL
            const detailUrlPath = extractPathFromUrl(url);
            if (!detailUrlPath) {
              throw new Error('Failed to extract detailUrlPath from URL');
            }

            logger.info(`[${logId}] Extracted detailUrlPath: ${detailUrlPath}`);
            sendLogToSubscribers(logId, 'info', `Extracted detailUrlPath: ${detailUrlPath}`);

            // Helper function to extract title from URL
            function extractTitleFromUrl(url) {
              // Handle special URL formats
              if (url.startsWith('latest://')) {
                const section = url.split('//')[1];
                return `Latest ${section.charAt(0).toUpperCase() + section.slice(1)}`;
              }

              try {
                const urlObj = new URL(url);
                const pathParts = urlObj.pathname.split('/');
                const lastPart = pathParts[pathParts.length - 1];

                // Remove file extension if present
                const titleWithoutExt = lastPart.replace(/\.\w+$/, '');

                // Remove any season indicators
                const titleWithoutSeason = titleWithoutExt.replace(/-saison-\d+/i, '');

                // Remove numeric ID prefix (common in Wiflix URLs like 33564-bet)
                const titleWithoutId = titleWithoutSeason.replace(/^\d+-/, '');

                // Replace hyphens with spaces and capitalize words
                return titleWithoutId
                  .replace(/-/g, ' ')
                  .replace(/\b\w/g, c => c.toUpperCase());
              } catch (e) {
                logger.warn(`[${logId}] Could not extract title from URL: ${e.message}`);
                return 'Unknown Title';
              }
            }

            // Extract title from URL for all content types
            const titleFromUrl = extractTitleFromUrl(url);
            logger.info(`[${logId}] Extracted title from URL: ${titleFromUrl}`);
            sendLogToSubscribers(logId, 'info', `Extracted title from URL: ${titleFromUrl}`);

            // Determine which scraper to use based on the URL and type
            if (url.includes('french-anime')) {
              // French Anime scraper
              logger.info(`[${logId}] Detected French Anime URL, using French Anime scraper`);
              sendLogToSubscribers(logId, 'info', `Detected French Anime URL, using French Anime scraper`);

              Model = Anime;
              details = await scrapeFrenchAnimeDetail(url);

              if (!details) {
                throw new Error('Failed to scrape French Anime details');
              }

              // Create a new anime item
              newItem = new Anime({
                title: titleFromUrl,
                detailUrl: url,
                detailUrlPath: detailUrlPath,
                image: details.image,
                episodes: details.episodes || [],
                streamingUrls: details.streamingUrls || [],
                animeLanguage: details.animeLanguage || 'unknown',
                createdAt: new Date(),
                updatedAt: new Date()
              });
            } else if (url.includes('wiflix') || url.includes('flemmix')) {
              // Wiflix scraper
              logger.info(`[${logId}] Detected Wiflix URL, using Wiflix scraper`);
              sendLogToSubscribers(logId, 'info', `Detected Wiflix URL, using Wiflix scraper`);

              details = await scrapeWiflixDetail(url);

              if (!details) {
                throw new Error('Failed to scrape Wiflix details');
              }

              // Determine if it's a movie or series based on the URL and type
              if (url.includes('film-') || type === 'MOVIE') {
                Model = Movie;
                newItem = new Movie({
                  title: titleFromUrl,
                  detailUrl: url,
                  detailUrlPath: detailUrlPath,
                  image: details.thumbnail,
                  streamingUrls: details.streamingUrls || [],
                  createdAt: new Date(),
                  updatedAt: new Date()
                });
              } else {
                // Extract season from URL if available
                const seasonMatch = url.match(/saison-(\d+)/i);
                const season = seasonMatch ? seasonMatch[1] : '1';

                Model = Series;
                newItem = new Series({
                  title: titleFromUrl,
                  detailUrl: url,
                  detailUrlPath: detailUrlPath,
                  image: details.thumbnail,
                  episodes: details.episodes || [],
                  season: season,
                  createdAt: new Date(),
                  updatedAt: new Date()
                });
              }
            } else if (url.includes('witv')) {
              // WiTV scraper
              logger.info(`[${logId}] Detected WiTV URL, using WiTV scraper`);
              sendLogToSubscribers(logId, 'info', `Detected WiTV URL, using WiTV scraper`);

              const { scrapeWitvDetail } = require('./src/scrapers/sites/witv/detail');
              details = await scrapeWitvDetail(url);

              if (!details) {
                throw new Error('Failed to scrape WiTV details');
              }

              Model = LiveTV;
              newItem = new LiveTV({
                title: titleFromUrl,
                detailUrl: url,
                detailUrlPath: detailUrlPath,
                image: details.image,
                streamingUrls: details.streamingUrls || [],
                createdAt: new Date(),
                updatedAt: new Date()
              });
            } else {
              throw new Error('Unsupported URL format. Please use a URL from Wiflix, French Anime, or WiTV.');
            }

            // Save the new item to the database
            logger.info(`[${logId}] Saving new ${type} to database`);
            sendLogToSubscribers(logId, 'info', `Saving new ${type} to database`);

            try {
              // First, try a direct update by detailUrlPath to handle the duplicate key case
              // This is the most reliable way to handle the duplicate key issue
              const updateResult = await Model.findOneAndUpdate(
                { detailUrlPath: detailUrlPath },
                {
                  $set: {
                    title: newItem.title,
                    detailUrl: newItem.detailUrl,
                    image: newItem.image || newItem.thumbnail,
                    episodes: newItem.episodes || [],
                    streamingUrls: newItem.streamingUrls || [],
                    season: newItem.season || '1',
                    updatedAt: new Date()
                  }
                },
                { new: true, upsert: false }
              );

              if (updateResult) {
                // Item was found and updated
                logger.info(`[${logId}] Existing item found and updated via findOneAndUpdate`);
                sendLogToSubscribers(logId, 'info', `Existing item found and updated via findOneAndUpdate`);

                logger.info(`[${logId}] Item updated successfully`);
                sendLogToSubscribers(logId, 'success', `Item updated successfully`);

                // Use the updated item for enrichment later
                newItem = updateResult;
              } else {
                // No existing item found, try to insert a new one
                logger.info(`[${logId}] No existing item found, attempting to insert new item`);
                sendLogToSubscribers(logId, 'info', `No existing item found, attempting to insert new item`);

                try {
                  // Save the new item
                  await newItem.save();

                  logger.info(`[${logId}] New item saved successfully`);
                  sendLogToSubscribers(logId, 'success', `New item saved successfully`);
                } catch (insertError) {
                  // If there's still a duplicate key error, try one more approach
                  if (insertError.code === 11000) {
                    logger.info(`[${logId}] Duplicate key detected during insert, trying alternative update approach...`);
                    sendLogToSubscribers(logId, 'info', `Duplicate key detected during insert, trying alternative update approach...`);

                    // Try a direct update with the raw MongoDB driver
                    const collection = mongoose.connection.collection(Model.collection.name);
                    const updateDoc = newItem.toObject();
                    delete updateDoc._id; // Remove _id to avoid errors

                    const result = await collection.updateOne(
                      { detailUrlPath: detailUrlPath },
                      { $set: updateDoc }
                    );

                    if (result.matchedCount > 0) {
                      logger.info(`[${logId}] Item updated successfully using raw MongoDB driver`);
                      sendLogToSubscribers(logId, 'success', `Item updated successfully using raw MongoDB driver`);

                      // Fetch the updated item for enrichment
                      newItem = await Model.findOne({ detailUrlPath: detailUrlPath });
                    } else {
                      throw new Error(`Failed to update item: No matching document found for detailUrlPath: ${detailUrlPath}`);
                    }
                  } else {
                    throw insertError;
                  }
                }
              }
            } catch (error) {
              logger.error(`[${logId}] Error saving/updating item: ${error.message}`, { stack: error.stack });
              sendLogToSubscribers(logId, 'error', `Error saving/updating item: ${error.message}`);
              throw error;
            }

            // Enrich the item with metadata
            logger.info(`[${logId}] Enriching item with metadata`);
            sendLogToSubscribers(logId, 'info', `Enriching item with metadata`);

            try {
              // Get the most up-to-date version of the item from the database
              const itemToEnrich = await Model.findOne({ detailUrlPath: detailUrlPath });

              if (!itemToEnrich) {
                throw new Error(`Item not found in database after saving/updating`);
              }

              const enriched = await enrichItemWithOptions(itemToEnrich, {
                useTmdb: true,
                useJikan: type === 'ANIME',
                useGemini: true,
                forceUpdate: true
              });

              if (enriched) {
                logger.info(`[${logId}] Item enriched successfully`);
                sendLogToSubscribers(logId, 'success', `Item enriched successfully`);

                // Update the item in the database
                await Model.findByIdAndUpdate(itemToEnrich._id, enriched, { new: true });
              }
            } catch (enrichError) {
              logger.error(`[${logId}] Error enriching item: ${enrichError.message}`, { stack: enrichError.stack });
              sendLogToSubscribers(logId, 'error', `Error enriching item: ${enrichError.message}`);
              // Continue execution - enrichment failure shouldn't fail the whole operation
            }

            logger.info(`[${logId}] Manual scrape completed successfully for URL: ${url}`);
            sendLogToSubscribers(logId, 'success', `Manual scrape completed successfully for URL: ${url}`);
          } catch (error) {
            logger.error(`[${logId}] Error during manual scrape: ${error.message}`, {
              url,
              type,
              stack: error.stack
            });
            sendLogToSubscribers(logId, 'error', `Error during manual scrape: ${error.message}`, {
              url,
              type
            });
          }
        });

        return {
          success: true,
          message: "Manual scrape operation started",
          url,
          type,
          logId
        };
      } catch (error) {
        logger.error(`Manual scrape error: ${error.message}`, {
          url,
          type,
          stack: error.stack
        });

        return {
          success: false,
          message: `Error starting manual scrape: ${error.message}`,
          url: null,
          type: null,
          logId: null
        };
      }
    },

    updateItem: async (_, { id, type, input, adminToken }) => {
      try {
        console.log('updateItem called with:', { id, type, input, adminToken });

        // Validate admin token
        console.log('Validating admin token...');
        const isValidToken = await Admin.validateToken(adminToken);
        console.log('Token validation result:', isValidToken);

        if (!isValidToken) {
          console.log('Invalid token, returning error response');
          return {
            success: false,
            message: "Invalid admin token",
            item: null
          };
        }

        // Get the appropriate model based on type
        console.log('Getting model for type:', type);
        const Model = {
          MOVIE: Movie,
          SERIES: Series,
          ANIME: Anime,
          LIVETV: LiveTV,
        }[type];
        console.log('Model found:', !!Model);

        if (!Model) {
          console.log('No model found for type:', type);
          return {
            success: false,
            message: "Invalid item type",
            item: null
          };
        }

        // Update the item
        const updatedItem = await Model.findByIdAndUpdate(
          id,
          { $set: input },
          { new: true, runValidators: true }
        );

        if (!updatedItem) {
          return {
            success: false,
            message: "Item not found",
            item: null
          };
        }

        return {
          success: true,
          message: "Item updated successfully",
          item: updatedItem
        };
      } catch (error) {
        console.error('Update item error:', error);
        return {
          success: false,
          message: error.message || "Error updating item",
          item: null
        };
      }
    }

  } // End Mutation
}; // End resolvers object

module.exports = resolvers;