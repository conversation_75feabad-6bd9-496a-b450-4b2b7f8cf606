#!/bin/bash

# 🚀 NetStream Local Deployment Script
# Builds and runs the combined Docker container locally

set -e

echo "🎬 NetStream Combined Docker Deployment"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="netstream-combined"
CONTAINER_NAME="netstream-app"
PORT=3000
MONGO_URI=${MONGO_URI:-"mongodb+srv://crypto:<EMAIL>/NetStream?retryWrites=true&w=majority"}

echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Image: ${IMAGE_NAME}"
echo -e "  Container: ${CONTAINER_NAME}"
echo -e "  Port: ${PORT}"
echo -e "  MongoDB: ${MONGO_URI:0:50}..."
echo ""

# Stop and remove existing container if it exists
echo -e "${YELLOW}🧹 Cleaning up existing containers...${NC}"
docker stop ${CONTAINER_NAME} 2>/dev/null || true
docker rm ${CONTAINER_NAME} 2>/dev/null || true

# Build the image
echo -e "${BLUE}🔨 Building Docker image...${NC}"
docker build -t ${IMAGE_NAME} .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build successful!${NC}"
else
    echo -e "${RED}❌ Build failed!${NC}"
    exit 1
fi

# Run the container
echo -e "${BLUE}🚀 Starting container...${NC}"
docker run -d \
    --name ${CONTAINER_NAME} \
    -p ${PORT}:${PORT} \
    -e MONGO_URI="${MONGO_URI}" \
    -e NODE_ENV="production" \
    -e PORT="${PORT}" \
    -e FASTIFY_PORT="3001" \
    -e NEXT_TELEMETRY_DISABLED="1" \
    ${IMAGE_NAME}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Container started successfully!${NC}"
    echo ""
    echo -e "${GREEN}🌍 Application URLs:${NC}"
    echo -e "  Frontend: ${BLUE}http://localhost:${PORT}${NC}"
    echo -e "  GraphQL API: ${BLUE}http://localhost:${PORT}/graphql${NC}"
    echo -e "  Health Check: ${BLUE}http://localhost:${PORT}/health${NC}"
    echo ""
    echo -e "${YELLOW}📊 View logs:${NC}"
    echo -e "  docker logs -f ${CONTAINER_NAME}"
    echo ""
    echo -e "${YELLOW}🛑 Stop container:${NC}"
    echo -e "  docker stop ${CONTAINER_NAME}"
    echo ""
    
    # Wait a moment and show initial logs
    echo -e "${BLUE}📋 Initial logs:${NC}"
    sleep 3
    docker logs ${CONTAINER_NAME}
    
else
    echo -e "${RED}❌ Failed to start container!${NC}"
    exit 1
fi
