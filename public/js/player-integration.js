/**
 * Player Integration Script
 * This script integrates the player with the existing media.js functionality
 */

(function() {
  console.log('Player Integration: Script loaded');

  // Wait for DOM content loaded
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Player Integration: DOM content loaded');

    // Wait for modernPlayer to be available
    const checkModernPlayer = setInterval(() => {
      if (window.modernPlayer) {
        clearInterval(checkModernPlayer);
        initializeIntegration();
      }
    }, 100);
  });

  /**
   * Initialize the integration
   */
  function initializeIntegration() {
    console.log('Player Integration: Initializing integration');

    // Store original onVideoElementConnected callback
    const originalCallback = window.modernPlayer.onVideoElementConnected;

    // Override the callback to handle HLS initialization
    window.modernPlayer.onVideoElementConnected = function() {
      console.log('Player Integration: Video element connected callback');

      // Call the original callback if it exists
      if (typeof originalCallback === 'function') {
        try {
          originalCallback();
        } catch (error) {
          console.error('Player Integration: Error in original callback:', error);
        }
      }

      // Check if we need to initialize HLS playback
      const hlsInstance = window.modernPlayer.getHlsInstance();
      if (hlsInstance) {
        console.log('Player Integration: Found HLS instance, initializing playback');

        try {
          // Get the video element
          const videoElement = document.getElementById('player');
          if (!videoElement) {
            console.error('Player Integration: Video element not found');
            return;
          }

          // Attach HLS to the video element
          hlsInstance.attachMedia(videoElement);

          // Start loading
          hlsInstance.on('mediaAttached', function() {
            console.log('Player Integration: HLS media attached, starting load');
            hlsInstance.loadSource(hlsInstance.url);
          });

          // Handle manifest parsed
          hlsInstance.on('manifestParsed', function(event, data) {
            console.log('Player Integration: HLS manifest parsed, levels:', data.levels.length);

            // Start playback
            setTimeout(() => {
              if (videoElement) {
                console.log('Player Integration: Starting HLS playback');
                videoElement.play()
                  .then(() => {
                    console.log('Player Integration: HLS playback started successfully');
                  })
                  .catch(error => {
                    console.error('Player Integration: Error starting HLS playback:', error);
                  });
              }
            }, 500);
          });

          // Handle errors
          hlsInstance.on('error', function(event, data) {
            console.error('Player Integration: HLS error:', data);

            if (data.fatal) {
              switch(data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  console.log('Player Integration: Fatal network error, trying to recover');
                  hlsInstance.startLoad();
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  console.log('Player Integration: Fatal media error, trying to recover');
                  hlsInstance.recoverMediaError();
                  break;
                default:
                  console.error('Player Integration: Fatal error, cannot recover');
                  hlsInstance.destroy();
                  break;
              }
            }
          });
        } catch (error) {
          console.error('Player Integration: Error initializing HLS playback:', error);
        }
      }
    };

    // Patch the media.js playItem function if it exists
    if (window.playItem) {
      const originalPlayItem = window.playItem;

      window.playItem = function(options) {
        console.log('Player Integration: Patched playItem called with options:', options);

        // Show the player container
        const playerContainer = document.getElementById('player-container');
        if (playerContainer) {
          playerContainer.classList.remove('hidden');
        }

        // Set the title
        if (options.title) {
          window.modernPlayer.setTitle(options.title);
        }

        // Handle iframe sources directly
        // Check for specific video provider URLs that should use iframe
        const isVideoProviderUrl = options.url && (
          options.url.includes('waaw1.tv') ||
          options.url.includes('do7go.com') ||
          options.url.includes('streamtape.com') ||
          options.url.includes('doodstream.com') ||
          options.url.includes('vidoza.net') ||
          options.url.includes('voe.sx') ||
          options.url.includes('upstream.to') ||
          options.url.includes('mixdrop.co') ||
          options.url.includes('vudeo.net') ||
          options.url.includes('tipfly.xyz') ||
          options.url.includes('lulu.st') ||
          options.url.includes('/e/') // Common pattern for embed URLs
        );

        console.log('Player Integration: URL check:', {
          url: options.url,
          isVideoProviderUrl,
          method: options.method,
          isSourceStream: options.isSourceStream
        });

        if (options.url && (options.method === 'iframe' || !options.isSourceStream || isVideoProviderUrl)) {
          console.log('Player Integration: Detected iframe source, loading directly');

          // Get the player instance
          const playerInstance = window.modernPlayer;

          // Load the iframe source
          if (playerInstance && typeof playerInstance.loadIframeSource === 'function') {
            // Show the player container first
            const playerContainer = document.getElementById('player-container');
            if (playerContainer) {
              playerContainer.classList.remove('hidden');
            }

            // Set a timeout to ensure the DOM is ready
            setTimeout(() => {
              // Get the iframe element
              const iframe = document.getElementById('player-iframe');
              const videoElement = document.getElementById('player');
              const playerControls = document.getElementById('player-controls');

              if (iframe) {
                console.log('Player Integration: Setting up iframe player');

                // Hide video element
                if (videoElement) {
                  videoElement.style.display = 'none';
                }

                // Hide controls
                if (playerControls) {
                  playerControls.style.display = 'none';
                }

                // Handle logo visibility for iframe content
                const playerLogo = document.getElementById('player-logo');
                if (playerLogo) {
                  // Check if we're in fullscreen mode
                  const isFullscreen = !!document.fullscreenElement ||
                                     !!document.webkitFullscreenElement ||
                                     !!document.mozFullScreenElement ||
                                     !!document.msFullscreenElement;

                  if (isFullscreen) {
                    // Hide logo in fullscreen for iframe content
                    playerLogo.style.display = 'none';
                    console.log('Player Integration: Hiding logo for iframe content in fullscreen');
                  }
                }

                // Show and set iframe source
                iframe.style.display = 'block';
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = 'none';
                iframe.style.position = 'absolute';
                iframe.style.top = '0';
                iframe.style.left = '0';
                iframe.style.zIndex = '10'; // Higher z-index to ensure it's on top
                iframe.setAttribute('allowfullscreen', '');

                // Clear any existing src first
                iframe.src = 'about:blank';

                // Force a reflow
                void iframe.offsetWidth;

                // Set the new src
                iframe.src = options.url;

                // Add a debug message to check if iframe is visible
                console.log('Player Integration: Iframe element:', {
                  display: iframe.style.display,
                  width: iframe.style.width,
                  height: iframe.style.height,
                  zIndex: iframe.style.zIndex,
                  src: iframe.src
                });

                console.log('Player Integration: Iframe source set to:', options.url);
              } else {
                console.error('Player Integration: Iframe element not found');

                // Fallback to using the loadIframeSource method
                playerInstance.loadIframeSource(options.url);
              }
            }, 100);

            // Return early to prevent the original function from handling the iframe
            return true;
          }
        }

        // Call the original function for non-iframe sources
        return originalPlayItem(options);
      };

      console.log('Player Integration: Patched playItem function');
    }

    // Patch the media.js hidePlayer function if it exists
    if (window.hidePlayer) {
      const originalHidePlayer = window.hidePlayer;

      window.hidePlayer = function() {
        console.log('Player Integration: Patched hidePlayer called');

        // Clean up iframe if it exists
        const iframe = document.getElementById('player-iframe');
        if (iframe) {
          console.log('Player Integration: Cleaning up iframe');
          iframe.src = '';
          iframe.style.display = 'none';
        }

        // Show controls again (they might have been hidden for iframe content)
        const playerControls = document.getElementById('player-controls');
        if (playerControls) {
          playerControls.style.display = 'flex';
        }

        // Show video element again
        const videoElement = document.getElementById('player');
        if (videoElement) {
          videoElement.style.display = 'block';
        }

        // Call the original function
        originalHidePlayer();

        // Ensure the player is properly reinitialized when shown again
        setTimeout(() => {
          // Get the player instance and reinitialize it
          if (window.modernPlayer) {
            console.log('Player Integration: Reinitializing player after hide');
            window.modernPlayer.initialize();
          }
        }, 100);
      };

      console.log('Player Integration: Patched hidePlayer function');
    }

    // Observe player container visibility changes
    const playerContainer = document.getElementById('player-container');
    if (playerContainer) {
      // Create a MutationObserver to watch for class changes
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'class') {
            const isHidden = playerContainer.classList.contains('hidden');

            if (!isHidden) {
              // Player is being shown, ensure it's properly initialized
              console.log('Player Integration: Player container shown, reinitializing');

              // Check if we're showing an iframe or a video
              const iframe = document.getElementById('player-iframe');
              const isIframeVisible = iframe && iframe.style.display === 'block' && iframe.src;

              setTimeout(() => {
                if (window.modernPlayer) {
                  // Initialize the player
                  window.modernPlayer.initialize();

                  if (isIframeVisible) {
                    // If iframe is visible, make sure controls are hidden
                    console.log('Player Integration: Iframe is visible, hiding controls');
                    const playerControls = document.getElementById('player-controls');
                    if (playerControls) {
                      playerControls.style.display = 'none';
                    }

                    // Make sure video element is hidden
                    const videoElement = document.getElementById('player');
                    if (videoElement) {
                      videoElement.style.display = 'none';
                    }

                    // Handle logo visibility for iframe content
                    const playerLogo = document.getElementById('player-logo');
                    if (playerLogo) {
                      // Check if we're in fullscreen mode
                      const isFullscreen = !!document.fullscreenElement ||
                                         !!document.webkitFullscreenElement ||
                                         !!document.mozFullScreenElement ||
                                         !!document.msFullscreenElement;

                      if (isFullscreen) {
                        // Hide logo in fullscreen for iframe content
                        playerLogo.style.display = 'none';
                        console.log('Player Integration: Hiding logo for iframe content in fullscreen');
                      }
                    }
                  } else {
                    // If iframe is not visible, connect the video element
                    window.modernPlayer.connectVideoElement();
                  }
                }
              }, 100);
            }
          }
        });
      });

      // Start observing
      observer.observe(playerContainer, { attributes: true });
      console.log('Player Integration: Started observing player container visibility');
    }

    console.log('Player Integration: Integration initialized successfully');
  }
})();
