// File: public/js/script.js
// Adapted for GraphQL endpoint

document.addEventListener('DOMContentLoaded', () => {
  console.log('script.js: DOMContentLoaded');

  // Basic responsive optimization (removed TV-specific detection)
  function setupBasicOptimizations() {
    // Apply lazy loading to all images for better performance
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (img.loading !== 'lazy') {
        img.loading = 'lazy';
      }
    });
  }

  setupBasicOptimizations();

  // --- Fetch Config ---
  // Initialize window.config object
  window.config = {};

  // Fetch config from GraphQL endpoint
  async function fetchConfig() {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query {
              config {
                tmdbApiKey
                wiflixBase
                frenchAnimeBase
                witvBase
              }
            }
          `
        })
      });

      const data = await response.json();
      if (data.data && data.data.config) {
        window.config = data.data.config;
        console.log('script.js: Config loaded:', window.config);
      }
    } catch (error) {
      console.error('script.js: Error fetching config:', error);
    }
  }

  // Fetch config immediately
  fetchConfig();

  // --- Helper Functions ---

  function debounce(func, wait) {
      let timeout;
      return function (...args) {
          const context = this;
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(context, args), wait);
      };
  }

  function toggleLoading(sectionId, show) {
      // Find the section element (movies, series, anime, livetv, search)
      const sectionElement = document.getElementById(sectionId);
      if (sectionElement) {
          let indicator = sectionElement.querySelector('.loading-indicator');
          // Create indicator if it doesn't exist
          if (!indicator) {
              indicator = document.createElement('div');
              indicator.classList.add('loading-indicator');
               // Prepend to ensure it's visually contained, adjust styling if needed
              sectionElement.prepend(indicator);
          }
          // Add/remove active class
          if (show) {
              indicator.classList.add('active');
              // console.log(`Loading indicator shown for: ${sectionId}`);
          } else {
              indicator.classList.remove('active');
              // console.log(`Loading indicator hidden for: ${sectionId}`);
          }
      } else {
           console.warn(`toggleLoading: Section element not found for ID "${sectionId}"`);
      }
  }

  // Function to setup sidebar navigation
  function setupSidebarNavigation() {
      document.querySelectorAll('.sidebar a').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
              e.preventDefault();
              const section = this.dataset.section;
              console.log(`script.js: Sidebar clicked, section: ${section}, current path: ${window.location.pathname}`);
              // Check if currently on a media detail page
              if (window.location.pathname.match(/^\/(movies|series|anime|livetv)\/\w+$/)) {
                  const targetUrl = `/#${section}`; // Target URL is homepage hash
                  console.log(`script.js: Redirecting from media page to ${targetUrl}`);
                  window.location.href = targetUrl; // Redirect to homepage
              } else {
                  // On homepage, scroll to the section
                  const sectionEl = document.getElementById(section);
                  if (sectionEl) {
                     sectionEl.scrollIntoView({ behavior: 'smooth' });
                  }
                  // Update active class in sidebar
                  document.querySelectorAll('.sidebar a').forEach(link => link.classList.remove('active'));
                  this.classList.add('active');
                  // Update URL hash without reloading (handled by home.js listener now)
                  // window.location.hash = section;
              }
          });
      });
  }

  setupSidebarNavigation();

  // --- Setup Return Arrow ---
  const returnArrow = document.getElementById('return-arrow');
  if (returnArrow) {
      const path = window.location.pathname.split('/');
      // Check if path is /<type>/<id>
      const isMediaPage = path.length === 3 && ['movies', 'series', 'anime', 'livetv'].includes(path[1]) && path[2];
      if (isMediaPage) {
          document.body.classList.add('media-page'); // Add class to show the arrow via CSS
          const mediaType = path[1];
          const targetUrl = `/#${mediaType}`; // Link back to the correct section on homepage
          returnArrow.href = targetUrl;
          console.log(`script.js: Return arrow active, pointing to ${targetUrl}`);
          // Click handler might not be strictly needed if href is correct, but kept for safety/logging
          returnArrow.addEventListener('click', (e) => {
              e.preventDefault();
              console.log(`script.js: Return arrow clicked, navigating to ${targetUrl}`);
              window.location.href = targetUrl;
          });
      } else {
           console.log(`script.js: Not a media page, return arrow remains hidden.`);
      }
  }

  // --- Loading Indicators (Monkey-patch fetch for GraphQL) ---
  const originalFetch = window.fetch;
  window.fetch = async function (url, options) {
      let section = null;
      const isGraphQLCall = url === '/graphql'; // Target the GraphQL endpoint

      if (isGraphQLCall && options?.method?.toUpperCase() === 'POST') {
          try {
              // Only parse body if it exists and is likely JSON
              if (options.body && typeof options.body === 'string') {
                  const body = JSON.parse(options.body);
                  const query = body.query || '';

                  // Improved logic to determine section from GraphQL query
                  if (query.includes('search(')) {
                      section = 'search';
                  } else if (query.includes('movies(')) {
                      section = 'movies';
                  } else if (query.includes('series(')) {
                      section = 'series';
                  } else if (query.includes('anime(')) {
                      section = 'anime';
                  } else if (query.includes('liveTV(')) { // Match GraphQL query name
                      section = 'livetv'; // Map to section ID
                  }
                  // Add more checks if other queries affect main list sections
              }
          } catch (e) {
              console.warn("script.js: Could not parse GraphQL query body for loading indicator.", e);
          }

          if (section) {
               // console.log(`script.js: GraphQL call for section: ${section}. Showing loading.`);
               toggleLoading(section, true);
          } else if (isGraphQLCall) {
               // console.log(`script.js: GraphQL call detected, but section undetermined for loading indicator.`);
               // Don't show loading for unknown GraphQL calls (like config or stream fetch)
          }
      }

      try {
          // Proceed with the actual fetch request
          const response = await originalFetch.call(this, url, options);
          return response; // Return the response
      } finally {
          // Hide loading indicator if a section was identified
          if (section) {
              // Optional delay to ensure indicator visibility on fast networks
              // await new Promise(resolve => setTimeout(resolve, 50));
              toggleLoading(section, false);
              // console.log(`script.js: Hiding loading for section: ${section}`);
          }
      }
  };


  // --- Close Player Button ---
  const closePlayerButton = document.getElementById('close-player');
  const playerContainer = document.getElementById('player-container');
  if (closePlayerButton && playerContainer) {
      closePlayerButton.addEventListener('click', (event) => {
          event.stopPropagation(); // Prevent click from bubbling up to playerContainer listener
          // Check if hidePlayer function exists (it should be defined in media.js)
          if (typeof hidePlayer === 'function') {
               console.log("script.js: Close player button clicked.");
              hidePlayer();
          } else {
              console.warn("script.js: hidePlayer function not found when trying to close player.");
              // Fallback: Directly hide the container (less clean)
              playerContainer.classList.add('hidden');
              playerContainer.style.display = 'none';
          }
      });
  }

  // --- Event Delegation for Grid Items (in .content) ---
  // This listener is now primarily handled within home.js for homepage grid items
  // Keeping a generic version here might be redundant or cause conflicts if not careful.
  // Let's comment it out here and rely on the one in home.js for the main grids.
  /*
  document.querySelector('.content')?.addEventListener('click', debounce((e) => {
      const gridItem = e.target.closest('.grid-item');

      // Ignore clicks inside the player or on source links
      if (e.target.closest('#player-container') || e.target.closest('.source-link')) {
          return;
      }

      if (gridItem) {
          const { id, type } = gridItem.dataset;
          // Ensure navigation only happens if NOT already on the target media page
          if (id && type && !window.location.pathname.startsWith(`/${type}/${id}`)) {
              console.log(`script.js: Delegated grid click, redirecting to /${type}/${id}`);
              window.location.href = `/${type}/${id}`;
          } else if (!id || !type) {
               console.warn("script.js: Delegated grid click on item without id/type:", gridItem);
          }
      }
  }, 250));
  */
  // Note: The click listener in media.js handles clicks on episode/provider grids specifically.

  // --- Accessibility (Keyboard Navigation - Escape key for player) ---
  document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
          const playerContainer = document.getElementById('player-container'); // Re-check element
          // Check if player exists and is visible
          if (playerContainer && !playerContainer.classList.contains('hidden')) {
              if (typeof hidePlayer === 'function') {
                   console.log("script.js: Escape key pressed, hiding player.");
                  hidePlayer();
              } else {
                   console.warn("script.js: hidePlayer function not found on Escape key press.");
                    playerContainer.classList.add('hidden');
                    playerContainer.style.display = 'none';
              }
          }
      }
  });

  // --- Image Error Handling ---
  document.addEventListener('error', (e) => {
      // Target only <img> elements
      if (e.target.tagName.toLowerCase() === 'img') {
          const isBanner = e.target.classList.contains('banner');
          const defaultSrc = isBanner ? '/default-banner.jpg' : '/default-thumbnail.jpg';
          // Prevent infinite loop if the default image itself fails
          if (e.target.src !== window.location.origin + defaultSrc) {
               console.log(`script.js: Replacing broken image (${e.target.src}) with default ${isBanner ? 'banner' : 'thumbnail'}.`);
              e.target.src = defaultSrc;
              e.target.alt += " (Image failed to load)"; // Add alt text indication
          }
      }
  }, true); // Use capture phase to catch errors early

  // --- Prevent Double Click on Source Links ---
   // This can be handled within the media.js click listener for better context control.
   // Removing from here to avoid potential conflicts if media.js already handles disabling.
  /*
  document.querySelector('.content')?.addEventListener('click', (e) => {
      const sourceLink = e.target.closest('.source-link');
      if (sourceLink) {
          console.log("script.js: Disabling source link briefly.");
          sourceLink.disabled = true;
          setTimeout(() => { sourceLink.disabled = false; }, 1000); // Re-enable after 1 second
      }
  });
  */

  // --- Player Visibility Fix (Monkey-patching playItem/hidePlayer) ---
  // This ensures the player container's display/opacity/pointerEvents are correct after show/hide actions.
  function ensurePlayerVisibility() {
      const playerContainer = document.getElementById('player-container');
      if (!playerContainer) return;

      const isHidden = playerContainer.classList.contains('hidden');
      const videoPlayer = document.getElementById('player');
      const iframePlayer = document.getElementById('player-iframe');

      if (!isHidden) {
          playerContainer.style.display = 'flex'; // Should be flex to center content
          playerContainer.style.opacity = '1';
          playerContainer.style.visibility = 'visible';
          playerContainer.style.pointerEvents = 'auto';
          // Ensure only the correct player (video or iframe) is visible
          if (videoPlayer) videoPlayer.style.display = videoPlayer.src ? 'block' : 'none';
          if (iframePlayer) iframePlayer.style.display = iframePlayer.src && iframePlayer.src !== 'about:blank' ? 'block' : 'none';
      } else {
          // Explicitly set styles when hidden
          playerContainer.style.display = 'none';
          playerContainer.style.opacity = '0';
          playerContainer.style.visibility = 'hidden';
          playerContainer.style.pointerEvents = 'none';
          if (videoPlayer) videoPlayer.style.display = 'none';
          if (iframePlayer) iframePlayer.style.display = 'none';
      }
  }

  // Check if playItem exists (defined in media.js) and patch it
  if (typeof window.playItem === 'function') {
      const originalPlayItem = window.playItem;
      window.playItem = function (...args) {
          console.log("script.js: Patched playItem called.");
          originalPlayItem.apply(this, args);
          // Use setTimeout to ensure visibility check runs after playItem updates state
          setTimeout(ensurePlayerVisibility, 0);
      };
       console.log("script.js: Patched playItem function.");
  } else {
       // This might happen on the homepage where media.js isn't loaded
       // console.log("script.js: playItem function not found for patching.");
  }

  // Check if hidePlayer exists (defined in media.js) and patch it
  if (typeof window.hidePlayer === 'function') {
      const originalHidePlayer = window.hidePlayer;
      window.hidePlayer = function (...args) {
           console.log("script.js: Patched hidePlayer called.");
          originalHidePlayer.apply(this, args);
           // Use setTimeout to ensure visibility check runs after hidePlayer updates state
          setTimeout(ensurePlayerVisibility, 0);
      };
       console.log("script.js: Patched hidePlayer function.");
  } else {
       // console.log("script.js: hidePlayer function not found for patching.");
  }

  // Add listener for popstate (browser back/forward) to ensure player visibility is correct
  window.addEventListener('popstate', () => {
      console.log("script.js: Popstate event triggered, ensuring player visibility.");
      ensurePlayerVisibility();
  });

  // Add listener for clicks directly on the player container overlay to hide it
  if (playerContainer) {
      playerContainer.addEventListener('click', (e) => {
          // If the click is directly on the container (not its children like the video/iframe/button)
          if (e.target === playerContainer) {
               console.log("script.js: Player overlay clicked, hiding player.");
              if (typeof hidePlayer === 'function') {
                  hidePlayer();
              }
          }
      });
  }

  // --- Lazy Loading Images ---
  // Use MutationObserver to apply loading="lazy" to images added dynamically
  const observer = new MutationObserver(debounce(() => {
      document.querySelectorAll('.content img:not([loading])').forEach(img => {
          img.loading = "lazy";
          // console.log(`script.js: Added loading=lazy to image: ${img.alt || img.src.substring(0, 50)}`);
      });
  }, 300)); // Debounce to avoid excessive checks during rapid DOM changes

   // Observe the main content area for changes
   const contentArea = document.querySelector('.content');
   if (contentArea) {
      observer.observe(contentArea, { childList: true, subtree: true });
      // Apply to initially loaded images too
      document.querySelectorAll('.content img:not([loading])').forEach(img => img.loading = "lazy");
       console.log("script.js: Initialized lazy loading observer.");
   } else {
        console.warn("script.js: Could not find .content area to observe for lazy loading.");
   }

});