/**
 * NetStream Modern Video Player
 * A clean implementation of a video player with custom controls
 */

// Player singleton instance
let playerInstance = null;

// Player class
class VideoPlayer {
  constructor() {
    // DOM Elements
    this.playerContainer = null;
    this.playerWrapper = null;
    this.videoElement = null;
    this.playerControls = null;
    this.playerIframe = null;

    // UI Elements
    this.playPauseBtn = null;
    this.volumeBtn = null;
    this.fullscreenBtn = null;
    this.settingsBtn = null;
    this.progressContainer = null;
    this.progressBar = null;
    this.progressBuffer = null;
    this.timeTooltip = null;
    this.currentTimeDisplay = null;
    this.durationDisplay = null;
    this.volumeSlider = null;
    this.volumeLevel = null;
    this.settingsMenu = null;
    this.playerTitle = null;
    this.closeBtn = null;

    // State
    this.isPlaying = false;
    this.isMuted = false;
    this.isFullscreen = false;
    this.isDraggingProgress = false;
    this.isDraggingVolume = false;
    this.lastVolume = 1;
    this.hideControlsTimeout = null;
    this.hlsInstance = null;
    this.currentMediaTitle = '';

    // Callbacks
    this.onVideoElementConnected = null;

    // Flags
    this.isInitialized = false;
    this.isConnecting = false;

    // Bind methods to maintain 'this' context
    this.togglePlayPause = this.togglePlayPause.bind(this);
    this.toggleMute = this.toggleMute.bind(this);
    this.toggleFullscreen = this.toggleFullscreen.bind(this);
    this.toggleSettings = this.toggleSettings.bind(this);
    this.handlePlay = this.handlePlay.bind(this);
    this.handlePause = this.handlePause.bind(this);
    this.handleTimeUpdate = this.handleTimeUpdate.bind(this);
    this.handleProgress = this.handleProgress.bind(this);
    this.handleLoadedMetadata = this.handleLoadedMetadata.bind(this);
    this.handleEnded = this.handleEnded.bind(this);
    this.updateVolumeUI = this.updateVolumeUI.bind(this);
    this.startProgressDrag = this.startProgressDrag.bind(this);
    this.seekToPosition = this.seekToPosition.bind(this);
    this.showProgressTooltip = this.showProgressTooltip.bind(this);
    this.hideProgressTooltip = this.hideProgressTooltip.bind(this);
    this.startVolumeDrag = this.startVolumeDrag.bind(this);
    this.setVolume = this.setVolume.bind(this);
    this.handleDrag = this.handleDrag.bind(this);
    this.stopDrag = this.stopDrag.bind(this);
    this.handleSettingChange = this.handleSettingChange.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.showControls = this.showControls.bind(this);
    this.hideControls = this.hideControls.bind(this);
    this.scheduleHideControls = this.scheduleHideControls.bind(this);
    this.handlePlayerClick = this.handlePlayerClick.bind(this);
    this.hidePlayer = this.hidePlayer.bind(this);
  }

  /**
   * Initialize the player
   * @returns {VideoPlayer} The player instance
   */
  initialize() {
    console.log('Player: Initializing player');

    if (this.isInitialized) {
      console.log('Player: Player already initialized');
      return this;
    }

    // Load enhanced subtitle styles
    this.loadEnhancedSubtitleStyles();

    // Get DOM elements
    this.refreshElements();

    // Create player structure if needed
    this.ensurePlayerStructure();

    // Add event listeners
    this.addEventListeners();

    // Set up global idle detection
    this.setupGlobalIdleDetection();

    // Mark as initialized
    this.isInitialized = true;
    console.log('Player: Player initialized successfully');

    return this;
  }

  /**
   * Load enhanced subtitle styles
   */
  loadEnhancedSubtitleStyles() {
    // Check if the enhanced styles are already loaded
    if (document.getElementById('enhanced-subtitle-styles')) {
      return;
    }

    // Create a link element for the enhanced subtitle styles
    const link = document.createElement('link');
    link.id = 'enhanced-subtitle-styles';
    link.rel = 'stylesheet';
    link.href = '/css/enhanced-subtitles.css';

    // Add the link to the head
    document.head.appendChild(link);

    console.log('Player: Enhanced subtitle styles loaded');
  }

  /**
   * Set up global idle detection
   */
  setupGlobalIdleDetection() {
    console.log('Player: Setting up global idle detection');

    // Initialize last activity time and mouse position
    this.lastActivityTime = Date.now();
    this.lastGlobalMousePosition = null;
    this.mouseHasMovedSinceFullscreen = false;

    // Set up a global timer to check for idle state
    setInterval(() => {
      // Only check if in fullscreen mode
      if (this.isFullscreen && !this.isInteractingWithControls) {
        // Get current time
        const now = Date.now();

        // Calculate idle time
        const idleTime = now - this.lastActivityTime;

        // If idle for more than 2 seconds, hide controls
        if (idleTime > 2000) {
          console.log('Player: Global idle detection triggered, idle for', idleTime, 'ms');
          this.hideControls();

          // Force cursor to be hidden in fullscreen mode after idle period
          if (this.playerWrapper) {
            this.playerWrapper.classList.add('cursor-hidden');
          }
        }

        // Additional check: if mouse hasn't moved in 3 seconds in fullscreen, force hide
        if (this.mouseHasMovedSinceFullscreen && this.lastMouseMoveTime && (now - this.lastMouseMoveTime > 3000)) {
          console.log('Player: Mouse hasn\'t moved in 3 seconds, forcing hide in fullscreen');
          this.hideControls();

          // Force cursor to be hidden
          if (this.playerWrapper) {
            this.playerWrapper.classList.add('cursor-hidden');
          }
        }
      }
    }, 500); // Check every half second for more responsive hiding

    // Track mouse movement at the document level
    document.addEventListener('mousemove', (e) => {
      const currentPosition = { x: e.clientX, y: e.clientY };

      // Update last activity time on any mouse movement
      this.lastActivityTime = Date.now();
      this.lastMouseMoveTime = Date.now();

      // Check if mouse has actually moved significantly
      const hasMoved = !this.lastGlobalMousePosition ||
                      Math.abs(this.lastGlobalMousePosition.x - currentPosition.x) > 5 ||
                      Math.abs(this.lastGlobalMousePosition.y - currentPosition.y) > 5;

      if (hasMoved) {
        // If in fullscreen, mark that mouse has moved since entering fullscreen
        if (this.isFullscreen) {
          this.mouseHasMovedSinceFullscreen = true;
        }

        // Update last position
        this.lastGlobalMousePosition = currentPosition;
      } else if (this.isFullscreen) {
        // If mouse hasn't moved significantly but we're in fullscreen,
        // increment the stationary time
        const now = Date.now();
        if (!this.mouseStationaryStartTime) {
          this.mouseStationaryStartTime = now;
        } else if (now - this.mouseStationaryStartTime > 2000 && !this.isInteractingWithControls) {
          // If mouse has been stationary for more than 2 seconds and not interacting with controls,
          // hide the controls
          console.log('Player: Mouse stationary for 2 seconds in fullscreen, hiding controls');
          this.hideControls();

          // Force cursor to be hidden
          if (this.playerWrapper) {
            this.playerWrapper.classList.add('cursor-hidden');
          }
        }
      }
    });

    // Reset stationary tracking when mouse leaves the document
    document.addEventListener('mouseleave', () => {
      this.mouseStationaryStartTime = null;
    });
  }

  /**
   * Refresh element references
   */
  refreshElements() {
    this.playerContainer = document.getElementById('player-container');
    this.playerWrapper = document.getElementById('player-wrapper');

    // Get video element with proper type casting
    const videoEl = document.getElementById('player');
    this.videoElement = videoEl instanceof HTMLVideoElement ? videoEl : null;

    // Get iframe element with proper type casting
    const iframeEl = document.getElementById('player-iframe');
    this.playerIframe = iframeEl instanceof HTMLIFrameElement ? iframeEl : null;

    this.playerControls = document.getElementById('player-controls');
    this.closeBtn = document.getElementById('close-player');

    // UI elements
    if (this.playerControls) {
      this.playPauseBtn = document.getElementById('player-play-pause');
      this.rewindBtn = document.getElementById('player-rewind');
      this.forwardBtn = document.getElementById('player-forward');
      this.volumeBtn = document.getElementById('player-volume-toggle');
      this.ccBtn = document.getElementById('player-cc');
      this.fullscreenBtn = document.getElementById('player-fullscreen');
      this.settingsBtn = document.getElementById('player-settings');
      this.progressContainer = document.getElementById('player-progress-container');
      this.progressBar = document.getElementById('player-progress-bar');
      this.progressBuffer = document.getElementById('player-progress-buffer');
      this.timeTooltip = document.getElementById('player-time-tooltip');
      this.currentTimeDisplay = document.getElementById('player-current-time');
      this.durationDisplay = document.getElementById('player-duration');
      this.volumeSlider = document.getElementById('player-volume-slider');
      this.volumeLevel = document.getElementById('player-volume-level');
      this.settingsMenu = document.getElementById('player-settings-menu');
      this.playerTitle = document.getElementById('player-title');
    }

    // Subtitle elements
    this.subtitlesContainer = document.getElementById('player-subtitles');

    console.log('Player: Refreshed element references', {
      playerContainer: !!this.playerContainer,
      playerWrapper: !!this.playerWrapper,
      videoElement: !!this.videoElement,
      playerIframe: !!this.playerIframe,
      playerControls: !!this.playerControls
    });
  }

  /**
   * Ensure the player structure exists
   */
  ensurePlayerStructure() {
    // Create player wrapper if it doesn't exist
    if (!this.playerWrapper && this.playerContainer) {
      console.log('Player: Creating player wrapper');
      this.playerWrapper = document.createElement('div');
      this.playerWrapper.id = 'player-wrapper';
      this.playerContainer.insertBefore(this.playerWrapper, this.closeBtn);
    }

    // Create player logo if it doesn't exist
    if (this.playerWrapper && !document.getElementById('player-logo')) {
      const playerLogo = document.createElement('div');
      playerLogo.id = 'player-logo';
      playerLogo.textContent = 'NetStream';
      this.playerWrapper.appendChild(playerLogo);
    }

    // Create subtitle container if it doesn't exist
    if (this.playerWrapper && !document.getElementById('player-subtitles')) {
      const subtitlesContainer = document.createElement('div');
      subtitlesContainer.id = 'player-subtitles';
      subtitlesContainer.className = 'player-subtitles';
      this.playerWrapper.appendChild(subtitlesContainer);
    }

    // Create video element if it doesn't exist
    if (this.playerWrapper && !this.videoElement) {
      console.log('Player: Creating video element');
      this.videoElement = document.createElement('video');
      this.videoElement.id = 'player';
      this.videoElement.setAttribute('playsinline', '');
      this.playerWrapper.insertBefore(this.videoElement, this.playerWrapper.firstChild);
    }

    // Create iframe element if it doesn't exist
    if (this.playerWrapper && !this.playerIframe) {
      console.log('Player: Creating iframe element');
      this.playerIframe = document.createElement('iframe');
      this.playerIframe.id = 'player-iframe';
      this.playerIframe.setAttribute('allowfullscreen', '');
      this.playerIframe.style.display = 'none'; // Hide by default
      this.playerWrapper.appendChild(this.playerIframe);
    }

    // Create controls if they don't exist
    if (this.playerWrapper && !this.playerControls) {
      console.log('Player: Creating player controls');
      this.createControls();
    }

    // Refresh elements after creating structure
    this.refreshElements();
  }

  /**
   * Create player controls
   */
  createControls() {
    // Create controls container
    const controls = document.createElement('div');
    controls.id = 'player-controls';

    // Add controls HTML
    controls.innerHTML = `
      <div id="player-title-bar">
        <div id="player-title">Now Playing</div>
      </div>

      <div id="player-progress-container">
        <div id="player-progress-buffer"></div>
        <div id="player-progress-bar"></div>
        <div id="player-time-tooltip">00:00</div>
      </div>

      <div id="player-buttons">
        <div class="player-button-group">
          <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
            <i class="fas fa-play"></i>
          </button>

          <button id="player-rewind" class="player-button" aria-label="Rewind 10 seconds">
            <i class="fas fa-undo"></i>
            <span class="player-button-text">10s</span>
          </button>

          <button id="player-forward" class="player-button" aria-label="Forward 10 seconds">
            <i class="fas fa-redo"></i>
            <span class="player-button-text">10s</span>
          </button>

          <div id="player-volume-container">
            <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
              <i class="fas fa-volume-up"></i>
            </button>
            <div id="player-volume-slider">
              <div id="player-volume-level"></div>
            </div>
          </div>

          <div id="player-time-display">
            <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
          </div>
        </div>

        <div class="player-button-group">
          <button id="player-cc" class="player-button" aria-label="Subtitles">
            <i class="fas fa-closed-captioning"></i>
          </button>
          <button id="player-settings" class="player-button" aria-label="Settings">
            <i class="fas fa-cog"></i>
          </button>
          <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
            <i class="fas fa-expand"></i>
          </button>
        </div>
      </div>
    `;

    // Create settings menu
    const settingsMenu = document.createElement('div');
    settingsMenu.id = 'player-settings-menu';
    settingsMenu.innerHTML = `
      <div class="player-settings-item" data-setting="quality">
        <span>Quality</span>
        <span id="player-quality-value">Auto</span>
      </div>
      <div class="player-settings-item" data-setting="speed">
        <span>Speed</span>
        <span id="player-speed-value">Normal</span>
      </div>
      <div class="player-settings-item" data-setting="subtitles">
        <span>Subtitles</span>
        <span id="player-subtitles-value">Off</span>
      </div>
    `;

    // Create subtitles menu
    const subtitlesMenu = document.createElement('div');
    subtitlesMenu.id = 'player-subtitles-menu';
    subtitlesMenu.innerHTML = `
      <div class="player-settings-section">
        <div class="player-settings-header">Subtitles</div>
        <div class="player-settings-options">
          <button id="subtitle-toggle-btn" class="player-settings-option active" data-subtitle-state="off">Off</button>

          <div class="player-subtitle-upload">
            <label for="subtitle-file">Load SRT File</label>
            <input type="file" id="subtitle-file" accept=".srt" />
          </div>

          <div class="player-subtitle-url">
            <label for="subtitle-url">Paste SRT URL</label>
            <div class="subtitle-url-input-group">
              <input type="text" id="subtitle-url" placeholder="https://example.com/subtitle.srt" />
              <button id="load-subtitle-url-btn">Load</button>
            </div>
          </div>

          <div class="player-subtitle-addic7ed">
            <button id="search-addic7ed-btn" class="subtitle-action-btn">
              <i class="fas fa-search" style="margin-right: 5px;"></i>Search on Addic7ed
            </button>
          </div>
        </div>
      </div>

      <div id="online-subtitles-container" class="online-subtitles-container">
        <div class="subtitle-section-title">Subtitle Information</div>
        <div class="subtitle-info">
          <p>Use the options above to add subtitles to your video.</p>
        </div>
      </div>

      <div class="subtitle-customization">
        <div class="subtitle-size-controls">
          <div class="subtitle-customization-label">Size</div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <button class="subtitle-control-btn" id="subtitle-size-decrease">-</button>
            <span class="subtitle-value" id="subtitle-size-value">100%</span>
            <button class="subtitle-control-btn" id="subtitle-size-increase">+</button>
          </div>
        </div>

        <div class="subtitle-opacity-controls">
          <div class="subtitle-customization-label">Background</div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <button class="subtitle-control-btn" id="subtitle-opacity-decrease">-</button>
            <span class="subtitle-value" id="subtitle-opacity-value">75%</span>
            <button class="subtitle-control-btn" id="subtitle-opacity-increase">+</button>
          </div>
        </div>
      </div>
    `;

    // Add to wrapper
    this.playerWrapper.appendChild(controls);
    this.playerWrapper.appendChild(settingsMenu);
    this.playerWrapper.appendChild(subtitlesMenu);
  }

  /**
   * Add event listeners to player elements
   */
  addEventListeners() {
    console.log('Player: Adding event listeners');

    // Video element events
    if (this.videoElement) {
      // Remove existing listeners to avoid duplicates
      this.videoElement.removeEventListener('play', this.handlePlay);
      this.videoElement.removeEventListener('pause', this.handlePause);
      this.videoElement.removeEventListener('timeupdate', this.handleTimeUpdate);
      this.videoElement.removeEventListener('loadedmetadata', this.handleLoadedMetadata);
      this.videoElement.removeEventListener('progress', this.handleProgress);
      this.videoElement.removeEventListener('ended', this.handleEnded);
      this.videoElement.removeEventListener('volumechange', this.updateVolumeUI);

      // Add listeners
      this.videoElement.addEventListener('play', this.handlePlay);
      this.videoElement.addEventListener('pause', this.handlePause);
      this.videoElement.addEventListener('timeupdate', this.handleTimeUpdate);
      this.videoElement.addEventListener('loadedmetadata', this.handleLoadedMetadata);
      this.videoElement.addEventListener('progress', this.handleProgress);
      this.videoElement.addEventListener('ended', this.handleEnded);
      this.videoElement.addEventListener('volumechange', this.updateVolumeUI);

      // Set initial volume
      this.videoElement.volume = localStorage.getItem('playerVolume') ?
        parseFloat(localStorage.getItem('playerVolume')) : 1;
      this.updateVolumeUI();

      console.log('Player: Added video element event listeners');
    }

    // Control buttons
    if (this.playPauseBtn) {
      this.playPauseBtn.removeEventListener('click', this.togglePlayPause);
      this.playPauseBtn.addEventListener('click', this.togglePlayPause);
      console.log('Player: Added play/pause button event listener');
    }

    // Add mouse enter/leave events for controls to prevent auto-hiding
    if (this.playerControls) {
      this.playerControls.removeEventListener('mouseenter', this.handleControlsMouseEnter);
      this.playerControls.removeEventListener('mouseleave', this.handleControlsMouseLeave);

      this.playerControls.addEventListener('mouseenter', this.handleControlsMouseEnter);
      this.playerControls.addEventListener('mouseleave', this.handleControlsMouseLeave);
      console.log('Player: Added controls mouse enter/leave event listeners');
    }

    if (this.rewindBtn) {
      this.rewindBtn.removeEventListener('click', this.skipBackward);
      this.rewindBtn.addEventListener('click', this.skipBackward);
      console.log('Player: Added rewind button event listener');
    }

    if (this.forwardBtn) {
      this.forwardBtn.removeEventListener('click', this.skipForward);
      this.forwardBtn.addEventListener('click', this.skipForward);
      console.log('Player: Added forward button event listener');
    }

    if (this.volumeBtn) {
      this.volumeBtn.removeEventListener('click', this.toggleMute);
      this.volumeBtn.addEventListener('click', this.toggleMute);
      console.log('Player: Added volume button event listener');
    }

    if (this.fullscreenBtn) {
      this.fullscreenBtn.removeEventListener('click', this.toggleFullscreen);
      this.fullscreenBtn.addEventListener('click', this.toggleFullscreen);
      console.log('Player: Added fullscreen button event listener');
    }

    if (this.settingsBtn) {
      this.settingsBtn.removeEventListener('click', this.toggleSettings);
      this.settingsBtn.addEventListener('click', this.toggleSettings);
      console.log('Player: Added settings button event listener');
    }

    if (this.ccBtn) {
      this.ccBtn.removeEventListener('click', this.toggleSubtitles);
      this.ccBtn.addEventListener('click', this.toggleSubtitles);
      console.log('Player: Added CC button event listener');
    }

    if (this.closeBtn) {
      this.closeBtn.removeEventListener('click', this.hidePlayer);
      this.closeBtn.addEventListener('click', this.hidePlayer);
      console.log('Player: Added close button event listener');
    }

    // Progress bar
    if (this.progressContainer) {
      this.progressContainer.removeEventListener('mousedown', this.startProgressDrag);
      this.progressContainer.removeEventListener('mousemove', this.showProgressTooltip);
      this.progressContainer.removeEventListener('mouseleave', this.hideProgressTooltip);
      this.progressContainer.removeEventListener('click', this.seekToPosition);

      this.progressContainer.addEventListener('mousedown', this.startProgressDrag);
      this.progressContainer.addEventListener('mousemove', this.showProgressTooltip);
      this.progressContainer.addEventListener('mouseleave', this.hideProgressTooltip);
      this.progressContainer.addEventListener('click', this.seekToPosition);
      console.log('Player: Added progress bar event listeners');
    }

    // Volume slider
    if (this.volumeSlider) {
      this.volumeSlider.removeEventListener('mousedown', this.startVolumeDrag);
      this.volumeSlider.removeEventListener('click', this.setVolume);

      this.volumeSlider.addEventListener('mousedown', this.startVolumeDrag);
      this.volumeSlider.addEventListener('click', this.setVolume);
      console.log('Player: Added volume slider event listeners');
    }

    // Settings menu
    if (this.settingsMenu) {
      const settingsItems = this.settingsMenu.querySelectorAll('.player-settings-item');
      settingsItems.forEach(item => {
        item.removeEventListener('click', this.handleSettingChange);
        item.addEventListener('click', this.handleSettingChange);
      });
      console.log('Player: Added settings menu event listeners');
    }

    // Document events for drag operations
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.stopDrag);

    document.addEventListener('mousemove', this.handleDrag);
    document.addEventListener('mouseup', this.stopDrag);
    console.log('Player: Added document-level event listeners');

    // Player wrapper events
    if (this.playerWrapper) {
      this.playerWrapper.removeEventListener('click', this.handlePlayerClick);
      this.playerWrapper.removeEventListener('mousemove', this.showControls);

      this.playerWrapper.addEventListener('click', this.handlePlayerClick);
      this.playerWrapper.addEventListener('mousemove', this.handleMouseMove);

      // Don't hide controls on mouse leave in fullscreen mode
      this.playerWrapper.addEventListener('mouseleave', () => {
        // Only hide controls on mouse leave if not in fullscreen mode
        if (this.isPlaying && !this.isFullscreen) {
          this.scheduleHideControls();
        }
      });
      console.log('Player: Added player wrapper event listeners');
    }

    // Keyboard shortcuts
    document.removeEventListener('keydown', this.handleKeyDown);
    document.addEventListener('keydown', this.handleKeyDown);
    console.log('Player: Added keyboard event listeners');

    // Fullscreen change events
    document.removeEventListener('fullscreenchange', this.updateFullscreenUI);
    document.removeEventListener('webkitfullscreenchange', this.updateFullscreenUI);
    document.removeEventListener('mozfullscreenchange', this.updateFullscreenUI);
    document.removeEventListener('MSFullscreenChange', this.updateFullscreenUI);

    document.addEventListener('fullscreenchange', this.updateFullscreenUI);
    document.addEventListener('webkitfullscreenchange', this.updateFullscreenUI);
    document.addEventListener('mozfullscreenchange', this.updateFullscreenUI);
    document.addEventListener('MSFullscreenChange', this.updateFullscreenUI);
    console.log('Player: Added fullscreen change event listeners');
  }

  /**
   * Connect a video element to the player
   * @returns {boolean} Success status
   */
  connectVideoElement() {
    console.log('Player: Connecting video element');

    // Prevent multiple simultaneous connections
    if (this.isConnecting) {
      console.log('Player: Already connecting video element, skipping');
      return false;
    }

    this.isConnecting = true;

    // Refresh element references
    this.refreshElements();

    // Ensure player structure
    this.ensurePlayerStructure();

    // Add event listeners
    this.addEventListeners();

    // Add event listener for subtitle URL button
    this.addSubtitleUrlEventListener();

    // Add event listener for Addic7ed search button
    this.addAddic7edSearchEventListener();

    // Apply styles to player elements
    this.applyStyles();

    // Explicitly show controls when player starts
    this.showControls();

    // Clear any existing hide timeout to prevent immediate hiding
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
      this.hideControlsTimeout = null;
    }

    // Only schedule hiding controls if the video is playing
    if (this.videoElement && !this.videoElement.paused) {
      // Use a longer initial delay for the first auto-hide
      setTimeout(() => {
        this.scheduleHideControls();
      }, 2000);
    }

    // Reset the connecting flag
    setTimeout(() => {
      this.isConnecting = false;
    }, 100);

    // Call the onVideoElementConnected callback if it exists
    if (typeof this.onVideoElementConnected === 'function') {
      console.log('Player: Calling onVideoElementConnected callback');
      try {
        this.onVideoElementConnected();
      } catch (error) {
        console.error('Player: Error in onVideoElementConnected callback:', error);
      }
    }

    console.log('Player: Video element connected successfully');
    return true;
  }

  /**
   * Apply styles to player elements
   */
  applyStyles() {
    // Add CSS for subtitle action button
    const styleElement = document.getElementById('player-custom-styles');
    if (!styleElement) {
      const customStyles = document.createElement('style');
      customStyles.id = 'player-custom-styles';
      customStyles.textContent = `
        .subtitle-action-btn {
          background-color: #3498db;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 10px;
          transition: background-color 0.3s;
          font-size: 14px;
        }

        .subtitle-action-btn:hover {
          background-color: #2980b9;
        }
      `;
      document.head.appendChild(customStyles);
    }

    // Style the wrapper
    if (this.playerWrapper) {
      this.playerWrapper.style.position = 'relative';
      this.playerWrapper.style.width = '90%';
      this.playerWrapper.style.height = '90%';
      this.playerWrapper.style.maxWidth = '1280px';
      this.playerWrapper.style.maxHeight = '720px';
      this.playerWrapper.style.borderRadius = '8px';
      this.playerWrapper.style.overflow = 'hidden';
      this.playerWrapper.style.background = '#000';
      this.playerWrapper.style.opacity = '1';
      this.playerWrapper.style.transform = 'scale(1)';
      this.playerWrapper.style.visibility = 'visible';
    }

    // Style the video element
    if (this.videoElement) {
      this.videoElement.style.display = 'block';
      this.videoElement.style.width = '100%';
      this.videoElement.style.height = '100%';
      this.videoElement.style.objectFit = 'contain';
      this.videoElement.style.position = 'static';
      this.videoElement.style.zIndex = 'auto';
      this.videoElement.style.opacity = '1';
      this.videoElement.style.visibility = 'visible';
      this.videoElement.style.background = '#000';
    }

    // Style the iframe element if it exists
    if (this.playerIframe) {
      this.playerIframe.style.display = 'none'; // Hide by default, will be shown when needed
      this.playerIframe.style.width = '100%';
      this.playerIframe.style.height = '100%';
      this.playerIframe.style.border = 'none';
      this.playerIframe.style.position = 'absolute';
      this.playerIframe.style.top = '0';
      this.playerIframe.style.left = '0';
      this.playerIframe.style.zIndex = '10'; // Higher z-index to ensure it's on top
      this.playerIframe.style.background = '#000';

      // Ensure the iframe has the allowfullscreen attribute
      this.playerIframe.setAttribute('allowfullscreen', '');

      console.log('Player: Styled iframe element:', {
        display: this.playerIframe.style.display,
        width: this.playerIframe.style.width,
        height: this.playerIframe.style.height,
        zIndex: this.playerIframe.style.zIndex
      });
    }

    // Style the controls
    if (this.playerControls) {
      this.playerControls.style.position = 'absolute';
      this.playerControls.style.bottom = '0';
      this.playerControls.style.left = '0';
      this.playerControls.style.width = '100%';
      this.playerControls.style.padding = '20px 20px';
      this.playerControls.style.background = 'linear-gradient(to top, rgba(0, 0, 0, 0.7) 70%, rgba(0,0,0,0))';
      this.playerControls.style.display = 'flex';
      this.playerControls.style.flexDirection = 'column';
      this.playerControls.style.opacity = '1';
      this.playerControls.style.visibility = 'visible';
      this.playerControls.style.zIndex = '2';
      this.playerControls.classList.add('active');
    }

    // Initialize fullscreen button icon
    if (this.fullscreenBtn) {
      const icon = this.fullscreenBtn.querySelector('i');
      if (icon) {
        // Check current fullscreen state
        const isFullscreen = !!document.fullscreenElement ||
                           !!document.webkitFullscreenElement ||
                           !!document.mozFullScreenElement ||
                           !!document.msFullscreenElement;

        // Set the appropriate icon
        icon.className = isFullscreen ? 'fas fa-compress' : 'fas fa-expand';

        // Update the internal state
        this.isFullscreen = isFullscreen;
      }
    }
  }

  /**
   * Handle play event
   */
  handlePlay() {
    this.isPlaying = true;

    // Update play/pause icon
    if (this.playPauseBtn) {
      const icon = this.playPauseBtn.querySelector('i');
      if (icon) {
        icon.className = 'fas fa-pause';
        console.log('Player: Updated play icon to pause');
      }
    }

    // First show controls, then schedule hiding them
    this.showControls();
    this.scheduleHideControls();

    console.log('Player: Video playback started');
  }

  /**
   * Handle pause event
   */
  handlePause() {
    this.isPlaying = false;

    // Update play/pause icon
    if (this.playPauseBtn) {
      const icon = this.playPauseBtn.querySelector('i');
      if (icon) {
        icon.className = 'fas fa-play';
        console.log('Player: Updated pause icon to play');
      }
    }

    // Show controls when paused
    this.showControls();

    // Clear any hide controls timeout
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
      this.hideControlsTimeout = null;
    }

    console.log('Player: Video playback paused');
  }

  /**
   * Toggle play/pause
   */
  togglePlayPause() {
    if (!this.videoElement) {
      console.error('Player: Video element not found for play/pause');
      return;
    }

    // Refresh element references to ensure we have the latest UI elements
    this.refreshElements();

    console.log('Player: Toggle play/pause, current state:', this.videoElement.paused ? 'paused' : 'playing');

    try {
      if (this.videoElement.paused) {
        // Update UI immediately to give feedback
        if (this.playPauseBtn) {
          const icon = this.playPauseBtn.querySelector('i');
          if (icon) {
            icon.className = 'fas fa-pause';
            console.log('Player: Updated play icon to pause');
          }
        }

        // For HLS streams, we need to make sure the HLS instance is properly attached
        const hlsInstance = this.getHlsInstance();
        if (hlsInstance && hlsInstance.media !== this.videoElement) {
          console.log('Player: Reattaching HLS instance to video element');
          try {
            hlsInstance.attachMedia(this.videoElement);

            // Wait for media to be attached before playing
            hlsInstance.once('mediaAttached', () => {
              console.log('Player: HLS media reattached, attempting to play');
              this.videoElement.play()
                .then(() => {
                  console.log('Player: Play successful after HLS reattachment');
                  this.isPlaying = true;
                  this.scheduleHideControls();
                })
                .catch(err => {
                  console.error('Player: Play error after HLS reattachment:', err);
                  if (this.playPauseBtn) {
                    const icon = this.playPauseBtn.querySelector('i');
                    if (icon) icon.className = 'fas fa-play';
                  }
                  this.isPlaying = false;
                });
            });

            return; // Exit early as we'll play when HLS is attached
          } catch (hlsError) {
            console.error('Player: Error reattaching HLS:', hlsError);
            // Continue with normal play attempt if HLS reattachment fails
          }
        }

        // Try to play
        console.log('Player: Attempting to play video');
        this.videoElement.play()
          .then(() => {
            console.log('Player: Play successful');
            this.isPlaying = true;
            // Schedule hiding controls
            this.scheduleHideControls();
          })
          .catch(err => {
            console.error('Player: Play error:', err);
            // Revert UI if play fails
            if (this.playPauseBtn) {
              const icon = this.playPauseBtn.querySelector('i');
              if (icon) {
                icon.className = 'fas fa-play';
              }
            }
            this.isPlaying = false;
          });
      } else {
        // Pause the video
        console.log('Player: Pausing video');
        this.videoElement.pause();
        this.isPlaying = false;

        // Update UI
        if (this.playPauseBtn) {
          const icon = this.playPauseBtn.querySelector('i');
          if (icon) {
            icon.className = 'fas fa-play';
            console.log('Player: Updated pause icon to play');
          }
        }

        // Keep controls visible when paused
        this.showControls();
      }
    } catch (error) {
      console.error('Player: Error in togglePlayPause:', error);
    }
  }

  /**
   * Handle time update event
   */
  handleTimeUpdate() {
    if (!this.videoElement || !this.progressBar || !this.currentTimeDisplay || !this.durationDisplay) return;

    const currentTime = this.videoElement.currentTime;
    const duration = this.videoElement.duration || 0;

    // Validate the values
    if (!isFinite(currentTime) || !isFinite(duration) || duration <= 0) {
      // Don't update UI with invalid values
      return;
    }

    // Update progress bar
    if (!this.isDraggingProgress) {
      const progress = Math.max(0, Math.min(100, (currentTime / duration) * 100));
      this.progressBar.style.width = `${progress}%`;
    }

    // Update time display
    this.currentTimeDisplay.textContent = this.formatTime(currentTime);
    this.durationDisplay.textContent = this.formatTime(duration);
  }

  /**
   * Handle progress event
   */
  handleProgress() {
    if (!this.videoElement || !this.progressBuffer) return;

    const duration = this.videoElement.duration;

    // Validate the duration
    if (!isFinite(duration) || duration <= 0) {
      return;
    }

    try {
      if (this.videoElement.buffered && this.videoElement.buffered.length > 0) {
        for (let i = 0; i < this.videoElement.buffered.length; i++) {
          if (this.videoElement.buffered.start(i) <= this.videoElement.currentTime &&
              this.videoElement.buffered.end(i) >= this.videoElement.currentTime) {
            const bufferedEnd = this.videoElement.buffered.end(i);
            const bufferedPercent = Math.max(0, Math.min(100, (bufferedEnd / duration) * 100));
            this.progressBuffer.style.width = `${bufferedPercent}%`;
            break;
          }
        }
      }
    } catch (error) {
      console.error('Player: Error updating buffer progress:', error);
    }
  }

  /**
   * Handle loaded metadata event
   */
  handleLoadedMetadata() {
    if (!this.videoElement || !this.durationDisplay) return;

    // Update duration display
    const duration = this.videoElement.duration;
    if (isFinite(duration) && duration > 0) {
      this.durationDisplay.textContent = this.formatTime(duration);
    }

    console.log('Player: Video metadata loaded, duration:', duration);
  }

  /**
   * Handle ended event
   */
  handleEnded() {
    this.isPlaying = false;

    // Update play/pause icon
    if (this.playPauseBtn) {
      const icon = this.playPauseBtn.querySelector('i');
      if (icon) {
        icon.className = 'fas fa-play';
      }
    }

    // Show controls when ended
    this.showControls();

    console.log('Player: Video playback ended');
  }

  /**
   * Format time in seconds to MM:SS format
   * @param {number} seconds - Time in seconds
   * @returns {string} Formatted time
   */
  formatTime(seconds) {
    if (!isFinite(seconds) || seconds < 0) return '00:00';

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * Toggle mute
   */
  toggleMute() {
    if (!this.videoElement) return;

    if (this.videoElement.volume === 0 || this.videoElement.muted) {
      this.videoElement.muted = false;
      this.videoElement.volume = this.lastVolume || 1;
    } else {
      this.lastVolume = this.videoElement.volume;
      this.videoElement.volume = 0;
    }

    this.updateVolumeUI();
    console.log('Player: Toggled mute, volume now:', this.videoElement.volume);
  }

  /**
   * Update volume UI
   */
  updateVolumeUI() {
    if (!this.videoElement || !this.volumeBtn || !this.volumeLevel) return;

    const volume = this.videoElement.muted ? 0 : this.videoElement.volume;

    // Update volume icon
    const icon = this.volumeBtn.querySelector('i');
    if (icon) {
      if (volume === 0) {
        icon.className = 'fas fa-volume-mute';
      } else if (volume < 0.5) {
        icon.className = 'fas fa-volume-down';
      } else {
        icon.className = 'fas fa-volume-up';
      }
    }

    // Update volume slider
    this.volumeLevel.style.width = `${volume * 100}%`;

    // Save volume to localStorage
    localStorage.setItem('playerVolume', volume);
  }

  /**
   * Start progress drag
   * @param {MouseEvent} e - Mouse event
   */
  startProgressDrag(e) {
    e.preventDefault();
    this.isDraggingProgress = true;
    this.seekToPosition(e);
  }

  /**
   * Seek to position
   * @param {MouseEvent} e - Mouse event
   */
  seekToPosition(e) {
    if (!this.videoElement || !this.progressContainer) return;

    // For HLS streams, we need to wait for the manifest to be loaded
    // and the duration to be available
    const hlsInstance = this.getHlsInstance();
    if (hlsInstance && (!this.videoElement.duration || !isFinite(this.videoElement.duration))) {
      console.log('Player: HLS stream detected but duration not available yet, waiting...');

      // For HLS, we can try to get the duration from the HLS instance
      if (hlsInstance.levels && hlsInstance.levels.length > 0) {
        const firstLevel = hlsInstance.levels[0];
        if (firstLevel && firstLevel.details && firstLevel.details.totalduration) {
          const duration = firstLevel.details.totalduration;
          console.log('Player: Using HLS level duration:', duration);

          // Calculate seek time using HLS duration
          const rect = this.progressContainer.getBoundingClientRect();
          const pos = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
          const seekTime = pos * duration;

          try {
            this.videoElement.currentTime = seekTime;
            console.log('Player: Seeking to position (HLS):', seekTime);
            return;
          } catch (error) {
            console.error('Player: Error setting currentTime for HLS:', error);
          }
        }
      }

      // If we can't get the duration from HLS, show a message
      console.warn('Player: Cannot seek yet, waiting for stream to load');
      return;
    }

    // Check if video is ready and has a valid duration
    if (!this.videoElement.duration || !isFinite(this.videoElement.duration)) {
      console.warn('Player: Cannot seek, video duration is not available or not finite');
      return;
    }

    const rect = this.progressContainer.getBoundingClientRect();
    const pos = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const seekTime = pos * this.videoElement.duration;

    // Validate the seek time before setting it
    if (isFinite(seekTime) && seekTime >= 0 && seekTime <= this.videoElement.duration) {
      try {
        this.videoElement.currentTime = seekTime;
        console.log('Player: Seeking to position:', seekTime);
      } catch (error) {
        console.error('Player: Error setting currentTime:', error);
      }
    } else {
      console.warn('Player: Invalid seek time calculated:', seekTime);
    }
  }

  /**
   * Show progress tooltip
   * @param {MouseEvent} e - Mouse event
   */
  showProgressTooltip(e) {
    if (!this.videoElement || !this.progressContainer || !this.timeTooltip) return;

    // Check if video is ready and has a valid duration
    if (!this.videoElement.duration || !isFinite(this.videoElement.duration)) {
      return;
    }

    const rect = this.progressContainer.getBoundingClientRect();
    const pos = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const tooltipTime = pos * this.videoElement.duration;

    // Only update tooltip if time is valid
    if (isFinite(tooltipTime) && tooltipTime >= 0) {
      this.timeTooltip.textContent = this.formatTime(tooltipTime);
      this.timeTooltip.style.left = `${e.clientX - rect.left}px`;
      this.timeTooltip.style.display = 'block';
    }
  }

  /**
   * Hide progress tooltip
   */
  hideProgressTooltip() {
    if (this.timeTooltip) {
      this.timeTooltip.style.display = 'none';
    }
  }

  /**
   * Start volume drag
   * @param {MouseEvent} e - Mouse event
   */
  startVolumeDrag(e) {
    e.preventDefault();
    this.isDraggingVolume = true;
    this.setVolume(e);
  }

  /**
   * Set volume
   * @param {MouseEvent} e - Mouse event
   */
  setVolume(e) {
    if (!this.videoElement || !this.volumeSlider) return;

    const rect = this.volumeSlider.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    const volume = Math.max(0, Math.min(1, pos));

    this.videoElement.muted = volume === 0;
    this.videoElement.volume = volume;
    this.updateVolumeUI();

    console.log('Player: Volume set to:', volume);
  }

  /**
   * Handle drag
   * @param {MouseEvent} e - Mouse event
   */
  handleDrag(e) {
    if (this.isDraggingProgress) {
      this.seekToPosition(e);
    } else if (this.isDraggingVolume) {
      this.setVolume(e);
    }
  }

  /**
   * Stop drag
   */
  stopDrag() {
    this.isDraggingProgress = false;
    this.isDraggingVolume = false;
  }

  /**
   * Toggle fullscreen
   */
  toggleFullscreen() {
    if (!this.playerWrapper) {
      console.error('Player: Player wrapper not found for fullscreen toggle');
      return;
    }

    // Check current fullscreen state directly from document
    const isCurrentlyFullscreen = !!document.fullscreenElement ||
                                 !!document.webkitFullscreenElement ||
                                 !!document.mozFullScreenElement ||
                                 !!document.msFullscreenElement;

    console.log('Player: Toggling fullscreen, current state:', isCurrentlyFullscreen ? 'fullscreen' : 'normal');

    try {
      if (!isCurrentlyFullscreen) {
        // Request fullscreen
        console.log('Player: Entering fullscreen mode');

        // Apply fullscreen class to wrapper
        if (this.playerWrapper) {
          this.playerWrapper.classList.add('fullscreen-mode');

          // Add mouse movement detection for auto-hiding controls
          this.playerWrapper.removeEventListener('mousemove', this.handleMouseMove);
          this.playerWrapper.addEventListener('mousemove', this.handleMouseMove);
          console.log('Player: Added mouse movement detection for fullscreen mode');

          // Set flag for fullscreen mode
          this.isFullscreen = true;

          // Reset mouse tracking variables for fullscreen mode
          this.lastMousePosition = null;
          this.lastMouseMoveTime = Date.now();
          this.mouseHasMovedSinceFullscreen = false;
          this.mouseStationaryStartTime = null;

          // Show controls initially when entering fullscreen
          this.showControls();

          // Set a timer to force hide controls after entering fullscreen
          // This ensures controls will hide even if the user doesn't move the mouse
          if (this.fullscreenInitialHideTimer) {
            clearTimeout(this.fullscreenInitialHideTimer);
          }

          this.fullscreenInitialHideTimer = setTimeout(() => {
            if (this.isFullscreen && !this.isInteractingWithControls && !this.mouseHasMovedSinceFullscreen) {
              console.log('Player: Initial fullscreen hide timer triggered');
              this.hideControls();

              // Force cursor to be hidden
              if (this.playerWrapper) {
                this.playerWrapper.classList.add('cursor-hidden');
              }
            }
            this.fullscreenInitialHideTimer = null;
          }, 3000); // Hide after 3 seconds in fullscreen if mouse hasn't moved
        }

        // Let CSS handle the logo and controls visibility

        // Start idle detection for auto-hiding controls
        console.log('Player: Starting idle detection in fullscreen mode');
        this.startIdleDetection();

        if (this.playerWrapper.requestFullscreen) {
          this.playerWrapper.requestFullscreen();
        } else if (this.playerWrapper.webkitRequestFullscreen) {
          this.playerWrapper.webkitRequestFullscreen();
        } else if (this.playerWrapper.mozRequestFullScreen) {
          this.playerWrapper.mozRequestFullScreen();
        } else if (this.playerWrapper.msRequestFullscreen) {
          this.playerWrapper.msRequestFullscreen();
        } else {
          console.log('Player: Fullscreen API not supported');
        }
      } else {
        // Exit fullscreen
        console.log('Player: Exiting fullscreen mode');

        // Remove fullscreen class from wrapper
        if (this.playerWrapper) {
          this.playerWrapper.classList.remove('fullscreen-mode');

          // Remove mouse movement detection for auto-hiding controls
          this.playerWrapper.removeEventListener('mousemove', this.handleMouseMove);
          console.log('Player: Removed mouse movement detection for fullscreen mode');

          // Reset flag for fullscreen mode
          this.isFullscreen = false;
        }

        // Let CSS handle the logo and controls visibility

        // Clear all timers
        if (this.autoHideTimer) {
          clearTimeout(this.autoHideTimer);
          this.autoHideTimer = null;
        }

        if (this.idleTimer) {
          clearTimeout(this.idleTimer);
          this.idleTimer = null;
        }

        // Reset mouse position tracking
        this.lastMousePosition = null;

        // Remove cursor-hidden class if it exists
        this.playerWrapper.classList.remove('cursor-hidden');

        // Ensure cursor is visible when exiting fullscreen
        this.playerWrapper.style.removeProperty('cursor');

        // Make sure all child elements also show cursor
        const allElements = this.playerWrapper.querySelectorAll('*');
        allElements.forEach(el => {
          el.style.removeProperty('cursor');
        });

        // Show controls in normal mode
        this.showControls();

        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        } else {
          console.log('Player: Fullscreen API not supported');
        }
      }

      // Update the UI immediately for better feedback
      this.updateFullscreenUI();
    } catch (error) {
      console.error('Player: Error toggling fullscreen:', error);
    }
  }

  /**
   * Update fullscreen UI
   */
  updateFullscreenUI = () => {
    // Check if we're in fullscreen mode
    const wasFullscreen = this.isFullscreen;
    this.isFullscreen = !!document.fullscreenElement ||
                 !!document.webkitFullscreenElement ||
                 !!document.mozFullScreenElement ||
                 !!document.msFullscreenElement;

    if (wasFullscreen !== this.isFullscreen) {
      console.log('Player: Fullscreen state changed to:', this.isFullscreen ? 'fullscreen' : 'normal');
    }

    // Update the fullscreen icon
    if (this.fullscreenBtn) {
      const icon = this.fullscreenBtn.querySelector('i');
      if (icon) {
        icon.className = this.isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
      }
    }

    // Check if we're showing an iframe
    const isIframeVisible = this.playerIframe &&
                           this.playerIframe.style.display === 'block' &&
                           this.playerIframe.src &&
                           this.playerIframe.src !== 'about:blank';

    // Apply fullscreen-specific styles
    if (this.playerWrapper) {
      if (this.isFullscreen) {
        // Apply fullscreen styles
        this.playerWrapper.classList.add('fullscreen-mode');

        // Make sure controls are properly positioned in fullscreen
        if (this.playerControls) {
          this.playerControls.style.bottom = '0';
          this.playerControls.style.left = '0';
          this.playerControls.style.width = '100%';
          this.playerControls.style.padding = '20px 20px';
        }

        // Start idle detection for auto-hiding controls
        console.log('Player: Starting idle detection in updateFullscreenUI');
        this.startIdleDetection();

        // Add mouse movement detection
        this.playerWrapper.removeEventListener('mousemove', this.handleMouseMove);
        this.playerWrapper.addEventListener('mousemove', this.handleMouseMove);
      } else {
        // Remove fullscreen styles
        this.playerWrapper.classList.remove('fullscreen-mode');

        // Remove cursor-hidden class
        this.playerWrapper.classList.remove('cursor-hidden');

        // Ensure cursor is visible when exiting fullscreen
        this.playerWrapper.style.removeProperty('cursor');

        // Make sure all child elements also show cursor
        const allElements = this.playerWrapper.querySelectorAll('*');
        allElements.forEach(el => {
          el.style.removeProperty('cursor');
        });

        // Clear all timers
        if (this.idleTimer) {
          clearTimeout(this.idleTimer);
          this.idleTimer = null;
        }

        // Remove mouse movement detection
        this.playerWrapper.removeEventListener('mousemove', this.handleMouseMove);
      }
    }

    // Handle controls visibility for iframe content
    if (isIframeVisible && this.playerControls) {
      // Always hide controls for iframe content
      this.playerControls.style.display = 'none';
      console.log('Player: Hiding controls for iframe content');

      // Also hide logo for iframe content
      const playerLogo = document.getElementById('player-logo');
      if (playerLogo) {
        playerLogo.style.display = 'none';
      }
    } else if (!isIframeVisible && !this.isFullscreen && this.playerControls) {
      // Show controls in normal mode for non-iframe content
      this.playerControls.style.display = 'flex';
      this.playerControls.style.opacity = '1';
      this.playerControls.style.visibility = 'visible';
      console.log('Player: Showing controls in normal mode');

      // Show logo in normal mode
      const playerLogo = document.getElementById('player-logo');
      if (playerLogo) {
        playerLogo.style.display = 'block';
      }
    }
  }

  /**
   * Toggle settings menu
   */
  toggleSettings() {
    if (!this.settingsMenu) {
      this.settingsMenu = document.getElementById('player-settings-menu');
      if (!this.settingsMenu) return;
    }

    // Toggle the active class
    this.settingsMenu.classList.toggle('active');

    // If the menu is now active, position it properly
    if (this.settingsMenu.classList.contains('active')) {
      // Make sure the menu is visible and properly styled
      this.settingsMenu.style.position = 'absolute';
      this.settingsMenu.style.bottom = '70px';
      this.settingsMenu.style.right = '20px';
      this.settingsMenu.style.background = 'rgba(28, 28, 28, 0.9)';
      this.settingsMenu.style.backdropFilter = 'blur(10px)';
      this.settingsMenu.style.webkitBackdropFilter = 'blur(10px)';
      this.settingsMenu.style.borderRadius = '8px';
      this.settingsMenu.style.padding = '12px';
      this.settingsMenu.style.display = 'flex';
      this.settingsMenu.style.flexDirection = 'column';
      this.settingsMenu.style.gap = '10px';
      this.settingsMenu.style.minWidth = '200px';
      this.settingsMenu.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
      this.settingsMenu.style.zIndex = '3';

      console.log('Player: Settings menu opened');
    } else {
      console.log('Player: Settings menu closed');
    }
  }

  /**
   * Handle setting change
   * @param {Event} e - Click event
   */
  handleSettingChange(e) {
    const setting = e.currentTarget.dataset.setting;

    if (setting === 'quality') {
      // Handle quality change
      console.log('Player: Quality setting clicked');

      // Toggle through quality options
      this.cycleQuality();
    } else if (setting === 'speed') {
      // Handle speed change
      console.log('Player: Speed setting clicked');

      // Toggle through speed options
      const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2];
      const currentSpeed = this.videoElement ? this.videoElement.playbackRate : 1;
      const currentIndex = speeds.indexOf(currentSpeed);
      const nextIndex = (currentIndex + 1) % speeds.length;
      const newSpeed = speeds[nextIndex];

      if (this.videoElement) {
        this.videoElement.playbackRate = newSpeed;

        // Update the speed value display
        const speedValue = document.getElementById('player-speed-value');
        if (speedValue) {
          speedValue.textContent = newSpeed === 1 ? 'Normal' : `${newSpeed}x`;
        }

        console.log('Player: Playback speed set to:', newSpeed);
      }
    } else if (setting === 'subtitles') {
      // Handle subtitles setting
      console.log('Player: Subtitles setting clicked');

      // Toggle subtitles menu
      this.toggleSubtitles();

      // Hide settings menu
      if (this.settingsMenu) {
        this.settingsMenu.classList.remove('active');
      }
    }
  }

  /**
   * Cycle through available quality options
   */
  cycleQuality = () => {
    const hlsInstance = this.getHlsInstance();
    if (!hlsInstance) {
      console.log('Player: No HLS instance available');
      return;
    }

    // Check if levels are available
    if (!hlsInstance.levels || !Array.isArray(hlsInstance.levels) || hlsInstance.levels.length <= 1) {
      console.log('Player: No quality levels available or only one level');
      return;
    }

    // Log all available levels for debugging
    console.log('Player: Available quality levels:', hlsInstance.levels.length);
    hlsInstance.levels.forEach((level, index) => {
      const height = level.height || 'unknown';
      const bitrate = level.bitrate ? Math.round(level.bitrate / 1000) + ' kbps' : 'unknown bitrate';
      console.log(`Player: Level ${index}: ${height}p (${bitrate})`);
    });

    // Get current quality level
    const currentLevel = hlsInstance.currentLevel;
    console.log('Player: Current quality level:', currentLevel);

    // Determine next quality level
    // Cycle: Auto -> Highest Quality -> Second Highest -> ... -> Lowest -> Auto
    let nextLevel;

    if (currentLevel === -1) {
      // Currently on Auto, switch to highest quality (index 0)
      nextLevel = 0;
      console.log('Player: Switching from Auto to highest quality (index 0)');
    } else if (currentLevel >= hlsInstance.levels.length - 1) {
      // Currently on lowest quality, switch back to Auto
      nextLevel = -1;
      console.log('Player: Switching from lowest quality to Auto');
    } else {
      // Move to next lower quality
      nextLevel = currentLevel + 1;
      console.log(`Player: Switching from level ${currentLevel} to level ${nextLevel}`);
    }

    try {
      // Get the quality label before changing the level
      let qualityLabel = 'Auto';
      if (nextLevel !== -1 && hlsInstance.levels && hlsInstance.levels[nextLevel]) {
        const level = hlsInstance.levels[nextLevel];
        qualityLabel = level.height ? `${level.height}p` :
                     (level.bitrate ? `${Math.round(level.bitrate / 1000)} kbps` : `Level ${nextLevel + 1}`);
      }

      // Set the new quality level
      console.log('Player: Setting quality level to', nextLevel, `(${qualityLabel})`);

      // Update UI immediately for better feedback
      const qualityValue = document.getElementById('player-quality-value');
      if (qualityValue) {
        qualityValue.textContent = qualityLabel;

        // Add a visual indicator for the change
        this.showQualityChangeIndicator(qualityLabel);
      }

      // Force update of autoLevelEnabled
      if (nextLevel === -1) {
        hlsInstance.autoLevelEnabled = true;
        hlsInstance.currentLevel = -1;
        hlsInstance.nextLevel = -1;
        console.log('Player: Enabled auto level selection');
      } else {
        hlsInstance.autoLevelEnabled = false;
        hlsInstance.loadLevel = nextLevel; // For immediate loading
        hlsInstance.nextLevel = nextLevel; // For next segment
        hlsInstance.currentLevel = nextLevel; // For current state
        console.log('Player: Disabled auto level selection, set level to', nextLevel);
      }

      // Add a class to the quality value to indicate it's changing
      if (qualityValue) {
        qualityValue.classList.add('changing');
        setTimeout(() => {
          qualityValue.classList.remove('changing');
        }, 1000);
      }
    } catch (error) {
      console.error('Player: Error setting quality level:', error);
    }

    // Show controls
    this.showControls();
  }

  /**
   * Show a temporary indicator when quality changes
   * @param {string} qualityLabel - The quality label to display
   */
  showQualityChangeIndicator = (qualityLabel) => {
    // Create or get the indicator element
    let indicator = document.getElementById('quality-change-indicator');
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'quality-change-indicator';

      // Add to player wrapper
      if (this.playerWrapper) {
        this.playerWrapper.appendChild(indicator);
      }
    }

    // Set the text and show the indicator
    indicator.textContent = `Quality: ${qualityLabel}`;

    // Clear any existing timeout
    if (this.qualityIndicatorTimeout) {
      clearTimeout(this.qualityIndicatorTimeout);
    }

    // Make it visible
    setTimeout(() => {
      indicator.style.opacity = '1';
    }, 10);

    // Hide after 2 seconds
    this.qualityIndicatorTimeout = setTimeout(() => {
      indicator.style.opacity = '0';
    }, 2000);
  }

  /**
   * Handle key down event
   * @param {KeyboardEvent} e - Keyboard event
   */
  handleKeyDown(e) {
    // Only handle keys when player is visible
    if (!this.playerContainer || this.playerContainer.classList.contains('hidden')) {
      return;
    }

    switch (e.key) {
      case ' ':
      case 'k':
        // Space or K - Toggle play/pause
        e.preventDefault();
        this.togglePlayPause();
        break;
      case 'f':
        // F - Toggle fullscreen
        e.preventDefault();
        this.toggleFullscreen();
        break;
      case 'm':
        // M - Toggle mute
        e.preventDefault();
        this.toggleMute();
        break;
      case 'c':
        // C - Toggle subtitles
        e.preventDefault();
        this.toggleSubtitles();
        break;
      case 'q':
        // Q - Cycle through quality options
        e.preventDefault();
        this.cycleQuality();
        break;
      case 'ArrowLeft':
        // Left arrow - Seek backward 10 seconds
        e.preventDefault();
        this.skipBackward();
        break;
      case 'ArrowRight':
        // Right arrow - Seek forward 10 seconds
        e.preventDefault();
        this.skipForward();
        break;
      case 'j':
        // J - Seek backward 10 seconds (YouTube-style)
        e.preventDefault();
        this.skipBackward();
        break;
      case 'l':
        // L - Seek forward 10 seconds (YouTube-style)
        e.preventDefault();
        this.skipForward();
        break;
      case 'ArrowUp':
        // Up arrow - Increase volume
        e.preventDefault();
        if (this.videoElement) {
          this.videoElement.volume = Math.min(1, this.videoElement.volume + 0.1);
          this.videoElement.muted = false;
          this.updateVolumeUI();
        }
        break;
      case 'ArrowDown':
        // Down arrow - Decrease volume
        e.preventDefault();
        if (this.videoElement) {
          this.videoElement.volume = Math.max(0, this.videoElement.volume - 0.1);
          this.updateVolumeUI();
        }
        break;
      case 'Escape':
        // Escape - Exit fullscreen or close player
        e.preventDefault();
        if (this.isFullscreen) {
          this.toggleFullscreen();
        } else {
          this.hidePlayer();
        }
        break;
    }
  }

  /**
   * Skip forward by 10 seconds
   */
  skipForward = () => {
    if (!this.videoElement) return;

    console.log('Player: Skipping forward 10 seconds');

    try {
      // Calculate new time (capped at duration)
      const newTime = Math.min(
        this.videoElement.duration || 0,
        this.videoElement.currentTime + 10
      );

      // Set new time
      this.videoElement.currentTime = newTime;

      // Show controls
      this.showControls();

      // Update progress bar immediately for better feedback
      this.handleTimeUpdate();

      console.log('Player: Skipped forward to', newTime);
    } catch (error) {
      console.error('Player: Error skipping forward:', error);
    }
  }

  /**
   * Skip backward by 10 seconds
   */
  skipBackward = () => {
    if (!this.videoElement) return;

    console.log('Player: Skipping backward 10 seconds');

    try {
      // Calculate new time (minimum 0)
      const newTime = Math.max(0, this.videoElement.currentTime - 10);

      // Set new time
      this.videoElement.currentTime = newTime;

      // Show controls
      this.showControls();

      // Update progress bar immediately for better feedback
      this.handleTimeUpdate();

      console.log('Player: Skipped backward to', newTime);
    } catch (error) {
      console.error('Player: Error skipping backward:', error);
    }
  }

  /**
   * Toggle subtitles menu
   */
  toggleSubtitles = () => {
    console.log('Player: Toggling subtitles menu');

    // Refresh elements to ensure we have the latest references
    this.refreshElements();

    const subtitlesMenu = document.getElementById('player-subtitles-menu');
    if (!subtitlesMenu) {
      console.error('Player: Subtitles menu not found');
      return;
    }

    // Toggle the active class
    const isActive = subtitlesMenu.classList.contains('active');

    // Hide settings menu if it's open
    if (this.settingsMenu && this.settingsMenu.classList.contains('active')) {
      this.settingsMenu.classList.remove('active');
    }

    // If we're showing the menu, update the title from UI first
    if (!isActive) {
      this.updateTitleFromUI();
    }

    // Toggle subtitles menu
    if (isActive) {
      subtitlesMenu.classList.remove('active');
      console.log('Player: Subtitles menu hidden');
    } else {
      subtitlesMenu.classList.add('active');
      console.log('Player: Subtitles menu shown');

      // Add event listener to the file input if it doesn't have one
      const fileInput = document.getElementById('subtitle-file');
      if (fileInput) {
        fileInput.removeEventListener('change', this.handleSubtitleFileUpload);
        fileInput.addEventListener('change', this.handleSubtitleFileUpload);
      }

      // Add event listeners for subtitle toggle button
      const toggleBtn = document.getElementById('subtitle-toggle-btn');
      if (toggleBtn) {
        toggleBtn.removeEventListener('click', this.toggleSubtitleDisplay);
        toggleBtn.addEventListener('click', this.toggleSubtitleDisplay);

        // Update button state based on current subtitles state
        if (this.subtitlesEnabled) {
          toggleBtn.textContent = 'Subtitles: On';
          toggleBtn.dataset.subtitleState = 'on';
          toggleBtn.classList.add('active');
        } else {
          toggleBtn.textContent = 'Subtitles: Off';
          toggleBtn.dataset.subtitleState = 'off';
          toggleBtn.classList.remove('active');
        }
      }

      // Add event listeners for subtitle customization
      const sizeDecreaseBtn = document.getElementById('subtitle-size-decrease');
      const sizeIncreaseBtn = document.getElementById('subtitle-size-increase');
      const opacityDecreaseBtn = document.getElementById('subtitle-opacity-decrease');
      const opacityIncreaseBtn = document.getElementById('subtitle-opacity-increase');

      if (sizeDecreaseBtn) {
        sizeDecreaseBtn.removeEventListener('click', this.decreaseSubtitleSize);
        sizeDecreaseBtn.addEventListener('click', this.decreaseSubtitleSize);
      }

      if (sizeIncreaseBtn) {
        sizeIncreaseBtn.removeEventListener('click', this.increaseSubtitleSize);
        sizeIncreaseBtn.addEventListener('click', this.increaseSubtitleSize);
      }

      if (opacityDecreaseBtn) {
        opacityDecreaseBtn.removeEventListener('click', this.decreaseSubtitleOpacity);
        opacityDecreaseBtn.addEventListener('click', this.decreaseSubtitleOpacity);
      }

      if (opacityIncreaseBtn) {
        opacityIncreaseBtn.removeEventListener('click', this.increaseSubtitleOpacity);
        opacityIncreaseBtn.addEventListener('click', this.increaseSubtitleOpacity);
      }

      // Show the subtitle information section
      this.showSubtitleInfo();

      // Add event listener for subtitle URL button
      this.addSubtitleUrlEventListener();

      // Add event listener for Addic7ed search button
      this.addAddic7edSearchEventListener();

      // Ensure Addic7ed service is loaded
      this.ensureAddic7edServiceLoaded();
    }

    // Show controls
    this.showControls();
  }

  /**
   * Show subtitle information section
   */
  showSubtitleInfo = () => {
    // Get the online subtitles container
    const onlineSubtitlesContainer = document.getElementById('online-subtitles-container');
    if (!onlineSubtitlesContainer) {
      console.error('Player: Online subtitles container not found');
      return;
    }

    // Show subtitle information
    onlineSubtitlesContainer.innerHTML = `
      <div class="subtitle-section-title">Subtitle Information</div>
      <div class="subtitle-info">
        <p>Use the "Load SRT File" option above to add subtitles to your video.</p>
        <p>You can adjust the size and opacity of subtitles below.</p>
      </div>
    `;
  }

  /**
   * Search for online subtitles using OpenSubtitles API (DEPRECATED)
   */
  searchOnlineSubtitles = async () => {
    // Check if OpenSubtitles service is available
    if (!window.openSubtitlesService) {
      console.error('Player: OpenSubtitles service not available');
      return;
    }

    // Get the online subtitles container
    const onlineSubtitlesContainer = document.getElementById('online-subtitles-container');
    if (!onlineSubtitlesContainer) {
      console.error('Player: Online subtitles container not found');
      return;
    }

    // Show loading indicator
    onlineSubtitlesContainer.innerHTML = '<div class="subtitle-loading">Searching for subtitles...</div>';

    // Get media info
    const mediaTitle = this.currentMediaTitle;
    if (!mediaTitle) {
      console.log('Player: No media title available for subtitle search');
      onlineSubtitlesContainer.innerHTML = '<div class="subtitle-error">No media information available for subtitle search</div>';
      return;
    }

    console.log('Player: Searching for subtitles for:', mediaTitle);

    try {
      // Check if OpenSubtitles service is rate limited
      if (window.openSubtitlesService.isRateLimited) {
        const now = new Date();
        if (window.openSubtitlesService.rateLimitResetTime && window.openSubtitlesService.rateLimitResetTime > now) {
          const minutesRemaining = Math.ceil((window.openSubtitlesService.rateLimitResetTime - now) / 60000);
          console.log(`Player: OpenSubtitles rate limited for ${minutesRemaining} more minutes`);

          onlineSubtitlesContainer.innerHTML = `
            <div class="subtitle-error">
              <p>OpenSubtitles API is currently rate limited.</p>
              <p>Please try again in ${minutesRemaining} minutes.</p>
              <p>You can still upload your own subtitle file below.</p>
              <button id="clear-rate-limit-btn" class="subtitle-action-btn">Reset Rate Limit</button>
            </div>
            <div class="subtitle-upload-section">
              <label for="subtitle-file" class="subtitle-upload-label">Upload SRT File</label>
              <input type="file" id="subtitle-file" accept=".srt" class="subtitle-file-input" />
            </div>
          `;

          // Add event listener to the clear rate limit button
          const clearRateLimitBtn = document.getElementById('clear-rate-limit-btn');
          if (clearRateLimitBtn) {
            clearRateLimitBtn.addEventListener('click', () => {
              if (window.openSubtitlesService && typeof window.openSubtitlesService.clearRateLimit === 'function') {
                window.openSubtitlesService.clearRateLimit();
                alert('Rate limit cleared. You can now try searching for subtitles again.');
                this.searchOnlineSubtitles(); // Try searching again
              }
            });
          }

          // Add event listener to the file input
          const fileInput = document.getElementById('subtitle-file');
          if (fileInput) {
            fileInput.addEventListener('change', this.handleSubtitleFileUpload);
          }

          return;
        }
      }

      // Initialize OpenSubtitles service if needed
      if (!window.openSubtitlesService.isLoggedIn) {
        await window.openSubtitlesService.initialize();
      }

      // Parse the title to extract TV show information
      let searchParams = {};
      let showTitle, seasonNumber, episodeNumber;

      // Check if it's a TV show episode (format: "Series: Show Title - S01:E05")
      const tvShowMatch = mediaTitle.match(/(?:Series|Anime):\s+(.+?)\s+-\s+S(\d+):E(\d+)/i);

      if (tvShowMatch) {
        showTitle = tvShowMatch[1].trim();
        seasonNumber = parseInt(tvShowMatch[2], 10);
        episodeNumber = parseInt(tvShowMatch[3], 10);

        console.log('Player: Detected TV show:', showTitle, 'Season:', seasonNumber, 'Episode:', episodeNumber);

        // Set search parameters for TV show
        searchParams = {
          query: showTitle,
          season_number: seasonNumber,
          episode_number: episodeNumber,
          type: 'episode'
        };
      } else {
        // Regular title search for movies
        searchParams = {
          query: mediaTitle
        };
      }

      console.log('Player: Searching with params:', searchParams);
      const subtitles = await window.openSubtitlesService.searchSubtitles(searchParams);

      // If no results with specific parameters, try a more general search for TV shows
      if ((!subtitles || subtitles.length === 0) && tvShowMatch) {
        console.log('Player: No results with specific parameters, trying more general search');

        // Try with just the show title and season
        const fallbackParams = {
          query: showTitle,
          season_number: seasonNumber
        };

        console.log('Player: Fallback search with params:', fallbackParams);
        const fallbackSubtitles = await window.openSubtitlesService.searchSubtitles(fallbackParams);

        if (fallbackSubtitles && fallbackSubtitles.length > 0) {
          console.log('Player: Found subtitles with fallback search');
          // Use these results instead
          return this.displaySubtitles(fallbackSubtitles, onlineSubtitlesContainer);
        }

        // If still no results, try with just the show title
        if (!fallbackSubtitles || fallbackSubtitles.length === 0) {
          console.log('Player: No results with season, trying just show title');
          const titleOnlyParams = {
            query: showTitle
          };

          console.log('Player: Title-only search with params:', titleOnlyParams);
          const titleOnlySubtitles = await window.openSubtitlesService.searchSubtitles(titleOnlyParams);

          if (titleOnlySubtitles && titleOnlySubtitles.length > 0) {
            console.log('Player: Found subtitles with title-only search');
            // Use these results
            return this.displaySubtitles(titleOnlySubtitles, onlineSubtitlesContainer);
          }
        }
      }

      if (!subtitles || subtitles.length === 0) {
        console.log('Player: No subtitles found after all attempts');
        onlineSubtitlesContainer.innerHTML = `
          <div class="subtitle-error">
            <p>No subtitles found for this title.</p>
            <p>You can upload your own subtitle file below.</p>
            <button id="clear-rate-limit-btn" class="subtitle-action-btn">Reset API Rate Limit</button>
          </div>
          <div class="subtitle-upload-section">
            <label for="subtitle-file" class="subtitle-upload-label">Upload SRT File</label>
            <input type="file" id="subtitle-file" accept=".srt" class="subtitle-file-input" />
          </div>
        `;

        // Add event listener to the file input
        const fileInput = document.getElementById('subtitle-file');
        if (fileInput) {
          fileInput.addEventListener('change', this.handleSubtitleFileUpload);
        }

        // Add event listener to the clear rate limit button
        const clearRateLimitBtn = document.getElementById('clear-rate-limit-btn');
        if (clearRateLimitBtn) {
          clearRateLimitBtn.addEventListener('click', () => {
            if (window.openSubtitlesService && typeof window.openSubtitlesService.clearRateLimit === 'function') {
              window.openSubtitlesService.clearRateLimit();
              alert('Rate limit cleared. You can now try searching for subtitles again.');
              this.searchOnlineSubtitles(); // Try searching again
            }
          });
        }

        return;
      }

      // Display the subtitles
      return this.displaySubtitles(subtitles, onlineSubtitlesContainer);
    } catch (error) {
      console.error('Player: Error searching for subtitles', error);
      const container = document.getElementById('online-subtitles-container');
      if (container) {
        container.innerHTML = `
          <div class="subtitle-error">
            <p>Error searching for subtitles.</p>
            <p>You can upload your own subtitle file below.</p>
            <button id="clear-rate-limit-btn" class="subtitle-action-btn">Reset API Rate Limit</button>
          </div>
          <div class="subtitle-upload-section">
            <label for="subtitle-file" class="subtitle-upload-label">Upload SRT File</label>
            <input type="file" id="subtitle-file" accept=".srt" class="subtitle-file-input" />
          </div>
        `;

        // Add event listener to the file input
        const fileInput = document.getElementById('subtitle-file');
        if (fileInput) {
          fileInput.addEventListener('change', this.handleSubtitleFileUpload);
        }

        // Add event listener to the clear rate limit button
        const clearRateLimitBtn = document.getElementById('clear-rate-limit-btn');
        if (clearRateLimitBtn) {
          clearRateLimitBtn.addEventListener('click', () => {
            if (window.openSubtitlesService && typeof window.openSubtitlesService.clearRateLimit === 'function') {
              window.openSubtitlesService.clearRateLimit();
              alert('Rate limit cleared. You can now try searching for subtitles again.');
              this.searchOnlineSubtitles(); // Try searching again
            }
          });
        }
      }
    }
  }

  /**
   * Display subtitles in the container
   * @param {Array} subtitles - Array of subtitle objects
   * @param {HTMLElement} container - Container element
   */
  displaySubtitles = (subtitles, container) => {
    try {
      console.log('Player: Found', subtitles.length, 'subtitles');

      // Group subtitles by language
      const subtitlesByLanguage = {};
      subtitles.forEach(subtitle => {
        if (!subtitle.attributes) {
          console.warn('Player: Subtitle missing attributes:', subtitle);
          return;
        }

        const language = subtitle.attributes.language;
        if (!subtitlesByLanguage[language]) {
          subtitlesByLanguage[language] = [];
        }
        subtitlesByLanguage[language].push(subtitle);
      });

      // Create HTML for subtitle options
      let html = '<div class="subtitle-language-groups">';

      // Sort languages alphabetically
      const languages = Object.keys(subtitlesByLanguage).sort();

      if (languages.length === 0) {
        container.innerHTML = '<div class="subtitle-error">No valid subtitles found</div>';
        return;
      }

      for (const language of languages) {
        const languageSubtitles = subtitlesByLanguage[language];

        html += `
          <div class="subtitle-language-group">
            <div class="subtitle-language-header">${language}</div>
            <div class="subtitle-language-options">
        `;

        // Sort subtitles by download count (most popular first)
        languageSubtitles.sort((a, b) =>
          (b.attributes.download_count || 0) - (a.attributes.download_count || 0)
        );

        // Show top 5 subtitles for each language (increased from 3)
        const topSubtitles = languageSubtitles.slice(0, 5);

        for (const subtitle of topSubtitles) {
          const attr = subtitle.attributes;

          // Skip if no files array or it's empty
          if (!attr.files || !attr.files.length) {
            console.warn('Player: Subtitle missing files:', subtitle);
            continue;
          }

          const fileId = attr.files[0].file_id;
          const format = attr.files[0].file_format;
          const downloads = attr.download_count || 0;

          // Get a meaningful release name
          let release = attr.release || 'Unknown release';

          // If we have episode info, add it to the release name
          if (attr.episode_number) {
            // Check if release already contains episode info
            if (!release.includes(`E${attr.episode_number}`)) {
              release = `${release} - E${attr.episode_number}`;
            }
          }

          // Add season info if available and not already in release name
          if (attr.season_number && !release.includes(`S${attr.season_number}`)) {
            release = `S${attr.season_number} - ${release}`;
          }

          // Add upload date if available
          const uploadDate = attr.upload_date ?
            ` (${new Date(attr.upload_date).toLocaleDateString()})` : '';

          html += `
            <button class="online-subtitle-option" data-file-id="${fileId}" data-format="${format}">
              <div class="subtitle-option-name">${release}${uploadDate}</div>
              <div class="subtitle-option-info">
                <span class="subtitle-downloads">${downloads} downloads</span>
                <span class="subtitle-format">${format}</span>
              </div>
            </button>
          `;
        }

        html += `
            </div>
          </div>
        `;
      }

      html += '</div>';

      // Update the container
      container.innerHTML = html;

      // Add event listeners to subtitle options
      const subtitleOptions = container.querySelectorAll('.online-subtitle-option');
      subtitleOptions.forEach(option => {
        option.addEventListener('click', this.handleOnlineSubtitleSelect);
      });
    } catch (error) {
      console.error('Player: Error displaying subtitles', error);
      if (container) {
        container.innerHTML = '<div class="subtitle-error">Error displaying subtitles</div>';
      }
    }
  }

  /**
   * Handle online subtitle selection
   * @param {Event} e - Click event
   */
  handleOnlineSubtitleSelect = async (e) => {
    const button = e.currentTarget;
    const fileId = button.dataset.fileId;
    const format = button.dataset.format;

    if (!fileId) {
      console.error('Player: No file ID for subtitle download');
      return;
    }

    console.log('Player: Downloading subtitle file ID:', fileId, 'format:', format);

    // Show loading state
    button.disabled = true;
    const originalButtonContent = button.innerHTML;
    button.innerHTML = '<div class="subtitle-loading">Downloading...</div>';

    try {
      // Get download link
      const downloadInfo = await window.openSubtitlesService.downloadSubtitle(fileId);

      if (!downloadInfo || !downloadInfo.link) {
        throw new Error('No download link received');
      }

      console.log('Player: Got download link:', downloadInfo.link);

      // Download subtitle content
      const subtitleContent = await window.openSubtitlesService.fetchSubtitleContent(downloadInfo.link);

      if (!subtitleContent) {
        throw new Error('Failed to download subtitle content');
      }

      console.log('Player: Downloaded subtitle content, length:', subtitleContent.length);

      // Parse subtitle content based on format
      if (format.toLowerCase() === 'srt') {
        // Parse SRT content
        this.parseSrtSubtitles(subtitleContent);

        // Enable subtitles
        this.enableSubtitles();

        // Update toggle button
        const toggleBtn = document.getElementById('subtitle-toggle-btn');
        if (toggleBtn) {
          toggleBtn.textContent = 'On';
          toggleBtn.dataset.subtitleState = 'on';
          toggleBtn.classList.add('active');
        }

        // Update CC button to show it's active
        if (this.ccBtn) {
          this.ccBtn.classList.add('active');
        }

        // Close subtitles menu
        const subtitlesMenu = document.getElementById('player-subtitles-menu');
        if (subtitlesMenu) {
          subtitlesMenu.classList.remove('active');
        }

        // Show success message
        const successMsg = document.createElement('div');
        successMsg.className = 'subtitle-success-message';
        successMsg.textContent = 'Subtitles enabled';
        successMsg.style.position = 'absolute';
        successMsg.style.bottom = '120px';
        successMsg.style.left = '50%';
        successMsg.style.transform = 'translateX(-50%)';
        successMsg.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        successMsg.style.color = '#fff';
        successMsg.style.padding = '10px 20px';
        successMsg.style.borderRadius = '4px';
        successMsg.style.zIndex = '100';

        this.playerWrapper.appendChild(successMsg);

        // Remove message after 3 seconds
        setTimeout(() => {
          if (successMsg.parentNode) {
            successMsg.parentNode.removeChild(successMsg);
          }
        }, 3000);

        console.log('Player: Subtitle loaded successfully');
      } else {
        throw new Error(`Unsupported subtitle format: ${format}`);
      }
    } catch (error) {
      console.error('Player: Error downloading subtitle', error);
      button.innerHTML = '<div class="subtitle-error">Download failed</div>';

      // Reset button after a delay
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalButtonContent;
      }, 2000);
    }
  }

  /**
   * Toggle subtitle display on/off
   */
  toggleSubtitleDisplay = () => {
    const toggleBtn = document.getElementById('subtitle-toggle-btn');
    if (!toggleBtn) return;

    const currentState = toggleBtn.dataset.subtitleState;

    if (currentState === 'off') {
      // Turn subtitles on
      if (this.subtitles && this.subtitles.length > 0) {
        this.enableSubtitles();
        toggleBtn.textContent = 'On';
        toggleBtn.dataset.subtitleState = 'on';
        toggleBtn.classList.add('active');

        // Update CC button to show it's active
        if (this.ccBtn) {
          this.ccBtn.classList.add('active');
        }

        // Update subtitles value in settings menu
        const subtitlesValue = document.getElementById('player-subtitles-value');
        if (subtitlesValue) {
          subtitlesValue.textContent = 'On';
        }
      } else {
        // No subtitles loaded yet
        alert('Please load a subtitle file first.');
      }
    } else {
      // Turn subtitles off
      this.disableSubtitles();
      toggleBtn.textContent = 'Off';
      toggleBtn.dataset.subtitleState = 'off';
      toggleBtn.classList.remove('active');

      // Update CC button to show it's inactive
      if (this.ccBtn) {
        this.ccBtn.classList.remove('active');
      }

      // Update subtitles value in settings menu
      const subtitlesValue = document.getElementById('player-subtitles-value');
      if (subtitlesValue) {
        subtitlesValue.textContent = 'Off';
      }
    }
  }

  /**
   * Increase subtitle font size
   */
  increaseSubtitleSize = () => {
    if (!this.subtitleFontSize) {
      this.subtitleFontSize = 100;
    }

    // Increase by 10% up to 200%
    this.subtitleFontSize = Math.min(200, this.subtitleFontSize + 10);

    // Update the display
    const sizeValue = document.getElementById('subtitle-size-value');
    if (sizeValue) {
      sizeValue.textContent = `${this.subtitleFontSize}%`;
    }

    // Apply the new size
    this.applySubtitleStyles();
  }

  /**
   * Decrease subtitle font size
   */
  decreaseSubtitleSize = () => {
    if (!this.subtitleFontSize) {
      this.subtitleFontSize = 100;
    }

    // Decrease by 10% down to 50%
    this.subtitleFontSize = Math.max(50, this.subtitleFontSize - 10);

    // Update the display
    const sizeValue = document.getElementById('subtitle-size-value');
    if (sizeValue) {
      sizeValue.textContent = `${this.subtitleFontSize}%`;
    }

    // Apply the new size
    this.applySubtitleStyles();
  }

  /**
   * Increase subtitle background opacity
   */
  increaseSubtitleOpacity = () => {
    if (!this.subtitleOpacity) {
      this.subtitleOpacity = 75;
    }

    // Increase by 5% up to 100%
    this.subtitleOpacity = Math.min(100, this.subtitleOpacity + 5);

    // Update the display
    const opacityValue = document.getElementById('subtitle-opacity-value');
    if (opacityValue) {
      opacityValue.textContent = `${this.subtitleOpacity}%`;
    }

    // Apply the new opacity
    this.applySubtitleStyles();
  }

  /**
   * Decrease subtitle background opacity
   */
  decreaseSubtitleOpacity = () => {
    if (!this.subtitleOpacity) {
      this.subtitleOpacity = 75;
    }

    // Decrease by 5% down to 0%
    this.subtitleOpacity = Math.max(0, this.subtitleOpacity - 5);

    // Update the display
    const opacityValue = document.getElementById('subtitle-opacity-value');
    if (opacityValue) {
      opacityValue.textContent = `${this.subtitleOpacity}%`;
    }

    // Apply the new opacity
    this.applySubtitleStyles();
  }

  /**
   * Apply subtitle styles based on user preferences
   */
  applySubtitleStyles = () => {
    if (!this.subtitlesContainer) return;

    // Get current values or set defaults
    const fontSize = this.subtitleFontSize || 100;
    const opacity = this.subtitleOpacity || 75;

    // Create a style element if it doesn't exist
    let styleEl = document.getElementById('subtitle-custom-styles');
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = 'subtitle-custom-styles';
      document.head.appendChild(styleEl);
    }

    // Update the styles
    styleEl.textContent = `
      .player-subtitles p {
        font-size: ${fontSize * 0.16}px !important;
        background-color: rgba(0, 0, 0, ${opacity / 100}) !important;
      }
    `;
  }

  /**
   * Handle subtitle file upload
   * @param {Event} e - Change event
   */
  handleSubtitleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    console.log('Player: Subtitle file selected:', file.name);

    // Update the title from UI first to ensure we have the correct episode info
    this.updateTitleFromUI();
    console.log('Player: Updated title before processing subtitle file:', this.currentMediaTitle);

    // Check if it's an SRT file
    if (!file.name.endsWith('.srt')) {
      console.error('Player: Invalid subtitle file format. Only .srt files are supported.');
      alert('Invalid subtitle file format. Only .srt files are supported.');
      return;
    }

    // Read the file
    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target.result;
      this.parseSrtSubtitles(content);

      // Automatically turn on subtitles after loading
      const toggleBtn = document.getElementById('subtitle-toggle-btn');
      if (toggleBtn) {
        toggleBtn.textContent = 'On';
        toggleBtn.dataset.subtitleState = 'on';
        toggleBtn.classList.add('active');
      }
    };
    reader.onerror = (error) => {
      console.error('Player: Error reading subtitle file:', error);
      alert('Error reading subtitle file.');
    };
    reader.readAsText(file);

    // Update UI
    const subtitlesValue = document.getElementById('player-subtitles-value');
    if (subtitlesValue) {
      // Show a cleaner name (without extension)
      const fileName = file.name.split('.')[0];
      // Capitalize first letter and limit length
      const displayName = fileName.charAt(0).toUpperCase() + fileName.slice(1);
      subtitlesValue.textContent = displayName.length > 15 ? displayName.substring(0, 12) + '...' : displayName;
    }

    // Update CC button to show it's active
    if (this.ccBtn) {
      this.ccBtn.classList.add('active');
    }

    // Show a success message
    const fileInput = document.getElementById('subtitle-file');
    if (fileInput) {
      const successMsg = document.createElement('div');
      successMsg.className = 'subtitle-upload-success';
      successMsg.textContent = 'Subtitle loaded successfully!';
      successMsg.style.color = 'var(--player-primary)';
      successMsg.style.textAlign = 'center';
      successMsg.style.marginTop = '8px';
      successMsg.style.fontWeight = 'bold';

      // Insert after the file input
      fileInput.parentNode.appendChild(successMsg);

      // Remove after 3 seconds
      setTimeout(() => {
        if (successMsg.parentNode) {
          successMsg.parentNode.removeChild(successMsg);
        }
      }, 3000);
    }

    // Apply default subtitle styles
    this.subtitleFontSize = 100;
    this.subtitleOpacity = 75;
    this.applySubtitleStyles();
  }

  /**
   * Parse SRT subtitles
   * @param {string} srtContent - SRT file content
   */
  parseSrtSubtitles(srtContent) {
    console.log('Player: Parsing SRT subtitles');

    // Clear existing subtitles
    this.subtitles = [];

    if (!srtContent || typeof srtContent !== 'string') {
      console.error('Player: Invalid subtitle content', srtContent);
      return;
    }

    // Log the first 200 characters to help with debugging
    console.log('Player: Subtitle content sample:', srtContent.substring(0, 200).replace(/\n/g, '\\n'));

    // Check if the content is HTML instead of an SRT file
    if (srtContent.trim().toLowerCase().startsWith('<!doctype html') ||
        srtContent.trim().toLowerCase().startsWith('<html') ||
        (srtContent.includes('<head>') && srtContent.includes('<body>'))) {
      console.error('Player: Received HTML content instead of SRT file');
      alert('The URL returned an HTML page instead of a subtitle file. This may be because:\n\n' +
            '1. The download link requires authentication\n' +
            '2. The download link has expired\n' +
            '3. The URL is for a webpage, not a direct subtitle file\n\n' +
            'Please try downloading the subtitle file to your device first, then upload it directly.');
      return;
    }

    // Try to detect and handle BOM (Byte Order Mark)
    if (srtContent.charCodeAt(0) === 0xFEFF) {
      console.log('Player: Removing BOM from subtitle content');
      srtContent = srtContent.slice(1);
    }

    // Try multiple splitting methods to handle different SRT formats
    let subtitleBlocks = [];

    // Method 1: Split by double newline (most common)
    subtitleBlocks = srtContent.trim().split(/\r?\n\r?\n/);

    // If that didn't work well, try other methods
    if (subtitleBlocks.length <= 1 && srtContent.length > 50) {
      console.log('Player: First splitting method failed, trying alternative methods');

      // Method 2: Split by number + newline pattern
      subtitleBlocks = srtContent.trim().split(/\r?\n(?=\d+\r?\n)/);

      // Method 3: If still not working, try a more aggressive approach
      if (subtitleBlocks.length <= 1) {
        const matches = srtContent.match(/\d+\r?\n\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}\r?\n[\s\S]+?(?=\r?\n\d+\r?\n|$)/g);
        if (matches && matches.length > 0) {
          subtitleBlocks = matches;
        }
      }
    }

    console.log(`Player: Found ${subtitleBlocks.length} potential subtitle blocks`);

    // Process each subtitle block
    for (const block of subtitleBlocks) {
      try {
        const lines = block.split(/\r?\n/);
        if (lines.length < 2) {
          continue;
        }

        // Find the timing line (it should contain ' --> ')
        let timeLineIndex = -1;
        for (let i = 0; i < Math.min(lines.length, 5); i++) {
          if (lines[i].includes(' --> ')) {
            timeLineIndex = i;
            break;
          }
        }

        if (timeLineIndex === -1) {
          continue;
        }

        // Parse the time line
        const timeLine = lines[timeLineIndex];

        // Support both comma and period as decimal separators
        const timeMatch = timeLine.match(/(\d{2}):(\d{2}):(\d{2})[,.](\d{3}) --> (\d{2}):(\d{2}):(\d{2})[,.](\d{3})/);

        if (!timeMatch) {
          continue;
        }

        // Calculate start and end times in seconds
        const startTime =
          parseInt(timeMatch[1]) * 3600 + // hours
          parseInt(timeMatch[2]) * 60 +   // minutes
          parseInt(timeMatch[3]) +        // seconds
          parseInt(timeMatch[4]) / 1000;  // milliseconds

        const endTime =
          parseInt(timeMatch[5]) * 3600 + // hours
          parseInt(timeMatch[6]) * 60 +   // minutes
          parseInt(timeMatch[7]) +        // seconds
          parseInt(timeMatch[8]) / 1000;  // milliseconds

        // Get the text (remaining lines after the time line)
        const text = lines.slice(timeLineIndex + 1).join('\n').trim();

        if (text) {
          // Add to subtitles array
          this.subtitles.push({
            startTime,
            endTime,
            text
          });
        }
      } catch (error) {
        console.error('Player: Error parsing subtitle block:', error, block);
      }
    }

    // If parsing failed, try using the subtitle proxy service parser as a fallback
    if (this.subtitles.length === 0 && window.subtitleProxyService && typeof window.subtitleProxyService.parseSRT === 'function') {
      console.log('Player: Primary parsing failed, trying subtitle proxy parser');
      try {
        const parsedSubtitles = window.subtitleProxyService.parseSRT(srtContent);
        if (parsedSubtitles && parsedSubtitles.length > 0) {
          this.subtitles = parsedSubtitles.map(sub => ({
            startTime: sub.startTime,
            endTime: sub.endTime,
            text: sub.text
          }));
        }
      } catch (error) {
        console.error('Player: Error using subtitle proxy parser:', error);
      }
    }

    console.log(`Player: Parsed ${this.subtitles.length} subtitles`);

    if (this.subtitles.length === 0) {
      console.error('Player: Failed to parse any subtitles from content');
      alert('Failed to parse subtitle file. The file may be in an unsupported format or corrupted.');
      return;
    }

    // Start displaying subtitles
    this.enableSubtitles();
  }

  /**
   * Enable subtitles
   */
  enableSubtitles() {
    if (!this.subtitles || !this.subtitles.length) {
      console.log('Player: No subtitles to enable');
      return;
    }

    console.log('Player: Enabling subtitles');

    // Make sure the subtitles container is visible
    if (this.subtitlesContainer) {
      this.subtitlesContainer.classList.remove('hidden');
    }

    // Start checking for subtitles to display
    this.subtitlesEnabled = true;

    // Add timeupdate event listener for subtitles if not already added
    if (this.videoElement) {
      this.videoElement.removeEventListener('timeupdate', this.updateSubtitles);
      this.videoElement.addEventListener('timeupdate', this.updateSubtitles);
    }
  }

  /**
   * Disable subtitles
   */
  disableSubtitles() {
    console.log('Player: Disabling subtitles');

    this.subtitlesEnabled = false;

    // Hide the subtitles container
    if (this.subtitlesContainer) {
      this.subtitlesContainer.classList.add('hidden');
      this.subtitlesContainer.innerHTML = '';
    }

    // Remove the timeupdate event listener for subtitles
    if (this.videoElement) {
      this.videoElement.removeEventListener('timeupdate', this.updateSubtitles);
    }

    // Update UI
    const subtitlesValue = document.getElementById('player-subtitles-value');
    if (subtitlesValue) {
      subtitlesValue.textContent = 'Off';
    }

    // Update CC button to show it's inactive
    if (this.ccBtn) {
      this.ccBtn.classList.remove('active');
    }
  }

  /**
   * Update subtitles based on current time
   */
  updateSubtitles = () => {
    if (!this.subtitlesEnabled || !this.subtitles || !this.subtitles.length || !this.videoElement || !this.subtitlesContainer) {
      return;
    }

    const currentTime = this.videoElement.currentTime;
    let subtitleText = '';

    // Find the subtitle that should be displayed at the current time
    for (const subtitle of this.subtitles) {
      if (currentTime >= subtitle.startTime && currentTime <= subtitle.endTime) {
        subtitleText = subtitle.text;
        break;
      }
    }

    // Update the subtitles container
    if (subtitleText) {
      this.subtitlesContainer.innerHTML = `<p>${subtitleText}</p>`;
    } else {
      this.subtitlesContainer.innerHTML = '';
    }
  }

  /**
   * Handle mouse movement in the player
   * @param {MouseEvent} e - Mouse event
   */
  handleMouseMove = (e) => {
    // Handle cursor visibility for non-fullscreen mode
    if (!this.isFullscreen) {
      // Always show cursor in non-fullscreen mode
      if (this.playerWrapper) {
        this.playerWrapper.classList.remove('cursor-hidden');
        this.playerWrapper.style.removeProperty('cursor');
      }
      return;
    }

    // Store current mouse position
    const currentX = e.clientX;
    const currentY = e.clientY;

    // Check if mouse has actually moved significantly
    const hasMoved = !this.lastMousePosition ||
                    Math.abs(this.lastMousePosition.x - currentX) > 5 ||
                    Math.abs(this.lastMousePosition.y - currentY) > 5;

    // Update last position
    this.lastMousePosition = { x: currentX, y: currentY };

    // Update last mouse move time
    this.lastMouseMoveTime = Date.now();

    // Reset mouse stationary tracking
    this.mouseStationaryStartTime = null;

    // Mark that mouse has moved in fullscreen
    this.mouseHasMovedSinceFullscreen = true;

    // Always show cursor when mouse moves in fullscreen mode
    if (this.playerWrapper) {
      this.playerWrapper.classList.remove('cursor-hidden');
      this.playerWrapper.style.removeProperty('cursor');

      // Make sure all child elements also show cursor
      const allElements = this.playerWrapper.querySelectorAll('*');
      allElements.forEach(el => {
        if (!el.closest('#player-controls') &&
            !el.closest('#player-settings-menu') &&
            !el.closest('#player-subtitles-menu') &&
            !el.closest('#player-logo')) {
          el.style.removeProperty('cursor');
        }
      });
    }

    // Debug
    if (hasMoved) {
      console.log('Player: Mouse moved in player area, showing controls');

      // Always show controls on significant mouse movement in fullscreen
      this.showControls();
    }

    // Always start idle detection on any mouse movement
    this.startIdleDetection();

    // Cancel any existing hide timers
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
      this.hideControlsTimeout = null;
    }

    if (this.autoHideTimer) {
      clearTimeout(this.autoHideTimer);
      this.autoHideTimer = null;
    }

    // Set a forced hide timer that will always trigger in fullscreen mode
    // This ensures controls will hide even if the mouse doesn't move
    if (!this.forcedHideTimer && this.isFullscreen) {
      this.forcedHideTimer = setTimeout(() => {
        // Only hide if we're still in fullscreen and not interacting with controls
        if (this.isFullscreen && !this.isInteractingWithControls) {
          console.log('Player: Forced hide timer triggered in fullscreen');
          this.hideControls();

          // Force cursor to be hidden
          if (this.playerWrapper) {
            this.playerWrapper.classList.add('cursor-hidden');
          }
        }
        this.forcedHideTimer = null;
      }, 3000); // Force hide after 3 seconds regardless of mouse movement
    }
  }

  /**
   * Start idle detection for mouse
   */
  startIdleDetection = () => {
    // Clear any existing idle timer
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
    }

    // Clear any existing forced hide timer
    if (this.forcedHideTimer) {
      clearTimeout(this.forcedHideTimer);
      this.forcedHideTimer = null;
    }

    console.log('Player: Starting idle detection timer');

    // Set a new idle timer with shorter timeout for more responsive hiding
    this.idleTimer = setTimeout(() => {
      console.log('Player: Idle timer triggered, fullscreen:', this.isFullscreen, 'interacting:', this.isInteractingWithControls);

      // Only hide if in fullscreen mode and not interacting with controls
      if (this.isFullscreen && !this.isInteractingWithControls) {
        console.log('Player: Hiding controls due to idle');
        this.hideControls();

        // Force cursor to be hidden in fullscreen mode
        if (this.playerWrapper) {
          this.playerWrapper.classList.add('cursor-hidden');
        }
      }
    }, 2000); // Hide after 2 seconds of inactivity

    // Set a forced hide timer that will always trigger in fullscreen mode
    // This ensures controls will hide even if the mouse doesn't move
    if (this.isFullscreen) {
      this.forcedHideTimer = setTimeout(() => {
        // Only hide if we're still in fullscreen and not interacting with controls
        if (this.isFullscreen && !this.isInteractingWithControls) {
          console.log('Player: Forced hide timer triggered in fullscreen');
          this.hideControls();

          // Force cursor to be hidden
          if (this.playerWrapper) {
            this.playerWrapper.classList.add('cursor-hidden');
          }
        }
      }, 3000); // Force hide after 3 seconds regardless of mouse movement
    }
  }

  /**
   * Handle mouse enter on controls
   */
  handleControlsMouseEnter = () => {
    // Set flag to indicate user is interacting with controls
    this.isInteractingWithControls = true;

    // Show controls
    this.showControls();

    // Clear any auto-hide timer
    if (this.autoHideTimer) {
      clearTimeout(this.autoHideTimer);
      this.autoHideTimer = null;
    }

    console.log('Player: Mouse entered controls');
  }

  /**
   * Handle mouse leave on controls
   */
  handleControlsMouseLeave = () => {
    // Reset the flag after a short delay
    clearTimeout(this.interactionTimeout);
    this.interactionTimeout = setTimeout(() => {
      this.isInteractingWithControls = false;

      // Reset the auto-hide timer if in fullscreen mode
      if (this.isFullscreen) {
        this.resetAutoHideTimer();
      }
    }, 500);

    console.log('Player: Mouse left controls');
  }

  /**
   * Handle player click
   * @param {MouseEvent} e - Mouse event
   */
  handlePlayerClick(e) {
    // Ignore clicks on controls
    if (e.target.closest('#player-controls') || e.target.closest('#player-settings-menu') || e.target.closest('#player-subtitles-menu')) {
      // Set flag to indicate user is interacting with controls
      this.isInteractingWithControls = true;

      // Reset the flag after a short delay
      clearTimeout(this.interactionTimeout);
      this.interactionTimeout = setTimeout(() => {
        this.isInteractingWithControls = false;
      }, 1000);

      return;
    }

    // Toggle play/pause on video area click
    this.togglePlayPause();
  }

  /**
   * Show controls
   */
  showControls() {
    if (!this.playerControls) {
      // Try to refresh elements if controls not found
      this.refreshElements();
      if (!this.playerControls) {
        console.error('Player: Controls not found even after refresh');
        return;
      }
    }

    // Check if we're showing an iframe
    const isIframeVisible = this.playerIframe &&
                           this.playerIframe.style.display === 'block' &&
                           this.playerIframe.src &&
                           this.playerIframe.src !== 'about:blank';

    // Don't show controls for iframe content
    if (isIframeVisible) {
      console.log('Player: Not showing controls for iframe content');
      return;
    }

    // Force display and visibility with !important to override any CSS
    this.playerControls.classList.add('active');
    this.playerControls.style.setProperty('opacity', '1', 'important');
    this.playerControls.style.setProperty('visibility', 'visible', 'important');
    this.playerControls.style.setProperty('display', 'flex', 'important');

    // Always show cursor when controls are visible
    if (this.playerWrapper) {
      this.playerWrapper.classList.remove('cursor-hidden');
      this.playerWrapper.style.removeProperty('cursor');
    }

    // Make sure all child elements are visible
    const allElements = this.playerControls.querySelectorAll('*');
    allElements.forEach(el => {
      el.style.setProperty('opacity', '1', 'important');
      el.style.setProperty('visibility', 'visible', 'important');
    });

    // Show logo when controls are visible
    const playerLogo = document.getElementById('player-logo');
    if (playerLogo) {
      playerLogo.style.setProperty('opacity', '1', 'important');
      playerLogo.style.setProperty('visibility', 'visible', 'important');
      playerLogo.style.setProperty('display', 'block', 'important');
    }

    // Show cursor in fullscreen mode
    if (this.isFullscreen && this.playerWrapper) {
      this.playerWrapper.classList.remove('cursor-hidden');
    }

    // Clear any existing hide timeout
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
      this.hideControlsTimeout = null;
    }

    // Schedule hiding controls if playing or in fullscreen, but with a longer delay
    if ((this.isPlaying || this.isFullscreen) && !this._initialControlsShown) {
      // For the first time showing controls, use a longer delay
      this._initialControlsShown = true;
      setTimeout(() => {
        this.scheduleHideControls();
      }, 2000);
    } else if (this.isPlaying || this.isFullscreen) {
      this.scheduleHideControls();
    }

    console.log('Player: Controls shown');
  }

  /**
   * Hide controls
   */
  hideControls() {
    if (!this.playerControls) return;

    // Don't hide if settings menu is open
    if (this.settingsMenu && this.settingsMenu.classList.contains('active')) {
      return;
    }

    // Don't hide if subtitles menu is open
    const subtitlesMenu = document.getElementById('player-subtitles-menu');
    if (subtitlesMenu && subtitlesMenu.classList.contains('active')) {
      return;
    }

    // Don't hide if user is interacting with controls
    if (this.isInteractingWithControls) {
      return;
    }

    // Check if we're showing an iframe
    const isIframeVisible = this.playerIframe &&
                           this.playerIframe.style.display === 'block' &&
                           this.playerIframe.src &&
                           this.playerIframe.src !== 'about:blank';

    if (isIframeVisible) {
      // For iframe content, completely hide controls
      this.playerControls.style.display = 'none';
      console.log('Player: Completely hiding controls for iframe content');
    } else {
      // For regular content, just make them transparent
      this.playerControls.classList.remove('active');

      // Use !important to override any CSS that might be keeping controls visible
      this.playerControls.style.setProperty('opacity', '0', 'important');
      this.playerControls.style.setProperty('visibility', 'hidden', 'important');

      console.log('Player: Hiding controls (making transparent)');

      // Also hide logo in fullscreen mode
      if (this.isFullscreen) {
        const playerLogo = document.getElementById('player-logo');
        if (playerLogo) {
          playerLogo.style.setProperty('opacity', '0', 'important');
          playerLogo.style.setProperty('visibility', 'hidden', 'important');
          playerLogo.style.setProperty('display', 'none', 'important');
        }

        // Hide cursor in fullscreen mode with !important to ensure it's hidden
        if (this.playerWrapper) {
          this.playerWrapper.classList.add('cursor-hidden');

          // Apply inline style as well for extra assurance
          this.playerWrapper.style.setProperty('cursor', 'none', 'important');
        }

        // Clear any pending hide timers since we've already hidden the controls
        if (this.hideControlsTimeout) {
          clearTimeout(this.hideControlsTimeout);
          this.hideControlsTimeout = null;
        }

        if (this.idleTimer) {
          clearTimeout(this.idleTimer);
          this.idleTimer = null;
        }

        if (this.forcedHideTimer) {
          clearTimeout(this.forcedHideTimer);
          this.forcedHideTimer = null;
        }
      }
    }
  }

  /**
   * Schedule hiding controls
   */
  scheduleHideControls() {
    // Clear any existing timeout
    if (this.hideControlsTimeout) {
      clearTimeout(this.hideControlsTimeout);
    }

    // Set new timeout with a longer delay (5 seconds instead of 3)
    this.hideControlsTimeout = setTimeout(() => {
      // Don't hide controls if settings or subtitles menu is open
      const settingsMenuOpen = this.settingsMenu && this.settingsMenu.classList.contains('active');
      const subtitlesMenu = document.getElementById('player-subtitles-menu');
      const subtitlesMenuOpen = subtitlesMenu && subtitlesMenu.classList.contains('active');

      if (!settingsMenuOpen && !subtitlesMenuOpen && !this.isInteractingWithControls) {
        this.hideControls();
      } else {
        // If menus are open, reschedule hiding for when they close
        console.log('Player: Not hiding controls because menus are open');
      }
    }, 5000); // Hide after 5 seconds (increased from 3)
  }

  /**
   * Add event listener for subtitle URL button
   */
  addSubtitleUrlEventListener() {
    const loadBtn = document.getElementById('load-subtitle-url-btn');
    if (loadBtn) {
      loadBtn.removeEventListener('click', this.handleSubtitleUrlLoad);
      loadBtn.addEventListener('click', this.handleSubtitleUrlLoad);
      console.log('Player: Added subtitle URL button event listener');
    }

    // Also add event listener for Enter key in the URL input
    const urlInput = document.getElementById('subtitle-url');
    if (urlInput) {
      urlInput.removeEventListener('keydown', this.handleSubtitleUrlKeydown);
      urlInput.addEventListener('keydown', this.handleSubtitleUrlKeydown);
    }
  }

  /**
   * Add event listener for Addic7ed search button
   */
  addAddic7edSearchEventListener() {
    const searchBtn = document.getElementById('search-addic7ed-btn');
    if (searchBtn) {
      searchBtn.removeEventListener('click', this.searchAddic7edSubtitles);
      searchBtn.addEventListener('click', this.searchAddic7edSubtitles);
      console.log('Player: Added Addic7ed search button event listener');
    }
  }

  /**
   * Ensure Addic7ed service is loaded
   */
  ensureAddic7edServiceLoaded() {
    console.log('Player: Ensuring Addic7ed service is loaded');

    if (!window.addic7edService) {
      console.log('Player: Addic7ed service not found, attempting to load it');

      // Create a script element
      const script = document.createElement('script');
      script.src = '/js/addic7ed-service.js';
      script.async = true;

      // Add an onload handler
      script.onload = () => {
        console.log('Player: Addic7ed service loaded successfully');
      };

      // Add an error handler
      script.onerror = (error) => {
        console.error('Player: Error loading Addic7ed service', error);
      };

      // Append the script to the document
      document.head.appendChild(script);
    } else {
      console.log('Player: Addic7ed service already loaded');
    }
  }

  /**
   * Ensure Addic7ed service is loaded
   */
  ensureAddic7edServiceLoaded() {
    console.log('Player: Ensuring Addic7ed service is loaded');

    if (!window.addic7edService) {
      console.log('Player: Addic7ed service not found, attempting to load it');

      // Create a script element
      const script = document.createElement('script');
      script.src = '/js/addic7ed-service.js';
      script.async = true;

      // Add an onload handler
      script.onload = () => {
        console.log('Player: Addic7ed service loaded successfully');
      };

      // Add an error handler
      script.onerror = (error) => {
        console.error('Player: Error loading Addic7ed service', error);
      };

      // Append the script to the document
      document.head.appendChild(script);
    } else {
      console.log('Player: Addic7ed service already loaded');
    }
  }

  /**
   * Get language code for a language name
   * @param {string} languageName - Full language name
   * @returns {string} Language code (2 characters)
   */
  getLanguageCode(languageName) {
    const languageCodes = {
      'english': 'EN',
      'french': 'FR',
      'spanish': 'ES',
      'german': 'DE',
      'italian': 'IT',
      'portuguese': 'PT',
      'portuguese (brazilian)': 'PT-BR',
      'russian': 'RU',
      'arabic': 'AR',
      'dutch': 'NL',
      'polish': 'PL',
      'turkish': 'TR',
      'chinese': 'ZH',
      'chinese (traditional)': 'ZH-TW',
      'chinese (simplified)': 'ZH-CN',
      'japanese': 'JA',
      'korean': 'KO',
      'swedish': 'SV',
      'danish': 'DA',
      'finnish': 'FI',
      'norwegian': 'NO',
      'czech': 'CS',
      'greek': 'EL',
      'hungarian': 'HU',
      'romanian': 'RO',
      'thai': 'TH',
      'ukrainian': 'UK',
      'vietnamese': 'VI',
      'hebrew': 'HE',
      'hindi': 'HI'
    };

    const normalizedLanguage = languageName.toLowerCase();
    return languageCodes[normalizedLanguage] || languageName.substring(0, 2).toUpperCase();
  }

  /**
   * Search for subtitles on Addic7ed.com
   */
  searchAddic7edSubtitles = async () => {
    console.log('Player: Starting Addic7ed subtitle search');

    // DEBUG: Log subtitle search start
    if (window.netStreamDebug) {
      window.netStreamDebug.log('Subtitle Search (Start)', {
        currentMediaTitle: this.currentMediaTitle || 'Unknown',
        playerTitle: this.playerTitle ? this.playerTitle.textContent : 'Unknown',
        timestamp: new Date().toISOString()
      });
    }

    // First, update the title from UI if needed
    const titleUpdated = this.updateTitleFromUI();

    // DEBUG: Log title update result
    if (window.netStreamDebug) {
      window.netStreamDebug.log('Subtitle Search (After Title Update)', {
        titleWasUpdated: titleUpdated,
        currentMediaTitle: this.currentMediaTitle || 'Unknown',
        playerTitle: this.playerTitle ? this.playerTitle.textContent : 'Unknown',
        timestamp: new Date().toISOString()
      });
    }

    // Check if Addic7ed service is available
    console.log('Player: Checking for Addic7ed service availability');
    console.log('Player: window.addic7edService =', window.addic7edService);

    if (!window.addic7edService) {
      console.error('Player: Addic7ed service is not available');

      // Try to load the service dynamically
      console.log('Player: Attempting to load Addic7ed service dynamically');

      try {
        // Create a script element
        const script = document.createElement('script');
        script.src = '/js/addic7ed-service.js';
        script.async = true;

        // Add an onload handler
        script.onload = () => {
          console.log('Player: Addic7ed service loaded dynamically');
          if (window.addic7edService) {
            console.log('Player: Addic7ed service is now available, retrying search');
            this.searchAddic7edSubtitles();
          } else {
            console.error('Player: Addic7ed service still not available after dynamic loading');
            alert('Addic7ed service could not be loaded. Please refresh the page and try again.');
          }
        };

        // Add an error handler
        script.onerror = (error) => {
          console.error('Player: Error loading Addic7ed service dynamically', error);
          alert('Error loading Addic7ed service. Please check the console for details.');
        };

        // Append the script to the document
        document.head.appendChild(script);

        // Show loading message
        const onlineSubtitlesContainer = document.getElementById('online-subtitles-container');
        if (onlineSubtitlesContainer) {
          onlineSubtitlesContainer.innerHTML = `
            <div class="subtitle-loading">Loading Addic7ed service...</div>
          `;
        }

        return;
      } catch (error) {
        console.error('Player: Error attempting to load Addic7ed service dynamically', error);
        alert('Addic7ed service is not available and could not be loaded dynamically.');
        return;
      }
    }

    // Get the current media title
    if (!this.currentMediaTitle) {
      alert('No media title available. Please play a video first.');
      return;
    }

    console.log('Player: Searching Addic7ed for subtitles for:', this.currentMediaTitle);

    // Show loading state in the subtitles container
    const onlineSubtitlesContainer = document.getElementById('online-subtitles-container');
    if (!onlineSubtitlesContainer) {
      console.error('Player: Online subtitles container not found');
      return;
    }

    onlineSubtitlesContainer.innerHTML = `
      <div class="subtitle-loading">Searching for subtitles on Addic7ed.com...</div>
    `;

    try {
      // Get the current media title
      if (!this.currentMediaTitle) {
        // Try to get the title from the document title
        const docTitle = document.title;
        if (docTitle && docTitle !== 'NetStream') {
          this.currentMediaTitle = docTitle;
          console.log('Player: Using document title as media title:', this.currentMediaTitle);
        } else {
          // Try to get the title from the URL
          const urlParams = new URLSearchParams(window.location.search);
          const mediaId = urlParams.get('id');

          if (mediaId) {
            // Try to extract show/season/episode from the ID
            const idMatch = mediaId.match(/^(.*?)_s(\d+)e(\d+)$/i);
            if (idMatch) {
              const show = idMatch[1].replace(/_/g, ' ');
              const season = parseInt(idMatch[2], 10);
              const episode = parseInt(idMatch[3], 10);

              this.currentMediaTitle = `${show} S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}`;
              console.log('Player: Created media title from ID:', this.currentMediaTitle);
            }
          }
        }

        if (!this.currentMediaTitle) {
          throw new Error('No media title available. Please play a video first.');
        }
      }

      // IMPORTANT FIX: Check if we're in the media page and get the current episode from the UI
      try {
        // Find the selected episode in the media page
        const episodeItem = document.querySelector('#episodes .grid-item[style*="border: 1px solid #00bcd4"]');
        if (episodeItem && episodeItem.dataset.ep) {
          const epNum = episodeItem.dataset.ep;
          const seasonSelect = document.getElementById('season-select');
          const currentSeason = seasonSelect ? seasonSelect.value : '1';

          // Get the show name from the current media title
          let showName = this.currentMediaTitle;

          // Extract show name from various formats
          const formats = [
            /^Series:\s+(.*?)\s+-\s+S\d+:E\d+/i,  // Series: Show Name - S01:E01
            /^(.*?)\s+S\d+E\d+/i,                // Show Name S01E01
            /^(.*?)\s+-\s+\d+x\d+/i,             // Show Name - 1x01
            /^(.*?)\s+-\s+Season\s+\d+\s+Episode\s+\d+/i // Show Name - Season 1 Episode 1
          ];

          for (const regex of formats) {
            const match = this.currentMediaTitle.match(regex);
            if (match && match[1]) {
              showName = match[1].trim();
              break;
            }
          }

          // Create a new title with the correct episode number
          const newTitle = `Series: ${showName} - S${currentSeason}:E${epNum}`;
          console.log('Player: Updating title for subtitle search from UI:', newTitle);

          // Update the current media title
          this.currentMediaTitle = newTitle;
        }
      } catch (error) {
        console.warn('Player: Error getting episode from UI:', error);
        // Continue with the current title if there's an error
      }

      console.log('Player: Current media title for subtitle search:', this.currentMediaTitle);

      // Parse the media title to extract show, season, and episode
      let parsedTitle = window.addic7edService.parseMediaTitle(this.currentMediaTitle);

      if (!parsedTitle) {
        // If we couldn't parse the title, ask the user for input
        const userInput = prompt(
          'Could not automatically detect show information. Please enter in format "Show Name / Season / Episode":\n' +
          'Example: "The Last of Us / 2 / 5"'
        );

        if (userInput) {
          const parts = userInput.split('/').map(part => part.trim());

          if (parts.length >= 3) {
            const show = parts[0];
            const season = parseInt(parts[1], 10);
            const episode = parseInt(parts[2], 10);

            if (show && !isNaN(season) && !isNaN(episode)) {
              parsedTitle = { show, season, episode };
              console.log('Player: Using user-provided show info:', parsedTitle);
            }
          }
        }

        if (!parsedTitle) {
          throw new Error('Could not parse show, season, and episode from the title');
        }
      }

      console.log('Player: Parsed media title:', parsedTitle);

      // Search for subtitles with fallback to Gemini AI
      console.log('Player: Searching for subtitles with fallback to Gemini AI');
      const subtitles = await window.addic7edService.searchSubtitlesWithFallback(
        parsedTitle.show,
        parsedTitle.season,
        parsedTitle.episode
      );

      if (!subtitles || subtitles.length === 0) {
        onlineSubtitlesContainer.innerHTML = `
          <div class="subtitle-error">
            <p>No subtitles found on Addic7ed.com for this title.</p>
            <p>You can try a different title or upload your own subtitle file.</p>
          </div>
        `;
        return;
      }

      // Group subtitles by language
      const subtitlesByLanguage = {};
      subtitles.forEach(subtitle => {
        const language = subtitle.language;
        if (!subtitlesByLanguage[language]) {
          subtitlesByLanguage[language] = [];
        }
        subtitlesByLanguage[language].push(subtitle);
      });

      // Create HTML for subtitle options
      let html = '<div class="subtitle-language-groups">';

      // Sort languages alphabetically
      const languages = Object.keys(subtitlesByLanguage).sort();

      for (const language of languages) {
        const languageSubtitles = subtitlesByLanguage[language];

        // Get language code
        const languageCode = this.getLanguageCode(language);

        html += `
          <div class="subtitle-language-group">
            <div class="subtitle-language-header">
              <span class="subtitle-language-code">${languageCode}</span>
              <span class="subtitle-language-name">${language}</span>
            </div>
            <div class="subtitle-language-options">
        `;

        // Sort subtitles by version
        languageSubtitles.sort((a, b) => a.version.localeCompare(b.version));

        for (const subtitle of languageSubtitles) {
          const hearingImpairedIcon = subtitle.hearing_impaired ?
            '<img src="/images/hi.png" alt="Hearing Impaired" title="Hearing Impaired" class="subtitle-hi-icon" />' : '';

          // Extract season and episode from show_title if available
          let seasonEpisodeInfo = '';
          if (subtitle.show_title) {
            // Try different patterns to extract season and episode
            let match = subtitle.show_title.match(/(\d+)x(\d+)/i);

            if (!match) {
              match = subtitle.show_title.match(/S(\d+)[:\s-]*E(\d+)/i);
            }

            if (!match) {
              // The Last of Us format: "The Last of Us - 02x05 - Feel Her Love"
              match = subtitle.show_title.match(/\s+-\s+(\d+)x(\d+)\s+-/i);
            }

            if (!match) {
              // Generic format with numbers: "Show Name - 2 5 - Episode Name"
              match = subtitle.show_title.match(/\s+-\s+(\d+)\s+(\d+)\s+-/i);
            }

            if (match) {
              const season = match[1].padStart(2, '0');
              const episode = match[2].padStart(2, '0');
              seasonEpisodeInfo = `S${season}:E${episode}`;
            }
          }

          // Get language code
          const languageCode = this.getLanguageCode(language);

          html += `
            <button class="online-subtitle-option" data-url="${subtitle.download}" title="${subtitle.version}">
              <div class="subtitle-option-name">
                <span class="subtitle-language-code">${languageCode}</span>
                ${seasonEpisodeInfo ? `<span class="subtitle-season-episode">${seasonEpisodeInfo}</span>` : ''}
                ${hearingImpairedIcon}
              </div>
              <div class="subtitle-option-source">Addic7ed</div>
            </button>
          `;
        }

        html += `
            </div>
          </div>
        `;
      }

      html += '</div>';

      // Update the container
      onlineSubtitlesContainer.innerHTML = html;

      // Add event listeners to subtitle options
      const subtitleOptions = onlineSubtitlesContainer.querySelectorAll('.online-subtitle-option');
      subtitleOptions.forEach(option => {
        option.addEventListener('click', this.handleAddic7edSubtitleSelect);
      });
    } catch (error) {
      console.error('Player: Error searching Addic7ed subtitles', error);
      onlineSubtitlesContainer.innerHTML = `
        <div class="subtitle-error">
          <p>Error searching for subtitles: ${error.message}</p>
          <p>You can try a different title or upload your own subtitle file.</p>
        </div>
      `;
    }
  }

  /**
   * Handle Addic7ed subtitle selection
   * @param {Event} e - Click event
   */
  handleAddic7edSubtitleSelect = async (e) => {
    const button = e.currentTarget;
    const url = button.dataset.url;

    if (!url) {
      console.error('Player: No URL for Addic7ed subtitle download');
      return;
    }

    console.log('Player: Downloading Addic7ed subtitle:', url);

    // Show loading state
    button.disabled = true;
    const originalButtonContent = button.innerHTML;
    button.innerHTML = '<div class="subtitle-loading">Downloading...</div>';

    try {
      // Download the subtitle content
      const content = await window.addic7edService.downloadSubtitle(url);

      if (!content) {
        throw new Error('Failed to download subtitle content');
      }

      console.log('Player: Downloaded Addic7ed subtitle content, length:', content.length);

      // Parse the subtitle content
      this.parseSrtSubtitles(content);

      // Enable subtitles
      this.enableSubtitles();

      // Update toggle button
      const toggleBtn = document.getElementById('subtitle-toggle-btn');
      if (toggleBtn) {
        toggleBtn.textContent = 'On';
        toggleBtn.dataset.subtitleState = 'on';
        toggleBtn.classList.add('active');
      }

      // Update CC button to show it's active
      if (this.ccBtn) {
        this.ccBtn.classList.add('active');
      }

      // Close subtitles menu
      const subtitlesMenu = document.getElementById('player-subtitles-menu');
      if (subtitlesMenu) {
        subtitlesMenu.classList.remove('active');
      }

      // Show success message
      const successMsg = document.createElement('div');
      successMsg.className = 'subtitle-success-message';
      successMsg.textContent = 'Subtitles loaded successfully';
      successMsg.style.position = 'absolute';
      successMsg.style.bottom = '120px';
      successMsg.style.left = '50%';
      successMsg.style.transform = 'translateX(-50%)';
      successMsg.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      successMsg.style.color = '#fff';
      successMsg.style.padding = '10px 20px';
      successMsg.style.borderRadius = '4px';
      successMsg.style.zIndex = '100';

      this.playerWrapper.appendChild(successMsg);

      // Remove message after 3 seconds
      setTimeout(() => {
        if (successMsg.parentNode) {
          successMsg.parentNode.removeChild(successMsg);
        }
      }, 3000);

      console.log('Player: Addic7ed subtitle loaded successfully');
    } catch (error) {
      console.error('Player: Error downloading Addic7ed subtitle', error);
      button.innerHTML = '<div class="subtitle-error">Download failed</div>';

      // Reset button after a delay
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalButtonContent;
      }, 2000);

      alert(`Error downloading subtitle: ${error.message}`);
    }
  }

  /**
   * Handle keydown event in subtitle URL input
   * @param {KeyboardEvent} e - Keydown event
   */
  handleSubtitleUrlKeydown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      this.handleSubtitleUrlLoad();
    }
  }

  /**
   * Handle subtitle URL load button click
   */
  handleSubtitleUrlLoad = async () => {
    const urlInput = document.getElementById('subtitle-url');
    if (!urlInput) return;

    // Update the title from UI first to ensure we have the correct episode info
    this.updateTitleFromUI();
    console.log('Player: Updated title before loading subtitle URL:', this.currentMediaTitle);

    let url = urlInput.value.trim();
    if (!url) {
      alert('Please enter a valid SRT URL');
      return;
    }

    // Try to fix common URL issues
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
      console.log('Player: Added https:// to URL:', url);
    }

    // Check if URL is valid
    try {
      new URL(url);
    } catch (e) {
      alert('Please enter a valid URL (e.g., https://example.com/subtitle.srt)');
      return;
    }

    // Check if URL ends with common subtitle extensions
    const subtitleExtensions = ['.srt', '.vtt', '.sub', '.sbv', '.ass', '.ssa'];
    const hasSubtitleExtension = subtitleExtensions.some(ext => url.toLowerCase().endsWith(ext));

    if (!hasSubtitleExtension) {
      // Special case for OpenSubtitles download URLs which don't end with .srt
      const isOpenSubtitlesUrl = url.includes('opensubtitles.com/download/') ||
                                url.includes('opensubtitles.org/download/');

      if (isOpenSubtitlesUrl) {
        // Show a warning about OpenSubtitles URLs
        const useOpenSubtitles = confirm(
          'OpenSubtitles download links often require authentication or expire quickly.\n\n' +
          'If this fails, please try:\n' +
          '1. Downloading the subtitle file to your device first\n' +
          '2. Then uploading it directly using the "Load SRT File" option\n\n' +
          'Do you want to try using this URL anyway?'
        );

        if (!useOpenSubtitles) return;
      } else {
        const confirmLoad = confirm('The URL does not have a recognized subtitle extension (.srt, .vtt, etc.). Are you sure this is a subtitle file?');
        if (!confirmLoad) return;
      }
    }

    // Disable the button and show loading state
    const loadBtn = document.getElementById('load-subtitle-url-btn');
    if (loadBtn) {
      loadBtn.disabled = true;
      loadBtn.textContent = 'Loading...';
    }

    // Show loading message in the subtitle menu
    const subtitleInfo = document.createElement('div');
    subtitleInfo.className = 'subtitle-loading';
    subtitleInfo.textContent = 'Downloading subtitle file...';

    const subtitlesMenu = document.getElementById('player-subtitles-menu');
    if (subtitlesMenu) {
      const infoContainer = subtitlesMenu.querySelector('.subtitle-info');
      if (infoContainer) {
        infoContainer.appendChild(subtitleInfo);
      }
    }

    try {
      // Use the subtitle proxy service to fetch the subtitle file
      let content;

      if (window.subtitleProxyService) {
        console.log('Player: Using subtitle proxy service to fetch subtitle');
        content = await window.subtitleProxyService.downloadSubtitle(url);
      } else {
        // Fallback to direct fetch if proxy service is not available
        console.log('Player: Subtitle proxy service not available, using direct fetch');
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`Failed to fetch subtitle file: ${response.status} ${response.statusText}`);
        }

        content = await response.text();
      }

      // Check if content is valid
      if (!content || typeof content !== 'string' || content.length < 10) {
        throw new Error('Downloaded content is empty or invalid');
      }

      // Parse the subtitle content
      this.parseSrtSubtitles(content);

      // Enable subtitles
      this.enableSubtitles();

      // Update toggle button
      const toggleBtn = document.getElementById('subtitle-toggle-btn');
      if (toggleBtn) {
        toggleBtn.textContent = 'On';
        toggleBtn.dataset.subtitleState = 'on';
        toggleBtn.classList.add('active');
      }

      // Update CC button to show it's active
      if (this.ccBtn) {
        this.ccBtn.classList.add('active');
      }

      // Show success message
      const successMsg = document.createElement('div');
      successMsg.className = 'subtitle-success-message';
      successMsg.textContent = 'Subtitles loaded successfully';
      successMsg.style.position = 'absolute';
      successMsg.style.bottom = '120px';
      successMsg.style.left = '50%';
      successMsg.style.transform = 'translateX(-50%)';
      successMsg.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      successMsg.style.color = '#fff';
      successMsg.style.padding = '10px 20px';
      successMsg.style.borderRadius = '4px';
      successMsg.style.zIndex = '100';

      this.playerWrapper.appendChild(successMsg);

      // Remove message after 3 seconds
      setTimeout(() => {
        if (successMsg.parentNode) {
          successMsg.parentNode.removeChild(successMsg);
        }
      }, 3000);

      // Close subtitles menu
      const subtitlesMenu = document.getElementById('player-subtitles-menu');
      if (subtitlesMenu) {
        subtitlesMenu.classList.remove('active');
      }

      console.log('Player: Subtitle loaded successfully from URL');
    } catch (error) {
      console.error('Player: Error loading subtitle from URL', error);

      // Remove loading message if it exists
      if (subtitleInfo && subtitleInfo.parentNode) {
        subtitleInfo.parentNode.removeChild(subtitleInfo);
      }

      // Show a more detailed error message
      let errorMessage = 'Failed to load subtitle file.';

      // Check if this is an OpenSubtitles URL
      const isOpenSubtitlesUrl = url.includes('opensubtitles.com/download/') ||
                                url.includes('opensubtitles.org/download/');

      if (isOpenSubtitlesUrl) {
        errorMessage = 'OpenSubtitles download link failed. These links often require authentication or expire quickly.\n\n' +
                      'Please try:\n' +
                      '1. Downloading the subtitle file to your device first\n' +
                      '2. Then uploading it directly using the "Load SRT File" option';
      } else if (error.message.includes('CORS')) {
        errorMessage = 'CORS error: The subtitle server does not allow cross-origin requests. Try using a different URL or downloading the file and uploading it directly.';
      } else if (error.message.includes('404')) {
        errorMessage = 'The subtitle file was not found (404 error). Please check the URL and try again.';
      } else if (error.message.includes('403')) {
        errorMessage = 'Access to the subtitle file was denied (403 error). The server may require authentication or not allow direct access.';
      } else if (error.message.includes('timeout') || error.message.includes('network')) {
        errorMessage = 'Network error: Could not connect to the subtitle server. Please check your internet connection and try again.';
      } else if (error.message.includes('empty') || error.message.includes('invalid')) {
        errorMessage = 'The downloaded file does not appear to be a valid subtitle file. Please try a different URL.';
      } else if (error.message.includes('HTML')) {
        errorMessage = 'The URL returned an HTML page instead of a subtitle file. This may be because:\n\n' +
                      '1. The download link requires authentication\n' +
                      '2. The download link has expired\n' +
                      '3. The URL is for a webpage, not a direct subtitle file\n\n' +
                      'Please try downloading the subtitle file to your device first, then upload it directly.';
      } else {
        errorMessage = `Error loading subtitle: ${error.message}`;
      }

      alert(errorMessage);

      // Show error in the subtitle menu
      const errorDiv = document.createElement('div');
      errorDiv.className = 'subtitle-error';
      errorDiv.textContent = 'Error: ' + errorMessage;

      const subtitlesMenu = document.getElementById('player-subtitles-menu');
      if (subtitlesMenu) {
        const infoContainer = subtitlesMenu.querySelector('.subtitle-info');
        if (infoContainer) {
          infoContainer.appendChild(errorDiv);

          // Remove error message after 10 seconds
          setTimeout(() => {
            if (errorDiv.parentNode) {
              errorDiv.parentNode.removeChild(errorDiv);
            }
          }, 10000);
        }
      }
    } finally {
      // Re-enable the button
      if (loadBtn) {
        loadBtn.disabled = false;
        loadBtn.textContent = 'Load';
      }
    }
  }

  /**
   * Reset auto-hide timer for fullscreen mode - DEPRECATED, use startIdleDetection instead
   */
  resetAutoHideTimer() {
    // This method is kept for backward compatibility
    // Use startIdleDetection instead
    this.startIdleDetection();
  }

  /**
   * Hide player
   */
  hidePlayer() {
    if (!this.playerContainer) return;

    // Pause video if playing
    if (this.videoElement && !this.videoElement.paused) {
      try {
        this.videoElement.pause();
      } catch (error) {
        console.error('Player: Error pausing video:', error);
      }
    }

    // Clear iframe source if it exists
    if (this.playerIframe) {
      this.playerIframe.src = '';
      this.playerIframe.style.display = 'none';
    }

    // Show controls again (they might have been hidden for iframe content)
    if (this.playerControls) {
      this.playerControls.style.display = 'flex';
    }

    // Hide player container
    this.playerContainer.classList.add('hidden');

    console.log('Player: Player hidden');
  }

  /**
   * Set player title
   * @param {string} title - Title to display
   */
  setTitle(title) {
    if (!this.playerTitle) {
      this.refreshElements();
    }

    // DEBUG: Log title before update
    if (window.netStreamDebug) {
      window.netStreamDebug.log('Player.setTitle (Before)', {
        oldPlayerTitle: this.playerTitle ? this.playerTitle.textContent : 'Unknown',
        oldMediaTitle: this.currentMediaTitle || 'Unknown',
        newTitle: title,
        timestamp: new Date().toISOString(),
        callStack: new Error().stack
      });
    }

    if (this.playerTitle) {
      this.playerTitle.textContent = title;
      this.currentMediaTitle = title; // Store title for subtitle search
      console.log('Player: Title set to:', title);

      // Update document title as well
      document.title = title;

      // Store the title in localStorage for debugging
      localStorage.setItem('lastPlayerTitle', title);

      // DEBUG: Log title after update
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Player.setTitle (After)', {
          playerTitle: this.playerTitle.textContent,
          currentMediaTitle: this.currentMediaTitle,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  /**
   * Update player title from currentMediaTitle
   * This ensures the title matches the currently selected episode
   */
  updateTitleFromUI() {
    console.log('Player: Checking if title needs to be updated');

    try {
      // Only use the currentMediaTitle to extract episode information
      if (!this.currentMediaTitle) {
        console.log('Player: No currentMediaTitle available');

        // DEBUG: Log failure
        if (window.netStreamDebug) {
          window.netStreamDebug.log('Update Title - No Title', {
            timestamp: new Date().toISOString()
          });
        }

        return false;
      }

      // Try to extract episode and season from the current title
      const episodeMatch = this.currentMediaTitle.match(/S(\d+)[:\s-]*E(\d+)/i);
      if (!episodeMatch) {
        console.log('Player: Could not extract episode info from title:', this.currentMediaTitle);

        // DEBUG: Log failure
        if (window.netStreamDebug) {
          window.netStreamDebug.log('Update Title - No Episode Info in Title', {
            currentMediaTitle: this.currentMediaTitle,
            timestamp: new Date().toISOString()
          });
        }

        return false;
      }

      const currentSeason = episodeMatch[1];
      const epNum = episodeMatch[2];

      console.log(`Player: Extracted episode info from title: S${currentSeason}:E${epNum}`);

      // DEBUG: Log extraction from title
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Update Title - Extracted From Title', {
          currentMediaTitle: this.currentMediaTitle,
          extractedSeason: currentSeason,
          extractedEpisode: epNum,
          timestamp: new Date().toISOString()
        });
      }

      // Check if the current title already contains this episode info
      const episodePattern = new RegExp(`S${currentSeason}[:\\s-]*E${epNum}\\b`, 'i');
      if (this.currentMediaTitle && episodePattern.test(this.currentMediaTitle)) {
        console.log('Player: Current title already contains correct episode info');

        // DEBUG: Log no change needed
        if (window.netStreamDebug) {
          window.netStreamDebug.log('Update Title - No Change Needed', {
            currentMediaTitle: this.currentMediaTitle,
            episodePattern: episodePattern.toString(),
            timestamp: new Date().toISOString()
          });
        }

        return false;
      }

      // Get the show name from the current media title or the page
      let showName = '';

      // Try to get show name from current title
      if (this.currentMediaTitle) {
        // Extract show name from various formats
        const formats = [
          /^Series:\s+(.*?)\s+-\s+S\d+:E\d+/i,  // Series: Show Name - S01:E01
          /^(.*?)\s+S\d+E\d+/i,                // Show Name S01E01
          /^(.*?)\s+-\s+\d+x\d+/i,             // Show Name - 1x01
          /^(.*?)\s+-\s+Season\s+\d+\s+Episode\s+\d+/i // Show Name - Season 1 Episode 1
        ];

        for (const regex of formats) {
          const match = this.currentMediaTitle.match(regex);
          if (match && match[1]) {
            showName = match[1].trim();
            break;
          }
        }
      }

      // If we couldn't extract the show name, try to get it from the page
      if (!showName) {
        const titleElement = document.querySelector('.media-title h1');
        if (titleElement) {
          showName = titleElement.textContent.trim();
        }
      }

      // If we still don't have a show name, use a default
      if (!showName) {
        showName = 'Current Media';
      }

      // DEBUG: Log show name extraction
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Update Title - Show Name', {
          extractedShowName: showName,
          fromCurrentTitle: this.currentMediaTitle || 'None',
          timestamp: new Date().toISOString()
        });
      }

      // Create a new title with the correct episode number
      const newTitle = `Series: ${showName} - S${currentSeason}:E${epNum}`;
      console.log('Player: Updating title from UI:', newTitle);

      // DEBUG: Log before title update
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Update Title - Before Update', {
          oldTitle: this.currentMediaTitle || 'Unknown',
          newTitle: newTitle,
          timestamp: new Date().toISOString()
        });
      }

      // Update the title
      this.setTitle(newTitle);

      // DEBUG: Log after title update
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Update Title - After Update', {
          currentMediaTitle: this.currentMediaTitle || 'Unknown',
          timestamp: new Date().toISOString()
        });
      }

      return true;
    } catch (error) {
      console.warn('Player: Error updating title from UI:', error);

      // DEBUG: Log error
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Update Title - Error', {
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      }

      return false;
    }
  }

  /**
   * Set HLS instance
   * @param {object} hls - HLS.js instance
   */
  setHlsInstance(hls) {
    this.hlsInstance = hls;
    console.log('Player: HLS instance set');

    // Add event listeners for HLS quality levels
    if (hls) {
      try {
        // Set Auto as default quality
        hls.startLevel = -1; // Start with auto level
        hls.currentLevel = -1;

        // Check if autoLevelEnabled is a writable property
        // In newer HLS.js versions, it's a getter-only property
        try {
          const descriptor = Object.getOwnPropertyDescriptor(hls, 'autoLevelEnabled');
          if (!descriptor || descriptor.writable || descriptor.set) {
            hls.autoLevelEnabled = true;
            console.log('Player: Set autoLevelEnabled to true');
          } else {
            console.log('Player: autoLevelEnabled is a read-only property, using currentLevel = -1 instead');
          }
        } catch (autoLevelError) {
          console.warn('Player: Could not set autoLevelEnabled:', autoLevelError);
        }

        console.log('Player: Set initial quality to Auto');

        // Listen for manifest parsed event to get quality levels
        hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
          console.log('Player: HLS manifest parsed, levels:', data.levels.length);

          // Ensure Auto is selected
          setTimeout(() => {
            try {
              hls.currentLevel = -1;

              // Check if autoLevelEnabled is a writable property
              try {
                const descriptor = Object.getOwnPropertyDescriptor(hls, 'autoLevelEnabled');
                if (!descriptor || descriptor.writable || descriptor.set) {
                  hls.autoLevelEnabled = true;
                }
              } catch (autoLevelError) {
                // Ignore error, currentLevel = -1 is enough
              }

              console.log('Player: Forced Auto quality after manifest parsed');
            } catch (error) {
              console.error('Player: Error setting Auto quality after manifest parsed:', error);
            }
          }, 100);

          // Update quality value in settings menu
          const qualityValue = document.getElementById('player-quality-value');
          if (qualityValue) {
            qualityValue.textContent = 'Auto';

            if (data.levels.length > 1) {
              console.log('Player: Multiple quality levels available:', data.levels.length);

              // Sort levels by resolution (highest to lowest)
              const sortedLevels = [...data.levels].sort((a, b) => {
                if (!a.height && !b.height) return 0;
                if (!a.height) return 1;
                if (!b.height) return -1;
                return b.height - a.height;
              });

              // Log available qualities for debugging
              sortedLevels.forEach((level, index) => {
                const height = level.height || 'unknown';
                const width = level.width || 'unknown';
                const bitrate = level.bitrate ? Math.round(level.bitrate / 1000) + ' kbps' : 'unknown bitrate';
                console.log(`Player: Quality level ${index}: ${height}p (${width}x${height}, ${bitrate})`);
              });
            }
          }
        });

        // Listen for level loading event
        hls.on(Hls.Events.LEVEL_LOADING, (event, data) => {
          const levelIndex = data.level;
          console.log('Player: HLS level loading:', levelIndex);
        });

        // Listen for level loaded event
        hls.on(Hls.Events.LEVEL_LOADED, (event, data) => {
          const levelIndex = data.level;
          console.log('Player: HLS level loaded:', levelIndex);
        });

        // Listen for level switched event
        hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
          const levelIndex = data.level;
          console.log('Player: HLS level switched to', levelIndex);

          // Update quality value in settings menu
          const qualityValue = document.getElementById('player-quality-value');
          if (qualityValue) {
            // Add changing class for visual feedback
            qualityValue.classList.add('changing');

            // Update the text content
            if (levelIndex === -1) {
              qualityValue.textContent = 'Auto';
              console.log('Player: Quality display updated to Auto');
            } else if (hls.levels && hls.levels[levelIndex]) {
              const level = hls.levels[levelIndex];
              const qualityLabel = level.height ? `${level.height}p` :
                                 (level.bitrate ? `${Math.round(level.bitrate / 1000)} kbps` : `Level ${levelIndex + 1}`);
              qualityValue.textContent = qualityLabel;
              console.log('Player: Quality display updated to', qualityLabel);

              // Show quality change indicator
              this.showQualityChangeIndicator(qualityLabel);
            }

            // Remove changing class after animation
            setTimeout(() => {
              qualityValue.classList.remove('changing');
            }, 1000);
          }
        });

        // Listen for error events
        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('Player: HLS error:', data.type, data.details, data);

          if (data.fatal) {
            switch(data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.error('Player: Fatal network error, trying to recover');
                hls.startLoad(); // Try to recover
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.error('Player: Fatal media error, trying to recover');
                hls.recoverMediaError(); // Try to recover
                break;
              default:
                console.error('Player: Fatal error, cannot recover');
                break;
            }
          }
        });
      } catch (error) {
        console.error('Player: Error setting up HLS instance:', error);
      }
    }
  }

  /**
   * Get HLS instance
   * @returns {object} HLS.js instance
   */
  getHlsInstance() {
    return this.hlsInstance;
  }

  /**
   * Load iframe source
   * @param {string} url - URL to load in iframe
   */
  loadIframeSource(url) {
    console.log('Player: Loading iframe source:', url);

    // Refresh elements to ensure we have the latest references
    this.refreshElements();

    // Ensure player structure exists
    this.ensurePlayerStructure();

    if (!this.playerIframe) {
      console.error('Player: Iframe element not found');
      return false;
    }

    // Hide video element and show iframe
    if (this.videoElement) {
      this.videoElement.style.display = 'none';

      // Pause video if it's playing
      if (!this.videoElement.paused) {
        try {
          this.videoElement.pause();
        } catch (error) {
          console.error('Player: Error pausing video:', error);
        }
      }
    }

    // Style and show iframe
    this.playerIframe.style.display = 'block';
    this.playerIframe.style.width = '100%';
    this.playerIframe.style.height = '100%';
    this.playerIframe.style.border = 'none';
    this.playerIframe.style.position = 'absolute';
    this.playerIframe.style.top = '0';
    this.playerIframe.style.left = '0';
    this.playerIframe.style.zIndex = '10'; // Higher z-index to ensure it's on top
    this.playerIframe.setAttribute('allowfullscreen', '');

    // Clear any existing src first
    this.playerIframe.src = 'about:blank';

    // Force a reflow
    void this.playerIframe.offsetWidth;

    // Set the new src
    this.playerIframe.src = url;

    // Hide controls for iframe content
    if (this.playerControls) {
      this.playerControls.style.display = 'none';
    }

    // Handle logo visibility for iframe content
    const playerLogo = document.getElementById('player-logo');
    if (playerLogo) {
      // Check if we're in fullscreen mode
      const isFullscreen = !!document.fullscreenElement ||
                         !!document.webkitFullscreenElement ||
                         !!document.mozFullScreenElement ||
                         !!document.msFullscreenElement;

      if (isFullscreen) {
        // Hide logo in fullscreen for iframe content
        playerLogo.style.display = 'none';
        console.log('Player: Hiding logo for iframe content in fullscreen');
      }
    }

    // Add a debug message to check if iframe is visible
    console.log('Player: Iframe element styled:', {
      display: this.playerIframe.style.display,
      width: this.playerIframe.style.width,
      height: this.playerIframe.style.height,
      zIndex: this.playerIframe.style.zIndex,
      src: this.playerIframe.src
    });

    console.log('Player: Iframe source loaded successfully');
    return true;
  }
}

/**
 * Get the player instance (singleton)
 * @returns {VideoPlayer} Player instance
 */
function getPlayerInstance() {
  if (!playerInstance) {
    playerInstance = new VideoPlayer();
    console.log('Player: Created new player instance');
  }
  return playerInstance;
}

// Create the global modernPlayer object
window.modernPlayer = {
  // Properties
  isConnecting: false,
  onVideoElementConnected: null,

  // Methods
  initialize: function() {
    return getPlayerInstance().initialize();
  },

  connectVideoElement: function() {
    return getPlayerInstance().connectVideoElement();
  },

  setTitle: function(title) {
    getPlayerInstance().setTitle(title);
  },

  getCurrentMediaTitle: function() {
    return getPlayerInstance().currentMediaTitle || '';
  },

  setHlsInstance: function(hls) {
    getPlayerInstance().setHlsInstance(hls);
  },

  getHlsInstance: function() {
    return getPlayerInstance().getHlsInstance();
  },

  togglePlayPause: function() {
    getPlayerInstance().togglePlayPause();
  },

  skipForward: function() {
    getPlayerInstance().skipForward();
  },

  skipBackward: function() {
    getPlayerInstance().skipBackward();
  },

  toggleSubtitles: function() {
    getPlayerInstance().toggleSubtitles();
  },

  enableSubtitles: function() {
    getPlayerInstance().enableSubtitles();
  },

  disableSubtitles: function() {
    getPlayerInstance().disableSubtitles();
  },

  loadSubtitles: function(srtContent) {
    getPlayerInstance().parseSrtSubtitles(srtContent);
  },

  cycleQuality: function() {
    getPlayerInstance().cycleQuality();
  },

  loadIframeSource: function(url) {
    return getPlayerInstance().loadIframeSource(url);
  },

  hidePlayer: function() {
    getPlayerInstance().hidePlayer();
  },

  refreshElements: function() {
    getPlayerInstance().refreshElements();
  }
};

// Initialize player when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Player: DOM content loaded, initializing player');
  window.modernPlayer.initialize();
});