// File: public/js/player.js
// Modern Video Player Implementation

// Global player instance to prevent multiple initializations
let playerInstance = null;

// Initialize immediately and also on DOMContentLoaded
function initializePlayer() {
  console.log('Player.js script loaded');

  // If player is already initialized, don't initialize again
  if (playerInstance) {
    console.log('Player.js: Player already initialized, skipping initialization');
    return playerInstance;
  }

  // Player elements
  const playerContainer = document.getElementById('player-container');
  let playerWrapper = document.getElementById('player-wrapper');
  let videoPlayer = document.getElementById('player');
  let playerIframe = document.getElementById('player-iframe');
  let playerControls = document.getElementById('player-controls');
  let playerTitle = document.getElementById('player-title');
  let closePlayerBtn = document.getElementById('close-player');

  // Function to refresh element references
  function refreshElements() {
    playerWrapper = document.getElementById('player-wrapper');
    videoPlayer = document.getElementById('player');
    playerIframe = document.getElementById('player-iframe');
    playerControls = document.getElementById('player-controls');
    playerTitle = document.getElementById('player-title');
    closePlayerBtn = document.getElementById('close-player');

    console.log('Player.js: Refreshed element references', {
      playerWrapper: !!playerWrapper,
      videoPlayer: !!videoPlayer,
      playerControls: !!playerControls
    });

    // If we don't have a player wrapper but we have a player container, create the wrapper
    if (!playerWrapper && playerContainer) {
      console.log('Player.js: Creating player wrapper during refresh');

      // Create the wrapper
      playerWrapper = document.createElement('div');
      playerWrapper.id = 'player-wrapper';

      // Create the logo
      const playerLogo = document.createElement('div');
      playerLogo.id = 'player-logo';
      playerLogo.textContent = 'NetStream';

      // Create the controls
      playerControls = document.createElement('div');
      playerControls.id = 'player-controls';
      playerControls.innerHTML = `
        <div id="player-title-bar">
          <div id="player-title">Now Playing</div>
        </div>

        <div id="player-progress-container">
          <div id="player-progress-buffer"></div>
          <div id="player-progress-bar"></div>
          <div id="player-time-tooltip">00:00</div>
        </div>

        <div id="player-buttons">
          <div class="player-button-group">
            <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
              <i class="fas fa-play"></i>
            </button>

            <div id="player-volume-container">
              <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
                <i class="fas fa-volume-up"></i>
              </button>
              <div id="player-volume-slider">
                <div id="player-volume-level"></div>
              </div>
            </div>

            <div id="player-time-display">
              <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
            </div>
          </div>

          <div class="player-button-group">
            <button id="player-settings" class="player-button" aria-label="Settings">
              <i class="fas fa-cog"></i>
            </button>
            <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
              <i class="fas fa-expand"></i>
            </button>
          </div>
        </div>
      `;

      // Create the settings menu
      const settingsMenu = document.createElement('div');
      settingsMenu.id = 'player-settings-menu';
      settingsMenu.innerHTML = `
        <div class="player-settings-item" data-setting="quality">
          <span>Quality</span>
          <span id="player-quality-value">Auto</span>
        </div>
        <div class="player-settings-item" data-setting="speed">
          <span>Speed</span>
          <span id="player-speed-value">Normal</span>
        </div>
      `;

      // Get the close button
      closePlayerBtn = document.getElementById('close-player');

      // Get the video and iframe elements
      videoPlayer = document.getElementById('player');
      playerIframe = document.getElementById('player-iframe');

      // Add elements to the wrapper
      playerWrapper.appendChild(playerLogo);

      // Re-add the video and iframe to the wrapper if they exist
      if (videoPlayer && videoPlayer.parentNode) {
        videoPlayer.parentNode.removeChild(videoPlayer);
        playerWrapper.appendChild(videoPlayer);
      }

      if (playerIframe && playerIframe.parentNode) {
        playerIframe.parentNode.removeChild(playerIframe);
        playerWrapper.appendChild(playerIframe);
      }

      playerWrapper.appendChild(playerControls);
      playerWrapper.appendChild(settingsMenu);

      // Add the wrapper to the container before the close button
      if (closePlayerBtn) {
        playerContainer.insertBefore(playerWrapper, closePlayerBtn);
      } else {
        playerContainer.appendChild(playerWrapper);
      }

      console.log('Player.js: Player wrapper created during refresh');

      // Update references after creating elements
      playerTitle = document.getElementById('player-title');
    }
  }

  // Refresh elements in case they were created by direct-player-fix.js
  refreshElements();

  // Control elements
  const playPauseBtn = document.getElementById('player-play-pause');
  const playPauseIcon = playPauseBtn?.querySelector('i');
  const volumeToggleBtn = document.getElementById('player-volume-toggle');
  const volumeIcon = volumeToggleBtn?.querySelector('i');
  const volumeSlider = document.getElementById('player-volume-slider');
  const volumeLevel = document.getElementById('player-volume-level');
  const fullscreenBtn = document.getElementById('player-fullscreen');
  const fullscreenIcon = fullscreenBtn?.querySelector('i');
  const settingsBtn = document.getElementById('player-settings');
  const settingsMenu = document.getElementById('player-settings-menu');
  const currentTimeDisplay = document.getElementById('player-current-time');
  const durationDisplay = document.getElementById('player-duration');
  const progressContainer = document.getElementById('player-progress-container');
  const progressBar = document.getElementById('player-progress-bar');
  const progressBuffer = document.getElementById('player-progress-buffer');
  const timeTooltip = document.getElementById('player-time-tooltip');
  const qualityValue = document.getElementById('player-quality-value');
  const speedValue = document.getElementById('player-speed-value');

  // State variables
  let isPlaying = false;
  let isMuted = false;
  let isFullscreen = false;
  let hideControlsTimeout;
  let currentMediaTitle = '';
  let hlsInstance = null;
  let availableQualities = [];
  let currentQuality = 'auto';
  let currentSpeed = 1;
  let lastVolume = 1;
  let isDraggingProgress = false;
  let isDraggingVolume = false;
  let controlsVisible = true;

  // Check if all required elements exist
  if (!playerContainer || !videoPlayer) {
    console.error('Player.js: Required player elements not found');
    return;
  }

  // Initialize player
  function initPlayer() {
    // Set initial volume
    if (videoPlayer) {
      videoPlayer.volume = localStorage.getItem('playerVolume') ?
        parseFloat(localStorage.getItem('playerVolume')) : 1;
      updateVolumeUI();
    }

    // Add event listeners
    addEventListeners();

    console.log('Player.js: Player initialized');
  }

  // Add event listeners
  function addEventListeners() {
    console.log('Player.js: Adding event listeners');

    // Video player events
    if (videoPlayer) {
      // Remove existing listeners to avoid duplicates
      videoPlayer.removeEventListener('play', handlePlay);
      videoPlayer.removeEventListener('pause', handlePause);
      videoPlayer.removeEventListener('timeupdate', handleTimeUpdate);
      videoPlayer.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoPlayer.removeEventListener('progress', handleProgress);
      videoPlayer.removeEventListener('ended', handleEnded);
      videoPlayer.removeEventListener('volumechange', updateVolumeUI);

      // Add listeners
      videoPlayer.addEventListener('play', handlePlay);
      videoPlayer.addEventListener('pause', handlePause);
      videoPlayer.addEventListener('timeupdate', handleTimeUpdate);
      videoPlayer.addEventListener('loadedmetadata', handleLoadedMetadata);
      videoPlayer.addEventListener('progress', handleProgress);
      videoPlayer.addEventListener('ended', handleEnded);
      videoPlayer.addEventListener('volumechange', updateVolumeUI);

      console.log('Player.js: Added video element event listeners');
    }

    // Get fresh references to control elements
    const playPauseBtn = document.getElementById('player-play-pause');
    const volumeToggleBtn = document.getElementById('player-volume-toggle');
    const fullscreenBtn = document.getElementById('player-fullscreen');
    const settingsBtn = document.getElementById('player-settings');
    const progressContainer = document.getElementById('player-progress-container');
    const volumeSlider = document.getElementById('player-volume-slider');
    const settingsMenu = document.getElementById('player-settings-menu');
    const closePlayerBtn = document.getElementById('close-player');

    console.log('Player.js: Control elements found:', {
      playPauseBtn: !!playPauseBtn,
      volumeToggleBtn: !!volumeToggleBtn,
      fullscreenBtn: !!fullscreenBtn,
      progressContainer: !!progressContainer
    });

    // Control buttons - with proper error handling
    if (playPauseBtn) {
      // Remove any existing event listeners to avoid duplicates
      playPauseBtn.removeEventListener('click', togglePlayPause);
      // Add the event listener with a direct function reference
      const playPauseHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('Player.js: Play/Pause button clicked');
        // Refresh elements before toggling
        refreshElements();
        // Toggle play/pause with a small delay to ensure UI is updated
        setTimeout(togglePlayPause, 10);
      };
      playPauseBtn.addEventListener('click', playPauseHandler);
      console.log('Player.js: Added play/pause button event listener');
    }

    if (volumeToggleBtn) {
      volumeToggleBtn.removeEventListener('click', toggleMute);
      volumeToggleBtn.addEventListener('click', toggleMute);
      console.log('Player.js: Added volume toggle button event listener');
    }

    if (fullscreenBtn) {
      // Remove any existing event listeners to avoid duplicates
      fullscreenBtn.removeEventListener('click', toggleFullscreen);
      // Add the event listener with a direct function reference
      const fullscreenHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('Player.js: Fullscreen button clicked');
        // Refresh elements before toggling
        refreshElements();
        // Toggle fullscreen with a small delay to ensure UI is updated
        setTimeout(toggleFullscreen, 10);
      };
      fullscreenBtn.addEventListener('click', fullscreenHandler);
      console.log('Player.js: Added fullscreen button event listener');
    }

    if (settingsBtn) {
      settingsBtn.removeEventListener('click', toggleSettings);
      settingsBtn.addEventListener('click', toggleSettings);
      console.log('Player.js: Added settings button event listener');
    }

    if (closePlayerBtn) {
      closePlayerBtn.removeEventListener('click', hidePlayer);
      closePlayerBtn.addEventListener('click', hidePlayer);
      console.log('Player.js: Added close button event listener');
    }

    // Progress bar
    if (progressContainer) {
      // Remove existing listeners to avoid duplicates
      progressContainer.removeEventListener('mousedown', startProgressDrag);
      progressContainer.removeEventListener('mousemove', showProgressTooltip);
      progressContainer.removeEventListener('mouseleave', hideProgressTooltip);
      progressContainer.removeEventListener('click', seekToPosition);

      // Add listeners
      progressContainer.addEventListener('mousedown', startProgressDrag);
      progressContainer.addEventListener('mousemove', showProgressTooltip);
      progressContainer.addEventListener('mouseleave', hideProgressTooltip);
      progressContainer.addEventListener('click', seekToPosition);

      console.log('Player.js: Added progress bar event listeners');
    }

    // Volume slider
    if (volumeSlider) {
      // Remove existing listeners to avoid duplicates
      volumeSlider.removeEventListener('mousedown', startVolumeDrag);
      volumeSlider.removeEventListener('click', setVolume);

      // Add listeners
      volumeSlider.addEventListener('mousedown', startVolumeDrag);
      volumeSlider.addEventListener('click', setVolume);

      console.log('Player.js: Added volume slider event listeners');
    }

    // Settings menu items
    if (settingsMenu) {
      const settingsItems = settingsMenu.querySelectorAll('.player-settings-item');
      settingsItems.forEach(item => {
        item.removeEventListener('click', handleSettingChange);
        item.addEventListener('click', handleSettingChange);
      });

      console.log('Player.js: Added settings menu event listeners');
    }

    // Document events for drag handling
    // Remove existing listeners to avoid duplicates
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);

    // Add listeners
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', stopDrag);

    console.log('Player.js: Added document-level event listeners');

    // Player container click to toggle controls
    if (playerWrapper) {
      playerWrapper.addEventListener('click', handlePlayerClick);
      playerWrapper.addEventListener('mousemove', showControls);
      playerWrapper.addEventListener('mouseleave', () => {
        if (isPlaying) scheduleHideControls();
      });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyDown);
  }

  // Play/Pause handlers
  function handlePlay() {
    // Refresh element references to ensure we have the latest UI elements
    refreshElements();

    isPlaying = true;

    // Update play/pause icon
    const playPauseBtn = document.getElementById('player-play-pause');
    const playPauseIcon = playPauseBtn?.querySelector('i');
    if (playPauseIcon) {
      playPauseIcon.className = 'fas fa-pause';
      console.log('Player.js: Updated play icon to pause');
    }

    // First show controls, then schedule hiding them
    showControls();
    scheduleHideControls();

    console.log('Player.js: Video playback started');
  }

  function handlePause() {
    // Refresh element references to ensure we have the latest UI elements
    refreshElements();

    isPlaying = false;

    // Update play/pause icon
    const playPauseBtn = document.getElementById('player-play-pause');
    const playPauseIcon = playPauseBtn?.querySelector('i');
    if (playPauseIcon) {
      playPauseIcon.className = 'fas fa-play';
      console.log('Player.js: Updated pause icon to play');
    }

    showControls();
  }

  function togglePlayPause() {
    // Refresh element references to ensure we have the latest video element
    refreshElements();

    if (!videoPlayer) {
      console.error('Player.js: Video element not found for play/pause');
      return;
    }

    // Get fresh reference to play/pause button and icon
    const playPauseBtn = document.getElementById('player-play-pause');
    const playPauseIcon = playPauseBtn?.querySelector('i');

    console.log('Player.js: Toggle play/pause, current state:', videoPlayer.paused ? 'paused' : 'playing');

    try {
      if (videoPlayer.paused) {
        // Update UI immediately to give feedback
        if (playPauseIcon) {
          playPauseIcon.className = 'fas fa-pause';
          console.log('Player.js: Updated play icon to pause');
        }

        // Try to play
        console.log('Player.js: Attempting to play video');
        videoPlayer.play()
          .then(() => {
            console.log('Player.js: Play successful');
            isPlaying = true;
            // Schedule hiding controls
            scheduleHideControls();
          })
          .catch(err => {
            console.error('Player.js: Play error:', err);
            // Revert UI if play fails
            if (playPauseIcon) playPauseIcon.className = 'fas fa-play';
            isPlaying = false;
          });
      } else {
        // Pause the video
        console.log('Player.js: Pausing video');
        videoPlayer.pause();
        isPlaying = false;
        if (playPauseIcon) {
          playPauseIcon.className = 'fas fa-play';
          console.log('Player.js: Updated pause icon to play');
        }
        // Keep controls visible when paused
        showControls();
      }
    } catch (error) {
      console.error('Player.js: Error in togglePlayPause:', error);
    }
  }

  // Volume handlers
  function toggleMute() {
    if (!videoPlayer) return;

    if (videoPlayer.volume === 0 || videoPlayer.muted) {
      videoPlayer.muted = false;
      videoPlayer.volume = lastVolume;
    } else {
      lastVolume = videoPlayer.volume;
      videoPlayer.volume = 0;
    }

    updateVolumeUI();
  }

  function updateVolumeUI() {
    if (!videoPlayer || !volumeIcon || !volumeLevel) return;

    const volume = videoPlayer.muted ? 0 : videoPlayer.volume;

    // Update volume icon
    if (volume === 0) {
      volumeIcon.className = 'fas fa-volume-mute';
    } else if (volume < 0.5) {
      volumeIcon.className = 'fas fa-volume-down';
    } else {
      volumeIcon.className = 'fas fa-volume-up';
    }

    // Update volume slider
    volumeLevel.style.width = `${volume * 100}%`;

    // Save volume to localStorage
    localStorage.setItem('playerVolume', volume);
  }

  function startVolumeDrag(e) {
    e.preventDefault();
    isDraggingVolume = true;
    setVolume(e);
  }

  function setVolume(e) {
    if (!videoPlayer || !volumeSlider) return;

    const rect = volumeSlider.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    const volume = Math.max(0, Math.min(1, pos));

    videoPlayer.muted = volume === 0;
    videoPlayer.volume = volume;
    updateVolumeUI();
  }

  // Progress bar handlers
  function handleTimeUpdate() {
    if (!videoPlayer || !progressBar || !currentTimeDisplay || !durationDisplay) return;

    const currentTime = videoPlayer.currentTime;
    const duration = videoPlayer.duration || 0;

    // Validate the values
    if (!isFinite(currentTime) || !isFinite(duration) || duration <= 0) {
      // Don't update UI with invalid values
      return;
    }

    // Update progress bar
    if (!isDraggingProgress) {
      const progress = Math.max(0, Math.min(100, (currentTime / duration) * 100));
      progressBar.style.width = `${progress}%`;
    }

    // Update time display
    currentTimeDisplay.textContent = formatTime(currentTime);
    durationDisplay.textContent = formatTime(duration);
  }

  function handleProgress() {
    if (!videoPlayer || !progressBuffer) return;

    const duration = videoPlayer.duration;

    // Validate the duration
    if (!isFinite(duration) || duration <= 0) {
      return;
    }

    try {
      if (videoPlayer.buffered && videoPlayer.buffered.length > 0) {
        for (let i = 0; i < videoPlayer.buffered.length; i++) {
          if (videoPlayer.buffered.start(i) <= videoPlayer.currentTime &&
              videoPlayer.buffered.end(i) >= videoPlayer.currentTime) {
            const bufferedEnd = videoPlayer.buffered.end(i);
            const bufferedPercent = Math.max(0, Math.min(100, (bufferedEnd / duration) * 100));
            progressBuffer.style.width = `${bufferedPercent}%`;
            break;
          }
        }
      }
    } catch (error) {
      console.error('Player.js: Error updating buffer progress:', error);
    }
  }

  function startProgressDrag(e) {
    e.preventDefault();
    isDraggingProgress = true;
    seekToPosition(e);
  }

  function seekToPosition(e) {
    if (!videoPlayer || !progressContainer) return;

    // Check if video is ready and has a valid duration
    if (!videoPlayer.duration || !isFinite(videoPlayer.duration)) {
      console.warn('Player.js: Cannot seek, video duration is not available or not finite');
      return;
    }

    const rect = progressContainer.getBoundingClientRect();
    const pos = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const seekTime = pos * videoPlayer.duration;

    // Validate the seek time before setting it
    if (isFinite(seekTime) && seekTime >= 0 && seekTime <= videoPlayer.duration) {
      try {
        videoPlayer.currentTime = seekTime;
      } catch (error) {
        console.error('Player.js: Error setting currentTime:', error);
      }
    } else {
      console.warn('Player.js: Invalid seek time calculated:', seekTime);
    }
  }

  function showProgressTooltip(e) {
    if (!videoPlayer || !progressContainer || !timeTooltip) return;

    // Check if video is ready and has a valid duration
    if (!videoPlayer.duration || !isFinite(videoPlayer.duration)) {
      return;
    }

    const rect = progressContainer.getBoundingClientRect();
    const pos = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const tooltipTime = pos * videoPlayer.duration;

    // Only update tooltip if time is valid
    if (isFinite(tooltipTime) && tooltipTime >= 0) {
      timeTooltip.textContent = formatTime(tooltipTime);
      timeTooltip.style.left = `${e.clientX - rect.left}px`;
      timeTooltip.style.display = 'block';
    }
  }

  function hideProgressTooltip() {
    if (timeTooltip) timeTooltip.style.display = 'none';
  }

  // Drag handling
  function handleDrag(e) {
    if (isDraggingProgress) {
      seekToPosition(e);
    } else if (isDraggingVolume) {
      setVolume(e);
    }
  }

  function stopDrag() {
    isDraggingProgress = false;
    isDraggingVolume = false;
  }

  // Fullscreen handlers
  function toggleFullscreen() {
    // Refresh element references
    refreshElements();

    if (!playerWrapper) {
      console.error('Player.js: Player wrapper not found for fullscreen toggle');
      return;
    }

    console.log('Player.js: Toggling fullscreen, current state:', isFullscreen ? 'fullscreen' : 'normal');

    try {
      if (!isFullscreen) {
        // Save original styles to restore later
        const originalStyles = {
          width: playerWrapper.style.width,
          height: playerWrapper.style.height,
          maxWidth: playerWrapper.style.maxWidth,
          maxHeight: playerWrapper.style.maxHeight,
          borderRadius: playerWrapper.style.borderRadius
        };

        // Store original styles in a data attribute
        playerWrapper.setAttribute('data-original-styles', JSON.stringify(originalStyles));

        // Apply fullscreen styles before entering fullscreen
        playerWrapper.style.width = '100%';
        playerWrapper.style.height = '100%';
        playerWrapper.style.maxWidth = '100%';
        playerWrapper.style.maxHeight = '100%';
        playerWrapper.style.borderRadius = '0';

        // Request fullscreen
        if (playerWrapper.requestFullscreen) {
          playerWrapper.requestFullscreen();
        } else if (playerWrapper.webkitRequestFullscreen) {
          playerWrapper.webkitRequestFullscreen();
        } else if (playerWrapper.msRequestFullscreen) {
          playerWrapper.msRequestFullscreen();
        } else {
          // Fallback for browsers that don't support fullscreen API
          console.log('Player.js: Fullscreen API not supported, using CSS fullscreen');
          playerContainer.classList.add('fullscreen-fallback');
          isFullscreen = true;
          updateFullscreenUI();
        }
      } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        } else {
          // Fallback for browsers that don't support fullscreen API
          console.log('Player.js: Fullscreen API not supported, using CSS fullscreen exit');
          playerContainer.classList.remove('fullscreen-fallback');
          isFullscreen = false;
          updateFullscreenUI();
        }
      }
    } catch (error) {
      console.error('Player.js: Error toggling fullscreen:', error);
    }
  }

  // Listen for fullscreen change
  document.addEventListener('fullscreenchange', updateFullscreenUI);
  document.addEventListener('webkitfullscreenchange', updateFullscreenUI);
  document.addEventListener('mozfullscreenchange', updateFullscreenUI);
  document.addEventListener('MSFullscreenChange', updateFullscreenUI);

  function updateFullscreenUI() {
    // Check if we're in fullscreen mode
    const wasFullscreen = isFullscreen;
    isFullscreen = !!document.fullscreenElement ||
                   !!document.webkitFullscreenElement ||
                   !!document.mozFullScreenElement ||
                   !!document.msFullscreenElement ||
                   playerContainer.classList.contains('fullscreen-fallback');

    console.log('Player.js: Fullscreen state changed to:', isFullscreen ? 'fullscreen' : 'normal');

    // Update the fullscreen icon
    if (fullscreenIcon) {
      fullscreenIcon.className = isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
    }

    // Refresh element references
    refreshElements();

    if (playerWrapper) {
      if (isFullscreen) {
        // Apply fullscreen styles
        playerWrapper.style.width = '100%';
        playerWrapper.style.height = '100%';
        playerWrapper.style.maxWidth = '100%';
        playerWrapper.style.maxHeight = '100%';
        playerWrapper.style.borderRadius = '0';

        // Make sure the video fills the screen
        if (videoPlayer) {
          videoPlayer.style.width = '100%';
          videoPlayer.style.height = '100%';
          videoPlayer.style.maxWidth = '100%';
          videoPlayer.style.maxHeight = '100%';
        }
      } else if (wasFullscreen) {
        // Restore original styles when exiting fullscreen
        try {
          const originalStyles = JSON.parse(playerWrapper.getAttribute('data-original-styles') || '{}');

          // Apply original styles
          playerWrapper.style.width = originalStyles.width || '90%';
          playerWrapper.style.height = originalStyles.height || '90%';
          playerWrapper.style.maxWidth = originalStyles.maxWidth || '1280px';
          playerWrapper.style.maxHeight = originalStyles.maxHeight || '720px';
          playerWrapper.style.borderRadius = originalStyles.borderRadius || '8px';

          // Reset video element styles
          if (videoPlayer) {
            videoPlayer.style.width = '100%';
            videoPlayer.style.height = '100%';
            videoPlayer.style.maxWidth = '';
            videoPlayer.style.maxHeight = '';
          }
        } catch (error) {
          console.error('Player.js: Error restoring original styles:', error);

          // Apply default styles as fallback
          playerWrapper.style.width = '90%';
          playerWrapper.style.height = '90%';
          playerWrapper.style.maxWidth = '1280px';
          playerWrapper.style.maxHeight = '720px';
          playerWrapper.style.borderRadius = '8px';
        }
      }
    }
  }

  // Settings handlers
  function toggleSettings() {
    if (!settingsMenu) {
      // Try to find the settings menu
      settingsMenu = document.getElementById('player-settings-menu');
      if (!settingsMenu) return;
    }

    // Refresh element references
    refreshElements();

    // Toggle the active class
    settingsMenu.classList.toggle('active');

    // If the menu is now active, position it properly
    if (settingsMenu.classList.contains('active')) {
      // Make sure the menu is visible and properly styled
      settingsMenu.style.position = 'absolute';
      settingsMenu.style.bottom = '70px';
      settingsMenu.style.right = '20px';
      settingsMenu.style.background = 'rgba(28, 28, 28, 0.9)';
      settingsMenu.style.backdropFilter = 'blur(10px)';
      settingsMenu.style.webkitBackdropFilter = 'blur(10px)';
      settingsMenu.style.borderRadius = '8px';
      settingsMenu.style.padding = '12px';
      settingsMenu.style.display = 'flex';
      settingsMenu.style.flexDirection = 'column';
      settingsMenu.style.gap = '10px';
      settingsMenu.style.minWidth = '200px';
      settingsMenu.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
      settingsMenu.style.zIndex = '3';
      settingsMenu.style.border = '1px solid rgba(255, 255, 255, 0.1)';
      settingsMenu.style.opacity = '1';
      settingsMenu.style.visibility = 'visible';

      // Style the settings items
      const settingsItems = settingsMenu.querySelectorAll('.player-settings-item');
      settingsItems.forEach(item => {
        item.style.display = 'flex';
        item.style.justifyContent = 'space-between';
        item.style.alignItems = 'center';
        item.style.color = '#ffffff';
        item.style.padding = '10px 12px';
        item.style.borderRadius = '4px';
        item.style.cursor = 'pointer';
        item.style.fontFamily = 'Roboto, sans-serif';
        item.style.fontSize = '0.95em';
        item.style.margin = '0';
        item.style.width = '100%';
        item.style.textAlign = 'left';

        // Style the spans inside the settings items
        const spans = item.querySelectorAll('span');
        spans.forEach(span => {
          span.style.display = 'inline-block';
          span.style.margin = '0';
          span.style.padding = '0';
        });

        // Style the value span
        const valueSpan = item.querySelector('span:last-child');
        if (valueSpan) {
          valueSpan.style.fontWeight = '500';
          valueSpan.style.color = '#4dd0e1';
        }
      });
    }

    // Keep controls visible when settings are open
    showControls();
  }

  function handleSettingChange(e) {
    const setting = e.currentTarget.dataset.setting;

    if (setting === 'quality') {
      cycleQuality();
    } else if (setting === 'speed') {
      cycleSpeed();
    }
  }

  function cycleQuality() {
    if (!videoPlayer || !qualityValue || !hlsInstance) return;

    // If HLS is available, cycle through available qualities
    if (hlsInstance && hlsInstance.levels && hlsInstance.levels.length > 1) {
      const levels = hlsInstance.levels;
      const currentLevel = hlsInstance.currentLevel;

      // Cycle through levels: auto -> highest -> second highest -> ... -> lowest -> auto
      let nextLevel;
      if (currentLevel === -1) {
        // Currently on auto, switch to highest quality
        nextLevel = 0;
      } else if (currentLevel === levels.length - 1) {
        // Currently on lowest quality, switch to auto
        nextLevel = -1;
      } else {
        // Move to next lower quality
        nextLevel = currentLevel + 1;
      }

      hlsInstance.currentLevel = nextLevel;

      // Update UI
      if (nextLevel === -1) {
        qualityValue.textContent = 'Auto';
      } else {
        const height = levels[nextLevel].height;
        qualityValue.textContent = `${height}p`;
      }
    } else {
      // For non-HLS videos, just show a message
      qualityValue.textContent = 'Auto';
    }
  }

  function cycleSpeed() {
    if (!videoPlayer || !speedValue) return;

    // Cycle through speeds: 0.5 -> 0.75 -> 1 -> 1.25 -> 1.5 -> 2 -> 0.5
    const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2];
    const currentIndex = speeds.indexOf(currentSpeed);
    const nextIndex = (currentIndex + 1) % speeds.length;

    currentSpeed = speeds[nextIndex];
    videoPlayer.playbackRate = currentSpeed;

    // Update UI
    if (currentSpeed === 1) {
      speedValue.textContent = 'Normal';
    } else {
      speedValue.textContent = `${currentSpeed}x`;
    }
  }

  // Controls visibility
  function showControls() {
    if (!playerControls) return;

    clearTimeout(hideControlsTimeout);
    playerControls.style.opacity = '1';
    controlsVisible = true;
  }

  function scheduleHideControls() {
    if (!isPlaying) return;

    clearTimeout(hideControlsTimeout);
    hideControlsTimeout = setTimeout(() => {
      if (playerControls && !settingsMenu?.classList.contains('active')) {
        playerControls.style.opacity = '0';
        controlsVisible = false;
      }
    }, 3000);
  }

  function handlePlayerClick(e) {
    // Refresh element references
    refreshElements();

    // Ignore clicks on controls or settings
    if (e.target.closest('#player-controls') || e.target.closest('#player-settings-menu')) {
      return;
    }

    console.log('Player.js: Player area clicked, controls visible:', controlsVisible);

    // Always show controls first
    showControls();

    // If controls were already visible, toggle play/pause
    if (controlsVisible) {
      // Small delay to ensure UI is updated before toggling
      setTimeout(() => {
        togglePlayPause();
      }, 10);
    }
  }

  // Keyboard shortcuts
  function handleKeyDown(e) {
    // Only handle keys when player is visible
    if (playerContainer.classList.contains('hidden')) return;

    switch (e.key) {
      case ' ':
      case 'k':
        e.preventDefault();
        togglePlayPause();
        break;
      case 'ArrowRight':
        e.preventDefault();
        if (videoPlayer) videoPlayer.currentTime += 10;
        break;
      case 'ArrowLeft':
        e.preventDefault();
        if (videoPlayer) videoPlayer.currentTime -= 10;
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (videoPlayer) videoPlayer.volume = Math.min(1, videoPlayer.volume + 0.1);
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (videoPlayer) videoPlayer.volume = Math.max(0, videoPlayer.volume - 0.1);
        break;
      case 'm':
        e.preventDefault();
        toggleMute();
        break;
      case 'f':
        e.preventDefault();
        toggleFullscreen();
        break;
      case 'Escape':
        if (!isFullscreen) hidePlayer();
        break;
    }
  }

  // Utility functions
  function formatTime(seconds) {
    // Handle invalid input
    if (!isFinite(seconds) || seconds < 0) {
      return '0:00';
    }

    // Format the time
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }

  function handleLoadedMetadata() {
    if (!videoPlayer) return;

    // Update duration display
    if (durationDisplay) {
      durationDisplay.textContent = formatTime(videoPlayer.duration);
    }
  }

  function handleEnded() {
    isPlaying = false;
    if (playPauseIcon) playPauseIcon.className = 'fas fa-play';
    showControls();
  }

  // Function to hide player
  function hidePlayer() {
    if (closePlayerBtn && typeof closePlayerBtn.click === 'function') {
      closePlayerBtn.click();
    }
  }

  // Initialize the player
  initPlayer();

  // Make functions available globally
  window.modernPlayer = {
    setTitle: (title) => {
      if (playerTitle) playerTitle.textContent = title;
      currentMediaTitle = title;
    },
    setHlsInstance: (instance) => {
      hlsInstance = instance;
    },
    refreshElements: refreshElements,
    // Callback for when video element is connected
    onVideoElementConnected: null,
    // Flag to prevent multiple connections
    isConnecting: false,
    // Add a function to connect the video element to our player
    connectVideoElement: () => {
      // Prevent multiple simultaneous connections
      if (window.modernPlayer.isConnecting) {
        console.log('Player.js: Already connecting video element, skipping');
        return false;
      }

      window.modernPlayer.isConnecting = true;
      console.log('Player.js: Connecting video element to modern player');

      // Refresh element references
      refreshElements();

      // Check if player controls exist, if not create them
      if (!playerControls) {
        console.log('Player.js: Creating player controls');

        // Create the controls
        playerControls = document.createElement('div');
        playerControls.id = 'player-controls';
        playerControls.innerHTML = `
          <div id="player-title-bar">
            <div id="player-title">Now Playing</div>
          </div>

          <div id="player-progress-container">
            <div id="player-progress-buffer"></div>
            <div id="player-progress-bar"></div>
            <div id="player-time-tooltip">00:00</div>
          </div>

          <div id="player-buttons">
            <div class="player-button-group">
              <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
                <i class="fas fa-play"></i>
              </button>

              <div id="player-volume-container">
                <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
                  <i class="fas fa-volume-up"></i>
                </button>
                <div id="player-volume-slider">
                  <div id="player-volume-level"></div>
                </div>
              </div>

              <div id="player-time-display">
                <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
              </div>
            </div>

            <div class="player-button-group">
              <button id="player-settings" class="player-button" aria-label="Settings">
                <i class="fas fa-cog"></i>
              </button>
              <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
                <i class="fas fa-expand"></i>
              </button>
            </div>
          </div>
        `;

        // Create the settings menu if it doesn't exist
        let settingsMenu = document.getElementById('player-settings-menu');
        if (!settingsMenu) {
          settingsMenu = document.createElement('div');
          settingsMenu.id = 'player-settings-menu';
          settingsMenu.innerHTML = `
            <div class="player-settings-item" data-setting="quality">
              <span>Quality</span>
              <span id="player-quality-value">Auto</span>
            </div>
            <div class="player-settings-item" data-setting="speed">
              <span>Speed</span>
              <span id="player-speed-value">Normal</span>
            </div>
          `;
        }

        // Add the controls and settings menu to the wrapper
        if (playerWrapper) {
          playerWrapper.appendChild(playerControls);
          playerWrapper.appendChild(settingsMenu);
        }
      }

      // Handle the video element
      if (playerWrapper) {
        // Check if we already have a video element
        let existingVideo = document.getElementById('player');

        // Only create a new video element if one doesn't exist
        if (!existingVideo) {
          console.log('Player.js: Creating new video element');

          // Create a new video element
          const newVideoElement = document.createElement('video');
          newVideoElement.id = 'player';
          newVideoElement.setAttribute('playsinline', '');

          // Style the new video element
          newVideoElement.style.display = 'block';
          newVideoElement.style.width = '100%';
          newVideoElement.style.height = '100%';
          newVideoElement.style.objectFit = 'contain';
          newVideoElement.style.position = 'static';
          newVideoElement.style.zIndex = 'auto';
          newVideoElement.style.opacity = '1';
          newVideoElement.style.visibility = 'visible';
          newVideoElement.style.background = '#000';

          // Insert as the first child
          if (playerWrapper.firstChild) {
            playerWrapper.insertBefore(newVideoElement, playerWrapper.firstChild);
          } else {
            playerWrapper.appendChild(newVideoElement);
          }

          // Refresh references after creating new element
          refreshElements();
        } else {
          console.log('Player.js: Using existing video element');
          videoPlayer = existingVideo;
        }
      } else {
        console.error('Player.js: Player wrapper not found for video element handling');
        window.modernPlayer.isConnecting = false;
        return false;
      }

      // Make sure our event listeners are attached to the video element
      if (videoPlayer) {
        // Remove existing listeners to avoid duplicates
        videoPlayer.removeEventListener('play', handlePlay);
        videoPlayer.removeEventListener('pause', handlePause);
        videoPlayer.removeEventListener('timeupdate', handleTimeUpdate);
        videoPlayer.removeEventListener('loadedmetadata', handleLoadedMetadata);
        videoPlayer.removeEventListener('progress', handleProgress);
        videoPlayer.removeEventListener('ended', handleEnded);
        videoPlayer.removeEventListener('volumechange', updateVolumeUI);

        // Add our listeners
        videoPlayer.addEventListener('play', handlePlay);
        videoPlayer.addEventListener('pause', handlePause);
        videoPlayer.addEventListener('timeupdate', handleTimeUpdate);
        videoPlayer.addEventListener('loadedmetadata', handleLoadedMetadata);
        videoPlayer.addEventListener('progress', handleProgress);
        videoPlayer.addEventListener('ended', handleEnded);
        videoPlayer.addEventListener('volumechange', updateVolumeUI);

        // Set initial volume
        videoPlayer.volume = localStorage.getItem('playerVolume') ?
          parseFloat(localStorage.getItem('playerVolume')) : 1;
        updateVolumeUI();

        // Make sure the wrapper is properly styled
        if (playerWrapper) {
          playerWrapper.style.position = 'relative';
          playerWrapper.style.width = '90%';
          playerWrapper.style.height = '90%';
          playerWrapper.style.maxWidth = '1280px';
          playerWrapper.style.maxHeight = '720px';
          playerWrapper.style.borderRadius = '8px';
          playerWrapper.style.overflow = 'hidden';
          playerWrapper.style.background = '#000';
          playerWrapper.style.opacity = '1';
          playerWrapper.style.transform = 'scale(1)';
          playerWrapper.style.visibility = 'visible';
        }

        // Make sure all controls are visible
        if (playerControls) {
          playerControls.style.position = 'absolute';
          playerControls.style.bottom = '0';
          playerControls.style.left = '0';
          playerControls.style.width = '100%';
          playerControls.style.padding = '20px 20px';
          playerControls.style.background = 'linear-gradient(to top, rgba(0, 0, 0, 0.7) 70%, rgba(0,0,0,0))';
          playerControls.style.display = 'flex';
          playerControls.style.flexDirection = 'column';
          playerControls.style.opacity = '1';
          playerControls.style.visibility = 'visible';
          playerControls.style.zIndex = '2';
          playerControls.classList.add('active');

          // Make sure all child elements are visible
          const allElements = playerControls.querySelectorAll('*');
          allElements.forEach(el => {
            el.style.opacity = '1';
            el.style.visibility = 'visible';
          });
        }

        // Make sure the title bar is properly styled
        const playerTitleBar = document.getElementById('player-title-bar');
        if (playerTitleBar) {
          playerTitleBar.style.display = 'flex';
          playerTitleBar.style.justifyContent = 'space-between';
          playerTitleBar.style.alignItems = 'center';
          playerTitleBar.style.marginBottom = '15px';
          playerTitleBar.style.width = '100%';
        }

        // Make sure the title is visible and properly styled
        const playerTitle = document.getElementById('player-title');
        if (playerTitle) {
          playerTitle.style.color = '#ffffff';
          playerTitle.style.fontSize = '1.2em';
          playerTitle.style.fontWeight = '500';
          playerTitle.style.textShadow = '1px 1px 2px rgba(0, 0, 0, 0.8)';
          playerTitle.style.whiteSpace = 'nowrap';
          playerTitle.style.overflow = 'hidden';
          playerTitle.style.textOverflow = 'ellipsis';
          playerTitle.style.maxWidth = '80%';
          playerTitle.style.opacity = '1';
          playerTitle.style.visibility = 'visible';
        }

        // Make sure the progress container is properly styled
        const progressContainer = document.getElementById('player-progress-container');
        if (progressContainer) {
          progressContainer.style.width = '100%';
          progressContainer.style.height = '6px';
          progressContainer.style.background = 'rgba(255, 255, 255, 0.2)';
          progressContainer.style.borderRadius = '5px';
          progressContainer.style.cursor = 'pointer';
          progressContainer.style.position = 'relative';
          progressContainer.style.marginBottom = '15px';
          progressContainer.style.overflow = 'hidden';
        }

        // Make sure the buttons container is properly styled
        const playerButtons = document.getElementById('player-buttons');
        if (playerButtons) {
          playerButtons.style.display = 'flex';
          playerButtons.style.alignItems = 'center';
          playerButtons.style.justifyContent = 'space-between';
          playerButtons.style.width = '100%';
          playerButtons.style.marginTop = '5px';
          playerButtons.style.opacity = '1';
          playerButtons.style.visibility = 'visible';
        }

        // Make sure all button groups are properly styled
        const buttonGroups = document.querySelectorAll('.player-button-group');
        buttonGroups.forEach(group => {
          group.style.display = 'flex';
          group.style.alignItems = 'center';
          group.style.gap = '15px';
        });

        // Make sure all buttons are properly styled
        const buttons = document.querySelectorAll('.player-button');
        buttons.forEach(button => {
          button.style.background = 'transparent';
          button.style.border = 'none';
          button.style.color = '#ffffff';
          button.style.fontSize = '1.2em';
          button.style.cursor = 'pointer';
          button.style.width = '40px';
          button.style.height = '40px';
          button.style.display = 'flex';
          button.style.alignItems = 'center';
          button.style.justifyContent = 'center';
          button.style.borderRadius = '50%';
          button.style.padding = '0';
          button.style.margin = '0';
        });

        // Make sure the time display is properly styled
        const timeDisplay = document.getElementById('player-time-display');
        if (timeDisplay) {
          timeDisplay.style.color = '#ffffff';
          timeDisplay.style.fontSize = '0.9em';
          timeDisplay.style.margin = '0 15px';
          timeDisplay.style.whiteSpace = 'nowrap';
          timeDisplay.style.minWidth = '100px';
          timeDisplay.style.textAlign = 'center';
        }

        console.log('Player.js: Video element connected successfully');

        // Make sure all event listeners are properly attached
        addEventListeners();

        // Reset the connecting flag
        setTimeout(() => {
          window.modernPlayer.isConnecting = false;
        }, 100);

        // Call the onVideoElementConnected callback if it exists
        if (typeof window.modernPlayer.onVideoElementConnected === 'function') {
          console.log('Player.js: Calling onVideoElementConnected callback');
          try {
            window.modernPlayer.onVideoElementConnected();
          } catch (error) {
            console.error('Player.js: Error in onVideoElementConnected callback:', error);
          }
        }

        // Make sure the player is ready for interaction
        setTimeout(() => {
          // Refresh elements to ensure we have the latest references
          refreshElements();

          // Add event listeners again to ensure they're attached
          addEventListeners();

          console.log('Player.js: Player ready for interaction');
        }, 500);

        return true;
      } else {
        console.error('Player.js: Video element not found');
        window.modernPlayer.isConnecting = false;
        return false;
      }
    }
  };

  // Store the player instance globally
  playerInstance = window.modernPlayer;
  return playerInstance;
}

// Run the initialization immediately
playerInstance = initializePlayer();

// Also run when the DOM is loaded to ensure everything is properly initialized
document.addEventListener('DOMContentLoaded', () => {
  // If player instance already exists, just refresh the elements
  if (playerInstance && playerInstance.refreshElements) {
    console.log('Player.js: Player already initialized, refreshing elements');
    playerInstance.refreshElements();
  } else {
    // Otherwise initialize the player
    playerInstance = initializePlayer();
  }
});
