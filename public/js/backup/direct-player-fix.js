// File: public/js/direct-player-fix.js
// This script directly fixes the player wrapper issue

(function() {
  console.log('Direct player fix: Script loaded');

  // Function to fix the player
  function fixPlayer() {
    // Get the player container
    const playerContainer = document.getElementById('player-container');
    if (!playerContainer) {
      console.error('Direct player fix: Player container not found');
      return;
    }

    // Check if the player wrapper exists
    let playerWrapper = document.getElementById('player-wrapper');

    // If the wrapper doesn't exist, create it
    if (!playerWrapper) {
      console.log('Direct player fix: Creating player wrapper');

      // Get the video and iframe elements
      const player = document.getElementById('player');
      const playerIframe = document.getElementById('player-iframe');
      const closeButton = document.getElementById('close-player');

      if (!player) {
        console.error('Direct player fix: Player element not found');
        return;
      }

      // Create the wrapper
      playerWrapper = document.createElement('div');
      playerWrapper.id = 'player-wrapper';

      // Create the logo
      const playerLogo = document.createElement('div');
      playerLogo.id = 'player-logo';
      playerLogo.textContent = 'NetStream';

      // Create the controls
      const playerControls = document.createElement('div');
      playerControls.id = 'player-controls';
      playerControls.innerHTML = `
        <div id="player-title-bar">
          <div id="player-title">Now Playing</div>
        </div>

        <div id="player-progress-container">
          <div id="player-progress-buffer"></div>
          <div id="player-progress-bar"></div>
          <div id="player-time-tooltip">00:00</div>
        </div>

        <div id="player-buttons">
          <div class="player-button-group">
            <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
              <i class="fas fa-play"></i>
            </button>

            <div id="player-volume-container">
              <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
                <i class="fas fa-volume-up"></i>
              </button>
              <div id="player-volume-slider">
                <div id="player-volume-level"></div>
              </div>
            </div>

            <div id="player-time-display">
              <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
            </div>
          </div>

          <div class="player-button-group">
            <button id="player-settings" class="player-button" aria-label="Settings">
              <i class="fas fa-cog"></i>
            </button>
            <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
              <i class="fas fa-expand"></i>
            </button>
          </div>
        </div>
      `;

      // Create the settings menu
      const settingsMenu = document.createElement('div');
      settingsMenu.id = 'player-settings-menu';
      settingsMenu.innerHTML = `
        <div class="player-settings-item" data-setting="quality">
          <span>Quality</span>
          <span id="player-quality-value">Auto</span>
        </div>
        <div class="player-settings-item" data-setting="speed">
          <span>Speed</span>
          <span id="player-speed-value">Normal</span>
        </div>
      `;

      // Move the player and iframe into the wrapper
      playerContainer.removeChild(player);
      if (playerIframe) {
        playerContainer.removeChild(playerIframe);
      }

      // Add elements to the wrapper
      playerWrapper.appendChild(playerLogo);
      playerWrapper.appendChild(player);
      if (playerIframe) {
        playerWrapper.appendChild(playerIframe);
      }
      playerWrapper.appendChild(playerControls);
      playerWrapper.appendChild(settingsMenu);

      // Add the wrapper to the container before the close button
      if (closeButton) {
        playerContainer.insertBefore(playerWrapper, closeButton);
      } else {
        playerContainer.appendChild(playerWrapper);
      }

      console.log('Direct player fix: Player wrapper created successfully');
    } else {
      console.log('Direct player fix: Player wrapper already exists');
    }

    // Initialize the modern player
    if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
      // Check if already connecting to avoid infinite loops
      if (window.modernPlayer.isConnecting) {
        console.log('Direct player fix: Already connecting video element, skipping');
        return;
      }

      console.log('Direct player fix: Connecting video element to modern player');

      // First refresh elements to ensure we have the latest references
      if (window.modernPlayer.refreshElements) {
        console.log('Direct player fix: Refreshing player elements');
        window.modernPlayer.refreshElements();
      }

      // Then connect the video element
      window.modernPlayer.connectVideoElement();
    }
  }

  // Run the fix immediately
  fixPlayer();

  // Also run the fix when the DOM is fully loaded
  document.addEventListener('DOMContentLoaded', fixPlayer);

  // Run the fix when the player is shown
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' &&
          mutation.attributeName === 'class') {
        const playerContainer = document.getElementById('player-container');
        if (playerContainer && !playerContainer.classList.contains('hidden')) {
          console.log('Direct player fix: Player container shown, applying fix');
          fixPlayer();
        }
      }
    });
  });

  // Start observing the player container
  const playerContainer = document.getElementById('player-container');
  if (playerContainer) {
    observer.observe(playerContainer, { attributes: true });
    console.log('Direct player fix: MutationObserver started');
  }
})();
