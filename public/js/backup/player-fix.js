// File: public/js/player-fix.js
// This script fixes the integration between the modern player UI and the media.js file

document.addEventListener('DOMContentLoaded', () => {
  console.log('Player-fix.js: Script loaded');

  // Wait for all scripts to be loaded
  setTimeout(() => {
    console.log('Player-fix.js: Applying fixes');

    // Get player elements
    const playerContainer = document.getElementById('player-container');
    const player = document.getElementById('player');
    const playerIframe = document.getElementById('player-iframe');
    const playerWrapper = document.getElementById('player-wrapper');
    const playerControls = document.getElementById('player-controls');

    if (!playerContainer || !player) {
      console.error('Player-fix.js: Required player elements not found');
      return;
    }

    console.log('Player-fix.js: Player elements found');

    // Function to ensure the player is properly displayed
    function ensurePlayerIsVisible() {
      if (!playerContainer.classList.contains('hidden')) {
        console.log('Player-fix.js: Player is visible, ensuring controls are shown');

        // Make sure player wrapper is visible
        if (playerWrapper) {
          playerWrapper.style.opacity = '1';
          playerWrapper.style.transform = 'scale(1)';
          playerWrapper.style.visibility = 'visible';
        }

        // Make sure controls are visible
        if (playerControls) {
          playerControls.style.opacity = '1';
          playerControls.classList.add('active');
          playerControls.style.visibility = 'visible';

          // Force all child elements to be visible
          const controlElements = playerControls.querySelectorAll('*');
          controlElements.forEach(element => {
            element.style.opacity = '1';
            element.style.visibility = 'visible';
          });
        }

        // Connect video element to modern player
        if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
          // Check if already connecting to avoid infinite loops
          if (window.modernPlayer.isConnecting) {
            console.log('Player-fix.js: Already connecting video element, skipping');
            return;
          }

          console.log('Player-fix.js: Connecting video element to modern player');

          // First refresh elements to ensure we have the latest references
          if (window.modernPlayer.refreshElements) {
            console.log('Player-fix.js: Refreshing player elements');
            window.modernPlayer.refreshElements();
          }

          // Then connect the video element
          window.modernPlayer.connectVideoElement();
        }

        // Force a reflow to ensure CSS changes are applied
        playerContainer.offsetHeight;
      }
    }

    // Override the playItem function to ensure our modern player is used
    if (typeof window.playItem === 'function') {
      console.log('Player-fix.js: Found playItem function, creating override');

      const originalPlayItem = window.playItem;

      window.playItem = function(url, isSourceStream = false, method = 'GET') {
        console.log('Player-fix.js: Overridden playItem called', { url, isSourceStream, method });

        // Call the original function
        originalPlayItem.call(this, url, isSourceStream, method);

        // Ensure our player is properly displayed
        setTimeout(ensurePlayerIsVisible, 100);
      };

      console.log('Player-fix.js: Successfully overrode playItem function');
    } else {
      console.warn('Player-fix.js: playItem function not found');
    }

    // Add a MutationObserver to watch for changes to the player container
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' &&
            mutation.attributeName === 'class' &&
            !playerContainer.classList.contains('hidden')) {
          console.log('Player-fix.js: Player container class changed to visible');
          ensurePlayerIsVisible();
        }
      });
    });

    // Start observing the player container
    observer.observe(playerContainer, { attributes: true });

    console.log('Player-fix.js: MutationObserver started');

    // Check if player is already visible
    ensurePlayerIsVisible();

    // Add event listener for video element
    if (player) {
      player.addEventListener('play', () => {
        console.log('Player-fix.js: Video play event detected');
        ensurePlayerIsVisible();
      });

      player.addEventListener('loadedmetadata', () => {
        console.log('Player-fix.js: Video metadata loaded');
        ensurePlayerIsVisible();
      });

      player.addEventListener('canplay', () => {
        console.log('Player-fix.js: Video can play');
        ensurePlayerIsVisible();
      });
    }

    // Run the check periodically to ensure the player is visible, but less frequently
    // to avoid potential infinite loops
    let visibilityCheckCount = 0;
    const visibilityCheckInterval = setInterval(() => {
      if (!playerContainer.classList.contains('hidden')) {
        // Only run a few times to avoid infinite loops
        if (visibilityCheckCount < 5) {
          console.log('Player-fix.js: Periodic visibility check', visibilityCheckCount);
          ensurePlayerIsVisible();
          visibilityCheckCount++;
        } else if (visibilityCheckCount === 5) {
          console.log('Player-fix.js: Stopping periodic visibility checks');
          clearInterval(visibilityCheckInterval);
          visibilityCheckCount++;
        }
      }
    }, 2000);

    console.log('Player-fix.js: Fixes applied successfully');
  }, 500); // Wait 500ms to ensure all scripts are loaded
});
