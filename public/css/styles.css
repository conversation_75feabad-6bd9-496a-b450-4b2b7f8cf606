/* File: /home/<USER>/NetStream/public/css/styles.css */

/* Admin Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--background-card);
  margin: 10% auto;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  width: 80%;
  max-width: 500px;
  box-shadow: var(--shadow-heavy);
  animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal .close {
  color: var(--text-secondary);
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: color var(--transition-fast);
}

.modal .close:hover {
  color: var(--primary-color);
}

.modal h2 {
  margin-top: 0;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.admin-login-form input {
  padding: 12px;
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
  background: var(--background-elevated);
  color: var(--text-primary);
  font-size: 1em;
}

.admin-login-form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

.admin-login-form button,
.modal-buttons button,
#admin-logout {
  padding: 12px;
  border-radius: var(--border-radius-small);
  border: none;
  background: var(--primary-color);
  color: var(--text-primary);
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.admin-login-form button:hover,
.modal-buttons button:hover,
#admin-logout:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.message {
  padding: 10px;
  border-radius: var(--border-radius-small);
  margin-top: 10px;
  font-size: 0.9em;
}

.message.info {
  background-color: rgba(0, 188, 212, 0.2);
  color: var(--primary-color);
}

.message.success {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.message.error {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.button.secondary {
  background-color: var(--background-elevated);
  border: 1px solid var(--border-color);
}

.button.danger {
  background-color: #f44336;
}

.button.danger:hover {
  background-color: #d32f2f;
}

/* Admin panel styles */
.admin-section {
  margin: 20px 0;
  padding: 15px;
  background-color: var(--background-elevated);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.admin-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--primary-color);
  font-size: 1.1em;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

/* Tab styles */
.admin-tabs {
  margin: 20px 0;
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 15px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: bold;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

.tab-pane {
  display: none;
  padding: 15px;
  background-color: var(--background-elevated);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
}

.tab-pane.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.manual-scrape-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.9em;
}

.input-with-button {
  display: flex;
  gap: 10px;
}

.input-with-button input {
  flex: 1;
  padding: 10px;
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
  background: var(--background-card);
  color: var(--text-primary);
  font-size: 0.9em;
}

.input-with-button input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

.manual-scrape-form input,
.manual-scrape-form select {
  padding: 10px;
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-color);
  background: var(--background-card);
  color: var(--text-primary);
  font-size: 0.9em;
  width: 100%;
}

.manual-scrape-form input:focus,
.manual-scrape-form select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

.manual-scrape-form select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='%2300bcd4'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 35px;
}

.manual-scrape-form button {
  padding: 12px;
  border-radius: var(--border-radius-small);
  border: none;
  background: var(--primary-color);
  color: var(--text-primary);
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-top: 10px;
}

.manual-scrape-form button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.button.small {
  padding: 8px 12px;
  font-size: 0.9em;
  min-width: 80px;
}

.admin-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.item-info {
  background-color: var(--background-elevated);
  padding: 15px;
  border-radius: var(--border-radius-small);
  margin: 15px 0;
  line-height: 1.6;
}

/* Delete button on grid items */
.delete-button {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 32px;
  height: 32px;
  background-color: rgba(244, 67, 54, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  display: none; /* Hidden by default */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all var(--transition-fast);
  backdrop-filter: blur(2px);
}

/* Only show delete buttons in admin mode */
.admin-mode .delete-button {
  display: flex;
}

/* Admin controls on detail page */
.admin-detail-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: none; /* Hidden by default */
  flex-direction: row;
  gap: 20px; /* Increased gap between buttons */
  z-index: 10;
}

.admin-mode .admin-detail-controls {
  display: flex;
}

.admin-detail-button-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.admin-detail-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 1.2em;
  transition: all var(--transition-fast);
  backdrop-filter: blur(3px);
}

.admin-detail-button.delete-button {
  background-color: rgba(244, 67, 54, 0.8);
}

.admin-detail-button.scrape-button {
  background-color: rgba(0, 188, 212, 0.8);
}

.admin-detail-button:hover {
  transform: scale(1.1);
}

.admin-detail-button.delete-button:hover {
  background-color: #d32f2f;
}

.admin-detail-button.scrape-button:hover {
  background-color: #0097a7;
}

/* Scrape log modal */
.scrape-log-content {
  max-width: 800px;
  width: 90%;
}

.scrape-status {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background-color: var(--background-elevated);
  border-radius: var(--border-radius-small);
}

.scrape-spinner {
  font-size: 1.5em;
  color: var(--primary-color);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scrape-spinner.success {
  color: #4caf50;
}

.scrape-spinner.error {
  color: #f44336;
}

#scrape-status-message {
  font-weight: 500;
  flex: 1;
}

.scrape-log {
  height: 400px;
  overflow-y: auto;
  background-color: var(--background-elevated);
  border-radius: var(--border-radius-small);
  padding: 10px;
  margin-bottom: 15px;
  font-family: monospace;
  font-size: 0.9em;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-item {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 3px;
}

.log-item.info {
  border-left: 3px solid var(--primary-color);
}

.log-item.error {
  border-left: 3px solid #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.log-item.warn {
  border-left: 3px solid #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.log-item.success {
  border-left: 3px solid #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.log-item.debug {
  border-left: 3px solid #9c27b0;
  background-color: rgba(156, 39, 176, 0.05);
  font-size: 0.9em;
}

/* Special styling for problematic URL logs */
.log-item.special {
  border-left: 3px solid #e91e63;
  background-color: rgba(233, 30, 99, 0.05);
  font-weight: 500;
}

.log-timestamp {
  color: var(--text-tertiary);
  margin-right: 5px;
}

.log-level {
  font-weight: bold;
  margin-right: 5px;
}

.log-level.info {
  color: var(--primary-color);
}

.log-level.error {
  color: #f44336;
}

.log-level.warn {
  color: #ff9800;
}

.log-level.success {
  color: #4caf50;
}

.log-level.debug {
  color: #9c27b0;
}

.log-meta {
  margin-top: 3px;
  padding-left: 10px;
  font-size: 0.9em;
  color: var(--text-secondary);
  border-left: 1px solid var(--border-color);
}

.reconnect-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-small);
}

.reconnect-container .button {
  padding: 8px 15px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.reconnect-container .button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.delete-button:hover {
  transform: scale(1.1);
  background-color: #d32f2f;
}

:root {
  --primary-color: #00bcd4;
  --primary-color-rgb: 0, 188, 212;
  --primary-dark: #0097a7;
  --primary-light: #4dd0e1;
  --primary-gradient: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
  --background-dark: #0a0a0a;
  --background-card: #1a1a1a;
  --background-elevated: #2a2a2a;
  --background-hero: #141414;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #737373;
  --border-color: #333333;
  --border-color-light: #404040;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.5);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.8);
  --shadow-glow: 0 0 20px rgba(0, 188, 212, 0.3);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --border-radius-small: 6px;
  --border-radius-medium: 12px;
  --border-radius-large: 16px;
  --border-radius-xl: 24px;
  --spacing-xs: 6px;
  --spacing-sm: 12px;
  --spacing-md: 18px;
  --spacing-lg: 24px;
  --spacing-xl: 36px;
  --spacing-xxl: 48px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--background-dark);
  color: var(--text-primary);
  padding-top: 0;
  overflow-x: hidden;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

.container {
  display: flex;
  min-height: 100vh;
  overflow-x: hidden;
}

.sidebar {
  width: 90px;
  background: linear-gradient(180deg, var(--background-card) 0%, rgba(26, 26, 26, 0.95) 100%);
  color: var(--text-primary);
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-medium);
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  transition: all var(--transition-normal);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
}

.sidebar:hover {
  box-shadow: var(--shadow-heavy);
  background: linear-gradient(180deg, var(--background-elevated) 0%, rgba(42, 42, 42, 0.98) 100%);
}

.sidebar h2 {
  margin: 0 0 32px;
  font-family: 'Poppins', sans-serif;
  font-size: 1.6em;
  font-weight: 700;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--primary-color);
  background: var(--primary-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  width: 100%;
  text-align: center;
  margin-left: 0;
  margin-right: 0px;
  padding-left: 0;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  letter-spacing: 2px;
  transition: all var(--transition-fast);
  position: relative;
}

.sidebar h2::after {
  content: '';
  position: absolute;
  bottom: 14px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: var(--primary-gradient);
  border-radius: 2px;
  box-shadow: var(--shadow-glow);
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sidebar a {
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border-radius: var(--border-radius-large);
  margin-bottom: 18px;
  transition: all var(--transition-normal);
  font-size: 0.85em;
  width: 66px;
  box-sizing: border-box;
  text-align: center;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.sidebar a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--primary-gradient);
  transform: scaleY(0);
  transition: transform var(--transition-normal);
  transform-origin: bottom;
  border-radius: 0 4px 4px 0;
}

.sidebar a::after {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
  border-radius: var(--border-radius-large);
  z-index: -1;
}

.sidebar a.active::before {
  transform: scaleY(1);
}

.sidebar a.active::after {
  opacity: 0.1;
}

.sidebar a:hover,
.sidebar a.active {
  color: var(--text-primary);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-glow);
}

.sidebar a:hover::after {
  opacity: 0.08;
}

.sidebar a.active {
  color: var(--primary-color);
  font-weight: 600;
}

.sidebar a i {
  margin-right: 0;
  margin-bottom: 6px;
  font-size: 1.5em;
  width: 24px;
  text-align: center;
  transition: all var(--transition-fast);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.sidebar a:hover i,
.sidebar a.active i {
  transform: scale(1.15) translateY(-1px);
  filter: drop-shadow(0 4px 8px rgba(0, 188, 212, 0.4));
}

.content {
  flex: 1;
  margin-left: 110px;
  overflow-x: hidden;
  position: relative;
  transition: margin-left var(--transition-normal);
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(180deg, var(--background-dark) 0%, rgba(10, 10, 10, 0.98) 100%);
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 5;
  background: rgba(10, 10, 10, 0.85);
  padding: 20px 32px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color-light);
}

.search-bar input {
  padding: 16px 24px;
  border-radius: var(--border-radius-xl);
  border: 2px solid var(--border-color);
  background: var(--background-elevated);
  color: var(--text-primary);
  font-size: 1.05em;
  flex-grow: 1;
  transition: all var(--transition-fast);
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.2);
  font-weight: 400;
}

.search-bar input::placeholder {
  color: var(--text-tertiary);
  opacity: 0.8;
  font-weight: 400;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.15), var(--shadow-glow);
  background: var(--background-card);
}

.search-bar button {
  padding: 16px 28px;
  border-radius: var(--border-radius-xl);
  border: none;
  background: var(--primary-gradient);
  color: var(--text-primary);
  font-size: 1.05em;
  font-weight: 600;
  cursor: pointer;
  white-space: nowrap;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.search-bar button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.search-bar button:hover::before {
  opacity: 1;
}

.search-bar button i {
  margin: 0;
  transition: transform var(--transition-fast);
}

.search-bar button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-heavy), var(--shadow-glow);
}

.search-bar button:hover i {
  transform: scale(1.1) rotate(5deg);
}

.search-bar button:active {
  transform: translateY(-1px) scale(0.98);
  box-shadow: var(--shadow-medium);
}

.return-arrow {
  display: none; /* Hidden by default, shown on media pages */
  position: fixed; /* Changed to fixed to stay visible while scrolling */
  top: 20px;
  left: 30px; /* Moved closer to the left corner */
  color: var(--primary-color);
  font-size: 1.5em;
  text-decoration: none;
  background: var(--background-elevated);
  padding: 10px;
  border-radius: 50%;
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
  z-index: 6; /* Above content but below player */
  width: 44px; /* Fixed size for circle */
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
}

.return-arrow i {
  margin: 0; /* Ensure no extra spacing around icon */
  transition: transform var(--transition-fast);
}

.return-arrow:hover {
  background: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.return-arrow:hover i {
  transform: translateX(-3px);
}

body.media-page .return-arrow {
  display: flex; /* Show on media pages with flex for centering */
}

.section {
  display: none;
  animation: fadeIn var(--transition-slow);
  padding: 0;
  opacity: 0;
  transform: translateY(10px);
}

.section.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

/* Hero Section Styles */
.hero-section {
  position: relative;
  height: 60vh;
  min-height: 400px;
  max-height: 600px;
  background: linear-gradient(135deg, var(--background-hero) 0%, var(--background-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: var(--spacing-xxl);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: blur(8px) brightness(0.3);
  transform: scale(1.1);
  z-index: 0;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.hero-background.loaded {
  opacity: 1;
}

.hero-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 188, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 188, 212, 0.08) 0%, transparent 50%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 var(--spacing-xl);
}

.hero-title {
  font-family: 'Poppins', sans-serif;
  font-size: 3.5em;
  font-weight: 900;
  margin-bottom: var(--spacing-lg);
  background: var(--primary-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -2px;
  line-height: 1.1;
}

.hero-description {
  font-size: 1.2em;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xxl);
  line-height: 1.6;
  font-weight: 400;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 16px 32px;
  border-radius: var(--border-radius-xl);
  border: none;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  overflow: hidden;
  min-width: 180px;
  justify-content: center;
}

.hero-btn.primary {
  background: var(--primary-gradient);
  color: var(--text-primary);
  box-shadow: var(--shadow-heavy);
}

.hero-btn.primary::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.hero-btn.primary:hover::before {
  opacity: 1;
}

.hero-btn.primary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-heavy), var(--shadow-glow);
}

.hero-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: 2px solid var(--border-color-light);
  backdrop-filter: blur(20px);
}

.hero-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-color);
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-medium);
}

.hero-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, transparent 0%, var(--background-dark) 100%);
  z-index: 1;
}

.section h2 {
  font-family: 'Poppins', sans-serif;
  font-weight: 800;
  font-size: 2.2em;
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
  position: relative;
  padding-bottom: var(--spacing-sm);
  letter-spacing: -1px;
  background: var(--primary-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: 4px;
  box-shadow: var(--shadow-glow);
}

/* Grid styles - only used for search results now */
#search-list.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
  padding: var(--spacing-xl) var(--spacing-lg);
}

/* Hide grid containers for main sections (movies, series, anime) */
#movies-list.grid,
#series-list.grid,
#anime-list.grid {
  display: none;
}

.grid-item {
  background: var(--background-card);
  border-radius: 6px;
  text-align: left;
  color: var(--text-primary);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.25s ease-out;
  overflow: hidden;
  position: relative;
  display: block;
  aspect-ratio: 3/4;
  border: none;
  transform: translateZ(0);
  height: 240px;
}

.grid-item::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.8) 100%);
  opacity: 1;
  transition: all 0.25s ease-out;
  z-index: 2;
  pointer-events: none;
}

.grid-item:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.grid-item:hover::before {
  background: linear-gradient(to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.9) 100%);
}

/* Netflix-style play button overlay */
.grid-item::after {
  content: '▶';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: white;
  opacity: 0;
  transition: all 0.25s ease-out;
  z-index: 4;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  pointer-events: none;
}

.grid-item:hover::after {
  opacity: 0.9;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Improve focus states for accessibility */
.grid-item:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.grid-item img.thumbnail,
.grid-item .thumbnail-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 0;
  display: block;
  transition: all 0.25s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimizeQuality;
  -ms-interpolation-mode: bicubic;
}

.grid-item:hover img.thumbnail {
  transform: scale(1.1);
}

.grid-item img.banner {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
  margin-bottom: 0;
  display: block;
  transition: transform var(--transition-normal);
}

.grid-item .thumbnail-placeholder {
  background: var(--background-elevated);
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-item div:last-child {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  font-size: 0.85em;
  font-weight: 700;
  text-align: left;
  z-index: 3;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 70%, rgba(0, 0, 0, 0) 100%);
  transition: all 0.25s ease-out;
  color: white;
  line-height: 1.2;
  font-family: 'Netflix Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.grid-item:hover div:last-child {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.1) 100%);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
}

/* Season/Episode info styling */
.season-episode {
  display: inline-block;
  padding: 4px 8px;
  background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
  border: 1px solid rgba(0, 188, 212, 0.8);
  border-radius: 4px;
  font-size: 0.75em;
  font-weight: 600;
  color: white;
  margin: 3px 2px;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  box-shadow: 0 2px 8px rgba(0, 188, 212, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  transition: all 0.25s ease-out;
}

/* Enhanced hover effect for season-episode badges */
.grid-item:hover .season-episode,
.carousel-item:hover .season-episode {
  background: linear-gradient(135deg, #00e5ff 0%, #00bcd4 100%);
  border-color: #00e5ff;
  box-shadow: 0 4px 12px rgba(0, 229, 255, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
  transform: scale(1.05);
}

.grid-item .extra-info {
  font-size: 0.75em;
  color: var(--text-secondary);
  margin-top: 6px;
  opacity: 0.8;
  font-style: italic;
}

.grid-item .stream-indicator {
  color: var(--primary-color);
  margin-left: 5px;
  font-size: 0.9em;
}

.grid-item .provider-name {
  margin-bottom: 8px;
  font-weight: 500;
  padding: 8px;
  border-radius: var(--border-radius-small);
  background-color: var(--background-elevated);
  width: 100%;
}

.grid-item .source-stream-url {
  width: 100%;
  padding: 0 8px 8px;
}

.grid-item .source-stream-url button {
  background-color: var(--background-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  transition: all var(--transition-fast);
  margin-top: 5px;
  width: 100%;
  cursor: pointer;
  display: block;
}

.grid-item .source-stream-url button:hover,
.grid-item .source-stream-url button:focus {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  outline: none;
}

/* Make source buttons focusable with remote control */
.remote-navigation .grid-item .source-stream-url button.remote-focus {
  background-color: var(--primary-color);
  color: white;
  border-color: white;
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(0, 188, 212, 0.7);
}

.grid-item .rating-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7em;
  font-weight: 600;
  z-index: 3;
  backdrop-filter: blur(4px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  font-family: 'Netflix Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

#series-list .grid-item {
  aspect-ratio: auto;
  min-height: 220px;
}

/* Player styles moved to player.css */

.banner {
  width: 100%;
  height: 500px;
  background-size: cover;
  background-position: center;
  margin-bottom: 0px;
  position: relative;
  border-radius: 0;
  overflow: visible;
  transition: all var(--transition-normal);
}

.banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 85%,
    rgba(0, 0, 0, 0.95) 100%
  );
  z-index: 1;
}

.banner::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent 0%,
    var(--primary-color) 50%,
    transparent 100%
  );
  opacity: 0.7;
}

#media-title {
  position: absolute;
  bottom: 40px;
  left: 40px;
  font-size: 3.2em;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  color: var(--text-primary);
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.9);
  z-index: 2;
  text-align: left;
  width: 90%;
  max-width: 80%;
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.media-meta {
  position: absolute;
  bottom: 20px;
  left: 40px;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 15px;
}

.media-meta span {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: 0.9em;
  gap: 5px;
}

.media-meta span i {
  color: var(--primary-color);
}

#media-wishlist-button {
  /* Remove conflicting styles - let media-modern.css handle it */
  position: relative;
  top: auto;
  right: auto;
  margin-left: 0;
  opacity: 1;
}

#media-wishlist-button i {
  font-size: 1rem;
}

#media-wishlist-button.active {
  color: #8e24aa !important;
  border-color: #8e24aa !important;
  background-color: rgba(142, 36, 170, 0.2) !important;
}

#media-wishlist-button.active:hover {
  background-color: rgba(142, 36, 170, 0.3) !important;
}

.media-content {
  padding: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.media-content h1,
.media-content h2,
.media-content h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.media-content h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

#media-info {
  margin-bottom: var(--spacing-xl);
  position: relative;
}

#media-info dl {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px 30px;
  padding: var(--spacing-xl);
  background: var(--background-card);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

/* TMDB and Jikan data styling */
.metadata-section {
  margin-top: 20px;
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
  width: 100%;
  grid-column: 1 / -1;
}

/* Hide specific TMDB metadata sections from frontend while keeping logic */
.metadata-section.tmdb-section {
  display: none !important;
}

/* TMDB seasons section - hidden by default, can be shown by JavaScript */
.metadata-section.tmdb-seasons-section {
  display: none;
}

.metadata-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.metadata-header h3 {
  font-size: 1.1em;
  margin: 0;
  display: flex;
  align-items: center;
}

.metadata-header h3 i {
  margin-right: 8px;
}

.expand-toggle {
  background: var(--background-elevated);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.expand-toggle:hover {
  background: var(--primary-color);
  color: white;
}

.expand-toggle i {
  transition: transform var(--transition-normal);
}

.expand-toggle.expanded i {
  transform: rotate(45deg);
}

.core-metadata, .expandable-metadata, .all-metadata {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px 20px;
  margin-bottom: 10px;
}

.core-metadata > div, .expandable-metadata > div, .all-metadata > div {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5px;
}

.all-metadata > div:last-child {
  grid-column: 1 / -1;
}

.expandable-metadata {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal), opacity var(--transition-normal);
  opacity: 0;
  margin-bottom: 0;
}

.expandable-metadata.expanded {
  max-height: 500px;
  opacity: 1;
  margin-top: 10px;
  border-top: 1px dashed var(--border-color);
  padding-top: 10px;
}

.tmdb-section .metadata-header h3,
.tmdb-seasons-section .metadata-header h3 {
  color: #01b4e4; /* TMDB blue color */
}

.jikan-section .metadata-header h3 {
  color: #2e51a2; /* MAL blue color */
}

.tmdb-data dt {
  color: #01b4e4 !important; /* TMDB blue color */
}

.jikan-data dt {
  color: #2e51a2 !important; /* MAL blue color */
}

/* TMDB Seasons Styling */
/* Note: Display is controlled by JavaScript based on data availability */

.tmdb-seasons-select-container {
  margin-bottom: 15px;
}

.tmdb-seasons-select-container label {
  margin-right: 10px;
  font-weight: 600;
}

.tmdb-seasons-select-container select {
  padding: 5px 10px;
  border-radius: var(--border-radius-small);
  background: var(--background-elevated);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tmdb-season-details {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.tmdb-season-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px 20px;
}

.tmdb-season-info > div {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5px;
}

.tmdb-season-info > div:last-child {
  grid-column: 1 / -1;
}

.tmdb-season-episodes h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1em;
  color: #01b4e4;
}

.tmdb-episodes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.tmdb-episode-card {
  background: var(--background-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  padding: 10px;
  transition: all var(--transition-fast);
}

.tmdb-episode-card:hover {
  border-color: #01b4e4;
  box-shadow: 0 0 5px rgba(1, 180, 228, 0.3);
}

.tmdb-episode-card h5 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 1em;
  color: var(--text-primary);
}

.tmdb-episode-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9em;
  color: var(--text-secondary);
}

.tmdb-episode-overview {
  font-size: 0.9em;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.play-episode-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-small);
  padding: 5px 10px;
  cursor: pointer;
  font-size: 0.9em;
  margin-top: 5px;
  transition: background-color var(--transition-fast);
}

.play-episode-btn:hover {
  background-color: var(--primary-color-hover);
}

#media-info dt {
  font-weight: 600;
  color: var(--text-secondary);
  text-align: left;
  display: flex;
  align-items: center;
  font-size: 1em;
}

#media-info dt i {
  margin-right: 10px;
  color: var(--primary-color);
  width: 20px;
  font-size: 1.1em;
}

#media-info dd {
  color: var(--text-primary);
  margin: 0;
  text-align: left;
  word-break: break-word;
  line-height: 1.5;
  font-size: 1em;
}

#seasons {
  margin-bottom: var(--spacing-xl);
}

#seasons h3 {
  margin-bottom: var(--spacing-md);
}

#season-select {
  padding: 12px 18px;
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
  background: var(--background-elevated);
  color: var(--text-primary);
  font-size: 1em;
  width: 50%;
  max-width: 180px;
  margin-bottom: var(--spacing-md);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='%2300bcd4'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
  box-shadow: var(--shadow-light);
  transition: all var(--transition-fast);
  cursor: pointer;
}

#season-select:hover,
#season-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

#episodes.grid {
  margin-bottom: var(--spacing-xl);
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: var(--spacing-sm);
}

#episodes .grid-item {
  padding: 10px 8px;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
  min-height: 40px;
  border-radius: var(--border-radius-small);
  aspect-ratio: auto;
  background: var(--background-card);
  box-shadow: var(--shadow-light);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

#episodes .grid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: var(--primary-color);
  transform: scaleY(0);
  transition: transform var(--transition-normal);
  transform-origin: bottom;
}

#episodes .grid-item:hover {
  background-color: var(--background-elevated);
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

#episodes .grid-item:hover::before {
  transform: scaleY(1);
}

#providers.grid {
  grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));
  gap: var(--spacing-md);
}

#providers.grid .grid-item {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  background: var(--background-card);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
}

#providers.grid .grid-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
  background: var(--background-elevated);
}

.source-stream-url {
  margin-top: var(--spacing-sm);
  width: 100%;
}

.source-stream-url button {
  width: 100%;
  padding: 8px 12px;
  background: var(--background-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.source-stream-url button:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.source-stream-url button {
  width: 100%;
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  background-color: #00bcd4;
  color: #fff;
  font-size: 0.85em;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 3px;
}

.source-stream-url button:hover {
  background-color: #0097a7;
}

.source-stream-url button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

.filter-controls {
  margin-bottom: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: 0 var(--spacing-xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg) var(--spacing-xl);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  margin: 0 var(--spacing-xl) var(--spacing-xl);
}

.filter-controls label {
  color: var(--text-secondary);
  margin-right: 5px;
  white-space: nowrap;
  font-weight: 500;
  font-size: 0.95em;
}

.filter-controls select {
  background: var(--background-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 10px 16px;
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  margin-left: 5px;
  font-size: 0.95em;
  box-shadow: var(--shadow-light);
  transition: all var(--transition-fast);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='%2300bcd4'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 35px;
}

.filter-controls select:hover,
.filter-controls select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
}

/* Section scrape button */
.section-scrape-button {
  display: none;
  width: 36px;
  height: 36px;
  background-color: rgba(0, 188, 212, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(2px);
  margin-left: auto;
}

.section-scrape-button:hover {
  background-color: #0097a7;
  transform: scale(1.1);
}

/* Only show scrape buttons in admin mode */
.admin-mode .section-scrape-button {
  display: flex;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  border-right: 3px solid var(--primary-color);
  width: 40px;
  height: 40px;
  animation: spin 0.8s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  z-index: 100;
  display: none;
  box-shadow: 0 0 10px rgba(0, 188, 212, 0.2);
}

.loading-indicator.active {
  display: block;
}

.loading-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

/* Skeleton loading for grid items */
.grid-item.skeleton {
  background: var(--background-card);
  position: relative;
  overflow: hidden;
  min-height: 280px;
  border-radius: var(--border-radius-large);
}

.grid-item.skeleton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    110deg,
    rgba(0, 188, 212, 0.05) 0%,
    rgba(0, 188, 212, 0.15) 50%,
    rgba(0, 188, 212, 0.05) 100%
  );
  animation: shimmer 2s infinite;
  background-size: 200% 100%;
}

.grid-item.skeleton .thumbnail-placeholder {
  height: 240px;
  background: var(--background-elevated);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.grid-item.skeleton div:last-child {
  height: 50px;
  background: var(--background-elevated);
  margin: 12px;
  border-radius: var(--border-radius-small);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: var(--shadow-medium);
  }
  50% {
    box-shadow: var(--shadow-glow);
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

.trending-movies-grid .grid-item img,
.trending-series-grid .grid-item img,
.trending-anime-grid .grid-item img {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
  display: block;
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  /* Bottom navigation bar for mobile */
  .container .sidebar {
    width: 100% !important;
    height: 70px !important;
    position: fixed !important;
    bottom: 0 !important;
    top: auto !important;
    left: 0 !important;
    z-index: 1000 !important;
    padding: 8px 16px !important;
    flex-direction: row !important;
    justify-content: space-around !important;
    align-items: center !important;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3) !important;
    background: var(--background-card) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-right: none !important;
    writing-mode: initial !important;
    text-orientation: initial !important;
  }

  .container .sidebar ul {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-around !important;
    align-items: center !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    margin-top: 0 !important;
  }

  .container .sidebar ul li {
    margin: 0 !important;
    flex: 1 !important;
    display: flex !important;
    justify-content: center !important;
  }

  .container .sidebar h2 {
    display: none !important;
  }

  .container .sidebar a {
    width: auto !important;
    margin-bottom: 0 !important;
    padding: 8px 12px !important;
    border-radius: var(--border-radius-medium) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 4px !important;
    font-size: 0.7em !important;
    min-width: 50px !important;
    text-align: center !important;
  }

  .container .sidebar a i {
    margin: 0 !important;
    font-size: 1.4em !important;
  }

  .container .sidebar a span {
    font-size: 0.8em !important;
    font-weight: 500 !important;
  }

  .content {
    margin-left: 0 !important;
    padding: 0 !important;
    padding-bottom: 90px !important; /* Space for bottom navigation */
  }

  /* Hide hero section in phone mode */
  .hero-section {
    display: none !important;
  }

  /* Hide regular search bar in phone mode */
  .search-bar {
    display: none !important;
  }

  /* Bottom search modal for mobile */
  .mobile-search-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--background-card);
    border-top: 1px solid var(--border-color);
    border-radius: 16px 16px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 2000;
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
  }

  .mobile-search-modal.show {
    transform: translateY(0);
  }

  .mobile-search-modal .search-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 100%;
  }

  .mobile-search-modal .search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .mobile-search-modal .search-title {
    font-size: 1.2em;
    font-weight: 600;
    color: var(--text-primary);
  }

  .mobile-search-modal .close-search {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5em;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
  }

  .mobile-search-modal .close-search:hover {
    background: var(--background-elevated);
    color: var(--text-primary);
  }

  .mobile-search-modal input {
    width: 100%;
    padding: 16px 20px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 12px;
    border: 2px solid var(--border-color);
    background: var(--background-elevated);
    color: var(--text-primary);
    transition: all 0.2s ease;
  }

  .mobile-search-modal input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.15);
  }

  .mobile-search-modal button {
    width: 100%;
    padding: 16px;
    font-size: 1.1em;
    font-weight: 600;
    border-radius: 12px;
    border: none;
    background: var(--primary-gradient);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-search-modal button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 188, 212, 0.3);
  }

  .return-arrow {
    left: 20px;
    top: 10px;
  }

  .hero-section {
    height: 60vh;
    min-height: 350px;
    padding: 20px 8px;
    text-align: center;
  }

  .hero-title {
    font-size: 2.8em;
    letter-spacing: -1px;
    margin-bottom: 16px;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 1.2em;
    margin-bottom: 24px;
    max-width: 95%;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
  }

  .hero-actions {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .hero-btn {
    min-width: auto;
    width: 85%;
    max-width: 300px;
    padding: 16px 24px;
    font-size: 1.1em;
    border-radius: 12px;
    font-weight: 600;
  }

  #search-list.grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    padding: 8px;
  }

  .grid-item {
    height: 160px;
    border-radius: 8px;
  }

  .grid-item div:last-child {
    padding: 8px;
    font-size: 0.8em;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    line-height: 1.3;
  }

  .season-episode {
    font-size: 0.7em;
    padding: 3px 6px;
    margin: 3px;
    border-radius: 4px;
  }

  .grid-item .rating-badge {
    top: 6px;
    right: 6px;
    font-size: 0.7em;
    padding: 3px 6px;
    border-radius: 4px;
  }

  /* Enhanced carousel for mobile */
  .trending-carousel-container {
    margin: 20px 0;
    padding: 0 8px;
  }

  .trending-carousel-container h3 {
    font-size: 1.6em;
    margin-bottom: 16px;
    font-weight: 700;
  }

  .carousel-items {
    padding-left: 50px;
    padding-right: 50px;
    gap: 12px;
    min-height: 180px;
  }

  .carousel-item {
    width: 120px;
    height: 160px;
    border-radius: 8px;
  }

  .carousel-item .title {
    padding: 8px;
    font-size: 0.8em;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    line-height: 1.3;
  }

  .carousel-item .season-episode {
    font-size: 0.7em;
    padding: 3px 5px;
    margin: 2px;
    border-radius: 3px;
  }

  .carousel-item .rating-badge {
    top: 6px;
    right: 6px;
    font-size: 0.7em;
    padding: 3px 6px;
    border-radius: 4px;
  }

  .carousel-item::after {
    font-size: 18px;
  }

  .carousel-nav {
    width: 45px;
    height: 45px;
    font-size: 1.2em;
    border-radius: 50%;
  }

  #media-info dl {
    grid-template-columns: 1fr;
    padding: var(--spacing-lg);
  }

  .banner {
    height: 200px;
  }

  #media-title {
    font-size: 1.5em;
    bottom: 10px;
    left: 10px;
  }

  .filter-controls {
    flex-wrap: wrap;
    justify-content: flex-start;
    margin: 0 8px 16px;
    padding: 12px;
    border-radius: 8px;
  }

  /* Media page improvements */
  .banner {
    height: 220px;
  }

  #media-title {
    font-size: 1.6em;
    bottom: 16px;
    left: 16px;
    line-height: 1.2;
  }

  .media-content {
    padding: 8px;
 }

  .filter-controls select,
  .filter-controls input {
    font-size: 1em;
    padding: 10px 12px;
    border-radius: 6px;
  }

  .filter-controls label {
    font-size: 1em;
    font-weight: 600;
  }

  #episodes.grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  #episodes .grid-item {
    height: 50px;
    font-size: 0.9em;
  }
}

/* Small Mobile Phones (480px and below) */
@media (max-width: 480px) {
  .hero-section {
    height: 55vh;
    min-height: 320px;
    padding: 16px 6px;
  }

  .hero-title {
    font-size: 2.4em;
  }

  .hero-description {
    font-size: 1.1em;
    margin-bottom: 20px;
  }

  .hero-btn {
    width: 90%;
    padding: 14px 20px;
    font-size: 1em;
  }

  #search-list.grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
    padding: 6px;
  }

  .grid-item {
    height: 133px;
  }

  .grid-item div:last-child {
    padding: 6px;
    font-size: 0.75em;
  }

  .trending-carousel-container {
    padding: 0 6px;
  }

  .trending-carousel-container h3 {
    font-size: 1.4em;
    margin-bottom: 12px;
  }

  .carousel-items {
    padding-left: 40px;
    padding-right: 40px;
    gap: 8px;
    min-height: 155px;
  }

  .carousel-item {
    width: 100px;
    height: 133px;
  }

  .carousel-item .title {
    padding: 6px;
    font-size: 0.75em;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
    font-size: 1.1em;
  }

  .search-bar {
    margin: 8px 6px;
    padding: 12px;
  }

  .search-bar input {
    padding: 12px 14px;
  }

  .search-bar button {
    padding: 12px;
    font-size: 1em;
  }
}

/* Large tablet screens (1280px and below) */
@media screen and (max-width: 1280px) {
  .sidebar {
    width: 60px;
    padding: 8px;
  }

  .sidebar a {
    padding: 8px;
    font-size: 0.9em;
    margin-bottom: 4px;
  }

  .sidebar a span {
    display: none; /* Hide text labels on smaller screens */
  }

  .content {
    margin-left: 60px;
    padding: 16px;
  }

  .search-bar {
    padding: 8px;
    margin: 8px;
  }

  .search-bar input {
    font-size: 0.95em;
    padding: 8px 12px;
  }

  .hero-section {
    height: 40vh;
    min-height: 250px;
    padding: 16px;
  }

  .hero-title {
    font-size: 2em;
    margin-bottom: var(--spacing-sm);
  }

  .hero-description {
    font-size: 0.9em;
    margin-bottom: var(--spacing-md);
  }

  .hero-btn {
    padding: 8px 16px;
    font-size: 0.9em;
  }

  #search-list.grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 6px;
    padding: var(--spacing-md);
  }

  .grid-item {
    height: 187px;
  }

  .grid-item div:last-child {
    padding: 6px;
    font-size: 0.7em;
  }

  .season-episode {
    font-size: 0.6em;
    padding: 2px 4px;
    margin: 1px;
  }

  .grid-item .rating-badge {
    top: 4px;
    right: 4px;
    font-size: 0.6em;
    padding: 1px 3px;
  }

  /* Enhanced carousel for mobile */
  .trending-carousel-container {
    margin: var(--spacing-md) 0;
    padding: 0 var(--spacing-md);
  }

  .trending-carousel-container h3 {
    font-size: 1.3em;
    margin-bottom: var(--spacing-xs);
  }

  .carousel-items {
    padding-left: 40px;
    padding-right: 40px;
    gap: 6px;
    min-height: 210px;
  }

  .carousel-item {
    width: 140px;
    height: 187px;
  }

  .carousel-item .title {
    padding: 6px;
    font-size: 0.7em;
  }

  .carousel-item .season-episode {
    font-size: 0.6em;
    padding: 1px 3px;
  }

  .carousel-item .rating-badge {
    top: 4px;
    right: 4px;
    font-size: 0.6em;
    padding: 1px 3px;
  }

  .carousel-item::after {
    font-size: 14px;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
  }

  .banner {
    height: 250px;
  }

  #media-title {
    font-size: 1.8em;
    bottom: 15px;
    left: 15px;
  }

  .media-content {
    padding: var(--spacing-md);
  }

  #media-info dl {
    grid-template-columns: 1fr;
    padding: var(--spacing-md);
    gap: 8px 15px;
  }

  .filter-controls {
    margin: 0 var(--spacing-md) var(--spacing-md);
    padding: var(--spacing-sm);
  }

  .filter-controls select,
  .filter-controls input {
    font-size: 0.8em;
    padding: 6px 8px;
  }
}

/* Tablet screens (1024px and below) */
@media screen and (max-width: 1024px) {
  .sidebar {
    width: 50px;
    padding: 6px;
  }

  .sidebar a {
    padding: 6px;
    font-size: 0.8em;
    margin-bottom: 2px;
  }

  .content {
    margin-left: 50px;
    padding: 12px;
  }

  .search-bar {
    padding: 6px;
    margin: 6px;
  }

  .search-bar input {
    font-size: 0.9em;
    padding: 6px 10px;
  }

  .hero-section {
    height: 35vh;
    min-height: 200px;
    padding: 12px;
  }

  .hero-title {
    font-size: 1.8em;
    margin-bottom: 8px;
  }

  .hero-description {
    font-size: 0.9em;
    margin-bottom: 12px;
  }

  .hero-btn {
    padding: 8px 16px;
    font-size: 0.9em;
  }

  #search-list.grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
    padding: 12px;
  }

  .grid-item {
    height: 187px;
  }

  .grid-item div:last-child {
    padding: 6px;
    font-size: 0.75em;
  }

  .carousel-items {
    padding-left: 40px;
    padding-right: 40px;
    gap: 8px;
    min-height: 210px;
  }

  .carousel-item {
    width: 140px;
    height: 187px;
  }

  .carousel-item .title {
    padding: 6px;
    font-size: 0.75em;
  }

  .carousel-nav {
    width: 35px;
    height: 35px;
    font-size: 0.9em;
  }

  .banner {
    height: 200px;
  }

  #media-title {
    font-size: 1.6em;
    bottom: 12px;
    left: 12px;
  }

  .media-content {
    padding: 12px;
  }

  #media-info dl {
    padding: 12px;
    gap: 8px 12px;
    font-size: 0.95em;
  }

  .filter-controls {
    margin: 0 12px 12px;
    padding: 8px;
  }

  .filter-controls select,
  .filter-controls input {
    font-size: 0.8em;
    padding: 6px 8px;
  }
}



/* Trending Carousel Styles */
.trending-carousel-container {
  margin: var(--spacing-lg) 0;
  padding: 0 var(--spacing-xl);
  position: relative;
}

.trending-carousel-container h3 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1.8em;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  position: relative;
  display: inline-block;
  padding-bottom: var(--spacing-xs);
  letter-spacing: -0.5px;
}

#movies-recently-watched-carousel h3,
#series-recently-watched-carousel h3,
#anime-recently-watched-carousel h3 {
  color: var(--text-primary);
}

#movies-recently-watched-carousel h3::after,
#series-recently-watched-carousel h3::after,
#anime-recently-watched-carousel h3::after {
  background-color: #ff7043; /* Different color for recently watched */
}

#movies-wishlist-carousel h3,
#series-wishlist-carousel h3,
#anime-wishlist-carousel h3 {
  color: var(--text-primary);
}

#movies-wishlist-carousel h3::after,
#series-wishlist-carousel h3::after,
#anime-wishlist-carousel h3::after {
  background-color: #8e24aa; /* Purple color for wish list */
}

.trending-carousel-container h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: 4px;
  box-shadow: var(--shadow-glow);
}

.carousel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.carousel-refresh-btn {
  background: var(--background-elevated);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
}

.carousel-refresh-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: rotate(180deg);
}

.carousel-refresh-btn:active {
  transform: rotate(180deg) scale(0.95);
}

.carousel-navigation {
  display: flex;
  align-items: center;
  position: relative;
  margin: 0 -10px;
}

.carousel-items {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding: var(--spacing-lg) 0;
  gap: 8px;
  flex-grow: 1;
  padding-left: 60px;
  padding-right: 60px;
  position: relative;
  min-height: 200px;
  scroll-snap-type: x mandatory;
}

.carousel-items::-webkit-scrollbar {
  display: none;
}

.carousel-nav {
  background: rgba(0, 0, 0, 0.8);
  color: var(--text-primary);
  border: 2px solid var(--border-color-light);
  border-radius: 50%;
  width: 52px;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: absolute;
  z-index: 5;
  box-shadow: var(--shadow-heavy);
  backdrop-filter: blur(20px);
}

.carousel-nav:hover {
  background: var(--primary-gradient);
  transform: scale(1.15);
  box-shadow: var(--shadow-heavy), var(--shadow-glow);
  border-color: var(--primary-color);
}

.carousel-nav:active {
  transform: scale(1.05);
}

.carousel-nav.prev {
  left: 10px;
}

.carousel-nav.next {
  right: 10px;
}

.carousel-nav i {
  transition: transform var(--transition-fast);
}

.carousel-nav.prev:hover i {
  transform: translateX(-2px);
}

.carousel-nav.next:hover i {
  transform: translateX(2px);
}

.carousel-item {
  flex: 0 0 auto;
  width: 180px;
  height: 240px;
  transition: all 0.25s ease-out;
  position: relative;
  cursor: pointer;
  display: block;
  z-index: 1;
  background-color: var(--background-card);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: none;
  overflow: hidden;
  scroll-snap-align: start;
  aspect-ratio: 3/4;
}

.carousel-item::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.8) 100%);
  opacity: 1;
  transition: all 0.25s ease-out;
  z-index: 2;
  pointer-events: none;
}

/* Netflix-style play button overlay for carousel */
.carousel-item::after {
  content: '▶';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  color: white;
  opacity: 0;
  transition: all 0.25s ease-out;
  z-index: 4;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  pointer-events: none;
}

.carousel-item:hover::before {
  background: linear-gradient(to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.9) 100%);
}

.carousel-item:hover::after {
  opacity: 0.9;
  transform: translate(-50%, -50%) scale(1.1);
}

.carousel-item:hover {
  transform: scale(1.05);
  z-index: 2;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Make sure all direct children are pointer-events:none */
.carousel-item > * {
  pointer-events: none;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  transition: all 0.25s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimizeQuality;
  -ms-interpolation-mode: bicubic;
}

.carousel-item:hover img {
  transform: scale(1.1);
}

.carousel-item .title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  font-size: 0.8em;
  font-weight: 700;
  text-align: left;
  z-index: 3;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 70%, rgba(0, 0, 0, 0) 100%);
  transition: all 0.25s ease-out;
  color: white;
  line-height: 1.2;
  font-family: 'Netflix Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.carousel-item:hover .title {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.1) 100%);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
}

/* Make sure season-episode class is styled consistently in carousel items */
.carousel-item .season-episode {
  display: inline-block;
  padding: 3px 6px;
  background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
  border: 1px solid rgba(0, 188, 212, 0.8);
  border-radius: 4px;
  font-size: 0.7em;
  font-weight: 600;
  color: white;
  margin: 2px 1px;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  box-shadow: 0 2px 6px rgba(0, 188, 212, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  backdrop-filter: blur(4px);
  text-transform: uppercase;
}

.carousel-item .rating-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.65em;
  font-weight: 600;
  z-index: 3;
  backdrop-filter: blur(4px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  font-family: 'Netflix Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.carousel-item .watched-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ff7043;
  padding: 3px 8px;
  border-radius: var(--border-radius-small);
  font-size: 0.8em;
  font-weight: 600;
  z-index: 2;
  backdrop-filter: blur(2px);
  border: 1px solid #ff7043;
  display: flex;
  align-items: center;
  gap: 5px;
}

.carousel-item .watched-indicator i {
  font-size: 0.9em;
}

.carousel-item .watched-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: #ff7043;
  z-index: 3;
}

.wishlist-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all var(--transition-fast);
  backdrop-filter: blur(2px);
  opacity: 0.7;
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: auto; /* Ensure wishlist buttons are clickable */
}

.grid-item:hover .wishlist-button,
.carousel-item:hover .wishlist-button {
  opacity: 1;
}

.wishlist-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.8);
}

.wishlist-button.active {
  color: #8e24aa;
  border-color: #8e24aa;
  background-color: rgba(142, 36, 170, 0.2);
  opacity: 1;
}

.wishlist-button.active:hover {
  background-color: rgba(142, 36, 170, 0.3);
}

.carousel-item .wishlist-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #8e24aa;
  padding: 3px 8px;
  border-radius: var(--border-radius-small);
  font-size: 0.8em;
  font-weight: 600;
  z-index: 2;
  backdrop-filter: blur(2px);
  border: 1px solid #8e24aa;
  display: flex;
  align-items: center;
  gap: 5px;
}

.carousel-item .wishlist-badge i {
  font-size: 0.9em;
}

/* Loading indicator for carousel */
.carousel-loading-item {
  flex: 0 0 auto;
  width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: default;
  background-color: var(--background-card);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-medium);
  scroll-snap-align: start;
}

.carousel-loading-item:hover {
  transform: none;
  box-shadow: var(--shadow-medium);
  border-color: var(--border-color);
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 220px;
  background: var(--background-elevated);
  border-radius: var(--border-radius-medium);
  border: 2px dashed var(--border-color);
  width: 100%;
  margin: 8px;
}

.loading-placeholder i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 500;
}

/* No more items indicator for carousel */
.carousel-no-more-item {
  flex: 0 0 auto;
  width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: default;
  background-color: var(--background-card);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-medium);
  scroll-snap-align: start;
}

.carousel-no-more-item:hover {
  transform: none;
  box-shadow: var(--shadow-medium);
  border-color: var(--border-color);
}

.no-more-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 220px;
  background: var(--background-elevated);
  border-radius: var(--border-radius-medium);
  border: 2px dashed var(--primary-color);
  width: 100%;
  margin: 8px;
  padding: 20px;
}

.no-more-placeholder i {
  font-size: 2rem;
  color: #4caf50;
  margin-bottom: 0.5rem;
}

.no-more-text {
  font-size: 0.9rem;
  color: var(--text-primary);
  text-align: center;
  font-weight: 600;
  margin-bottom: 1rem;
}

.refresh-carousel-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
}

.refresh-carousel-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.refresh-carousel-btn i {
  font-size: 0.9rem;
}

/* Genre carousel specific styles */
.genre-carousel {
  margin-bottom: var(--spacing-md);
}

.genre-carousel h3 {
  font-size: 1.4rem;
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  position: relative;
  padding-bottom: 6px;
}

.genre-carousel h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

/* Genre-specific colors for different content types */
#movies-genre-carousels .genre-carousel h3::after {
  background-color: #2196f3; /* Blue for movies */
}

#series-genre-carousels .genre-carousel h3::after {
  background-color: #4caf50; /* Green for series */
}

#anime-genre-carousels .genre-carousel h3::after {
  background-color: #ff9800; /* Orange for anime */
}

/* Responsive adjustments for genre carousels */
@media (max-width: 768px) {
  .genre-carousel h3 {
    font-size: 1.1rem;
  }

  .genre-carousel .carousel-refresh-btn {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}

/* Admin Display Settings Styles */
.display-settings-form {
  max-width: 600px;
}

.toggle-group {
  margin-bottom: var(--spacing-md);
}

.toggle-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--card-bg);
  transition: all 0.3s ease;
}

.toggle-label:hover {
  border-color: var(--primary-color);
  background-color: var(--hover-bg);
}

.toggle-text {
  flex: 1;
  margin-right: var(--spacing-md);
}

.toggle-text strong {
  display: block;
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.toggle-text small {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.toggle-switch {
  position: relative;
  width: 60px;
  height: 30px;
  flex-shrink: 0;
}

.toggle-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 30px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(30px);
}

.toggle-description {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
}

.toggle-description p {
  margin: 4px 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.grid-status {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: 6px;
  text-align: center;
}

.status-indicator {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-indicator.active {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-indicator.inactive {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Admin hidden state for grid containers */
.admin-hidden {
  display: none !important;
}

/* Responsive adjustments for display settings */
@media (max-width: 768px) {
  .toggle-label {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .toggle-text {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }

  .toggle-switch {
    align-self: flex-end;
  }
}

@media (min-width: 769px) {
  .mobile-search-modal {
    display: none !important;
  }
}