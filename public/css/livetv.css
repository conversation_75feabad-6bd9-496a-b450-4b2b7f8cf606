/* LiveTV Styles - TV-like Layout with Overlay Channel Selector */

/* Override section padding for LiveTV - Full Main Content */
#livetv.section {
  position: relative;
  padding: 0;
  height: 100%;
  overflow: hidden;
  outline: none; /* Remove focus outline */
}

/* Hide main search bar and sidebar in LiveTV section */
.livetv-active .search-bar {
  display: none !important;
}

.livetv-active .sidebar {
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 2000;
}

/* Show sidebar on hover or when triggered */
.livetv-active .sidebar:hover,
.livetv-active .sidebar.show {
  transform: translateX(0);
}

/* Allow sidebar mouse interaction but prevent keyboard focus when locked */
.livetv-focus-locked .sidebar a,
.livetv-focus-locked .sidebar button,
.livetv-focus-locked .sidebar [tabindex] {
  outline: none;
}

.livetv-focus-locked .sidebar a:focus,
.livetv-focus-locked .sidebar button:focus,
.livetv-focus-locked .sidebar [tabindex]:focus {
  outline: none;
  box-shadow: none;
}

/* Sidebar hover trigger area */
.livetv-sidebar-trigger {
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 100vh;
  z-index: 1500;
  background: transparent;
}

/* Activation Hint */
.livetv-activation-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.livetv-activation-hint.show {
  opacity: 1;
}

.hint-content {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid var(--primary-color);
  border-radius: 15px;
  padding: 20px 30px;
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
  font-size: 1.2em;
  font-weight: 500;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hint-content i {
  color: var(--primary-color);
  font-size: 1.5em;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

.livetv-active .livetv-sidebar-trigger:hover + .sidebar,
.livetv-active .livetv-sidebar-trigger:hover ~ * .sidebar {
  transform: translateX(0);
}

/* Ensure LiveTV section takes full screen when sidebar and search bar are hidden */
.livetv-active .content {
  padding: 0 !important;
  margin-left: 0 !important;
}

.livetv-active #livetv.section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
}

/* Full Screen TV Player Container */
.livetv-player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== TV PLAYER ELEMENTS ===== */
/* TV Logo */
.tv-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.2);
  font-size: 3em;
  font-weight: bold;
  z-index: 1;
  pointer-events: none;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Player Elements */
#livetv-player-video,
#livetv-player-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  background: #000;
  z-index: 2;
}

#livetv-player-video {
  object-fit: contain;
}

#livetv-player-iframe {
  display: none;
}

/* Simple TV Controls */
.tv-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  gap: 20px;
  align-items: center;
}

.livetv-player-container:hover .tv-controls,
.tv-controls.show {
  opacity: 1;
}

.tv-control-btn {
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: white;
  font-size: 1.4em;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tv-control-btn:hover,
.tv-control-btn.focused {
  background: var(--primary-color);
  border-color: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(var(--primary-color-rgb), 0.5);
}

.tv-control-btn.focused {
  border-color: white;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* ===== CHANNEL SELECTOR OVERLAY (RIGHT SIDE) ===== */
.livetv-channel-selector {
  position: absolute;
  top: 0;
  right: 0;
  width: 350px;
  height: 100%;
  background: transparent;
  z-index: 10;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.4s ease;
}

.livetv-channel-selector.show {
  transform: translateX(0);
}

/* Selector Header */
.selector-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.favorites-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1em;
  transition: all 0.3s ease;
}

.favorites-indicator i:first-child {
  color: #ffd700;
}

.favorites-indicator i:last-child {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9em;
}

/* Channel Cards Container */
.channel-cards-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 20px;
}

/* Channel Cards */
.channel-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px 20px;
  text-align: center;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  width: 100%;
  max-width: 280px;
  cursor: pointer;
  position: relative;
}

.channel-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.channel-card.current {
  background: rgba(var(--primary-color-rgb), 0.2);
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.channel-card.prev-1,
.channel-card.next-1 {
  opacity: 0.8;
  transform: scale(0.95);
}

.channel-card.prev-2,
.channel-card.next-2 {
  opacity: 0.5;
  transform: scale(0.9);
}

.channel-card .channel-number {
  font-size: 1.5em;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.channel-card.current .channel-number {
  font-size: 1.8em;
  color: white;
}

.channel-card .channel-name {
  font-size: 1em;
  color: white;
  font-weight: 500;
  margin-bottom: 3px;
  line-height: 1.2;
}

.channel-card.current .channel-name {
  font-size: 1.1em;
  font-weight: 600;
}

.channel-card .channel-category {
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* Favorite Icon */
.channel-card .favorite-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.channel-card .favorite-icon:hover {
  color: #ffd700;
  transform: scale(1.2);
}

.channel-card .favorite-icon.active {
  color: #ffd700;
}

.channel-card.current .favorite-icon {
  color: rgba(255, 255, 255, 0.6);
}

.channel-card.current .favorite-icon.active {
  color: #ffd700;
}



/* Favorites List (Second Panel) - Same Style as Main Selector */
.livetv-favorites-list {
  position: absolute;
  top: 0;
  right: 0;
  width: 350px;
  height: 100%;
  background: transparent;
  z-index: 11;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.4s ease;
}

.livetv-favorites-list.show {
  transform: translateX(0);
}

.favorites-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.favorites-header h3 {
  color: white;
  margin: 0;
  font-size: 1.3em;
  font-weight: 600;
}

.favorites-back-btn {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9em;
}

.favorites-back-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Favorites Channel Cards Container - Same as Main Selector */
.favorites-cards-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 15px;
  padding: 20px;
}



/* Hide the show selector button - not needed anymore */
.livetv-show-selector-btn {
  display: none !important;
}



/* Responsive Design */
@media (max-width: 768px) {
  .livetv-channel-selector {
    width: 100%;
    right: -100%;
  }

  .channel-display {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .channel-display .channel-number {
    font-size: 1.5em !important;
  }
}

/* End of LiveTV Styles */
