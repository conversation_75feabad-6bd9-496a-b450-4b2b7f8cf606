/* Modern Video Player Styles */
:root {
  --player-primary: #00bcd4;
  --player-primary-dark: #0097a7;
  --player-primary-light: #4dd0e1;
  --player-background: rgba(0, 0, 0, 0.95);
  --player-controls-bg: rgba(0, 0, 0, 0.7);
  --player-text: #ffffff;
  --player-text-secondary: rgba(255, 255, 255, 0.7);
  --player-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  --player-transition: 0.3s ease;
  --player-border-radius: 8px;
}

/* Player Container */
#player-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--player-background);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all var(--player-transition);
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

#player-container.hidden {
  display: none;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

#player-container:not(.hidden) {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

/* Fullscreen styles */
#player-wrapper.fullscreen-mode {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  border-radius: 0 !important;
}

#player-wrapper.fullscreen-mode #player,
#player-wrapper.fullscreen-mode #player-iframe {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
}

/* Hide logo in fullscreen mode */
#player-wrapper.fullscreen-mode #player-logo {
  display: none !important;
}

/* Fullscreen fallback for browsers that don't support the Fullscreen API */
#player-container.fullscreen-fallback {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: #000 !important;
}

#player-container.fullscreen-fallback #player-wrapper {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  border-radius: 0 !important;
}

#player-container.fullscreen-fallback #player,
#player-container.fullscreen-fallback #player-iframe {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
}

/* Hide logo in fullscreen fallback mode */
#player-container.fullscreen-fallback #player-logo {
  display: none !important;
}

#player-container:not(.hidden) #player-controls {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Video Player */
#player-wrapper {
  position: relative;
  width: 90%;
  height: 90%;
  max-width: 1280px;
  max-height: 720px;
  border-radius: var(--player-border-radius);
  box-shadow: var(--player-shadow);
  background: #000;
  overflow: hidden;
  transition: all var(--player-transition);
  transform: scale(0.95);
  opacity: 0;
}

#player-container:not(.hidden) #player-wrapper {
  transform: scale(1) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

#player {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
  display: block;
}

#player-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

/* Player Controls */
#player-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px 20px;
  background: linear-gradient(to top, var(--player-controls-bg) 70%, rgba(0,0,0,0));
  display: flex;
  flex-direction: column;
  opacity: 1; /* Start visible */
  transition: opacity var(--player-transition), visibility var(--player-transition);
  z-index: 2;
  box-sizing: border-box;
}

#player-wrapper:hover #player-controls,
#player-controls.active {
  opacity: 1;
  visibility: visible;
}

/* Hide controls in fullscreen mode by default */
#player-wrapper.fullscreen-mode #player-controls {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity var(--player-transition), visibility var(--player-transition) !important;
}

/* Show controls on hover in fullscreen mode */
#player-wrapper.fullscreen-mode:hover #player-controls,
#player-wrapper.fullscreen-mode #player-controls.active {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
}

/* Hide cursor in fullscreen mode ONLY when controls are hidden */
#player-wrapper.fullscreen-mode.cursor-hidden {
  cursor: none !important;
}

/* Force cursor hiding in fullscreen mode ONLY when controls are hidden */
#player-wrapper.fullscreen-mode.cursor-hidden * {
  cursor: none !important;
}

/* Always show cursor when hovering over controls in any mode */
#player-controls,
#player-controls *,
#player-logo,
#player-settings-menu,
#player-settings-menu *,
#player-subtitles-menu,
#player-subtitles-menu * {
  cursor: auto !important;
}

/* Ensure buttons have pointer cursor */
.player-button,
.player-settings-option,
.subtitle-action-btn,
button {
  cursor: pointer !important;
}

/* Always show cursor in non-fullscreen mode */
#player-wrapper:not(.fullscreen-mode),
#player-wrapper:not(.fullscreen-mode) * {
  cursor: auto !important;
}

/* Ensure buttons have pointer cursor in non-fullscreen mode */
#player-wrapper:not(.fullscreen-mode) .player-button,
#player-wrapper:not(.fullscreen-mode) button {
  cursor: pointer !important;
}

/* Ensure controls stay hidden in fullscreen mode */
#player-wrapper.fullscreen-mode #player-controls:not(.active):not(:hover) {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity var(--player-transition), visibility var(--player-transition) !important;
}

/* Ensure logo stays hidden in fullscreen mode */
#player-wrapper.fullscreen-mode #player-logo:not(:hover) {
  opacity: 0 !important;
  visibility: hidden !important;
  display: none !important;
}

/* Title Bar */
#player-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 5px 0;
  width: 100%;
}

#player-title {
  color: var(--player-text);
  font-size: 1.2em;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
  font-family: 'Poppins', sans-serif;
}

/* Progress Bar */
#player-progress-container {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  margin-bottom: 15px;
  overflow: hidden;
}

#player-progress-container:hover {
  height: 8px;
}

#player-progress-bar {
  height: 100%;
  background: var(--player-primary);
  width: 0;
  border-radius: 5px;
  position: relative;
  transition: width 0.1s linear;
}

#player-progress-buffer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  width: 0;
  border-radius: 5px;
}

#player-time-tooltip {
  position: absolute;
  bottom: 15px;
  background: var(--player-controls-bg);
  color: var(--player-text);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  transform: translateX(-50%);
  display: none;
}

/* Control Buttons */
#player-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 5px;
}

.player-button-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.player-button {
  background: transparent;
  border: none;
  color: var(--player-text);
  font-size: 1.2em;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--player-transition);
  padding: 0;
  margin: 0;
}

.player-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--player-primary);
}

.player-button.active {
  color: var(--player-primary);
}

/* Special styling for CC button when active */
#player-cc.active {
  color: var(--player-primary);
  position: relative;
}

#player-cc.active::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: var(--player-primary);
  border-radius: 50%;
}

/* Quality change indicator */
#quality-change-indicator {
  position: absolute;
  top: 10%;
  right: 10%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: bold;
  z-index: 5;
  opacity: 0;
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-button i {
  font-size: 1.2em;
  width: 1.2em;
  height: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.player-button-text {
  font-size: 0.7em;
  position: absolute;
  bottom: 5px;
  right: 5px;
  font-weight: bold;
}

/* Specific styling for rewind and forward buttons */
#player-rewind, #player-forward {
  position: relative;
}

/* Subtitles styling */
.player-subtitles {
  position: absolute;
  bottom: 70px;
  left: 0;
  width: 100%;
  text-align: center;
  z-index: 3;
  pointer-events: none;
  transition: bottom 0.3s ease;
}

.player-subtitles.hidden {
  display: none;
}

.player-subtitles p {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  margin: 0 auto;
  border-radius: 3px;
  max-width: 80%;
  font-family: 'Arial', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.3;
  letter-spacing: 0.1px;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: none;
}

/* Adjust subtitle position when controls are hidden */
#player-wrapper:not(:hover) .player-subtitles {
  bottom: 40px;
}

/* Subtitle menu styling */
#player-subtitles-menu {
  position: absolute;
  right: 10px;
  bottom: 60px;
  background-color: rgba(28, 28, 28, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 6px;
  padding: 10px;
  z-index: 5;
  display: none;
  width: 240px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

#player-subtitles-menu.active {
  display: block;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.player-settings-section {
  margin-bottom: 8px;
}

.player-settings-header {
  font-size: 14px;
  font-weight: bold;
  color: white;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.player-settings-options {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.player-settings-option {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 3px;
  color: white;
  padding: 6px 8px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 13px;
}

.player-settings-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.player-settings-option.active {
  background-color: var(--player-primary);
  color: black;
  font-weight: bold;
}

.player-subtitle-upload {
  margin-top: 8px;
  padding: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.player-subtitle-upload label {
  display: block;
  margin-bottom: 4px;
  cursor: pointer;
  color: var(--player-primary);
  font-weight: bold;
  text-align: center;
  transition: color 0.2s;
  font-size: 12px;
}

.player-subtitle-upload label:hover {
  color: white;
}

.player-subtitle-upload input[type="file"] {
  width: 100%;
  margin-top: 4px;
  color: white;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  padding: 4px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  cursor: pointer;
  font-size: 11px;
}

.player-subtitle-upload input[type="file"]::-webkit-file-upload-button {
  background-color: var(--player-primary);
  color: black;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  margin-right: 6px;
  font-weight: bold;
  font-size: 11px;
}

/* Subtitle customization controls */
.subtitle-customization {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.subtitle-customization-label {
  font-size: 12px;
  color: white;
  margin-bottom: 2px;
}

.subtitle-size-controls, .subtitle-opacity-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.subtitle-control-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 3px;
  color: white;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.subtitle-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.subtitle-value {
  font-size: 12px;
  color: white;
}

/* Volume Control */
#player-volume-container {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

#player-volume-slider {
  width: 0;
  height: 5px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  cursor: pointer;
  overflow: hidden;
  transition: width var(--player-transition);
}

#player-volume-container:hover #player-volume-slider {
  width: 80px;
}

#player-volume-level {
  height: 100%;
  background: var(--player-primary);
  width: 100%;
  border-radius: 5px;
}

/* Time Display */
#player-time-display {
  color: var(--player-text);
  font-size: 0.9em;
  margin: 0 15px;
  white-space: nowrap;
  font-family: 'Roboto', sans-serif;
  min-width: 100px;
  text-align: center;
}

/* Close Button */
#close-player {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--player-controls-bg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--player-text);
  font-size: 1.5em;
  cursor: pointer;
  z-index: 1001;
  border-radius: 50%;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--player-transition);
  box-shadow: var(--player-shadow);
  transform: scale(0.9);
  opacity: 0.8;
}

#player-container:not(.hidden) #close-player {
  transform: scale(1);
  opacity: 1;
}

#close-player:hover {
  background-color: var(--player-primary);
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.5);
}

/* Settings Menu */
#player-settings-menu {
  position: absolute !important;
  bottom: 70px !important;
  right: 20px !important;
  background: rgba(28, 28, 28, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-radius: var(--player-border-radius) !important;
  padding: 12px !important;
  display: none !important;
  flex-direction: column !important;
  gap: 10px !important;
  min-width: 200px !important;
  box-shadow: var(--player-shadow) !important;
  z-index: 3 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  margin: 0 !important;
  width: auto !important;
  height: auto !important;
}

#player-settings-menu.active {
  display: flex !important;
}

.player-settings-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  color: var(--player-text) !important;
  padding: 10px 12px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-family: 'Roboto', sans-serif !important;
  font-size: 0.95em !important;
  margin: 0 !important;
  width: 100% !important;
  height: auto !important;
  text-align: left !important;
}

.player-settings-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.player-settings-item.active {
  color: var(--player-primary) !important;
}

.player-settings-item span {
  display: inline-block !important;
  margin: 0 !important;
  padding: 0 !important;
}

.player-settings-item span:last-child {
  font-weight: 500 !important;
  color: var(--player-primary-light) !important;
  transition: color 0.3s ease, transform 0.3s ease !important;
}

/* Quality value changing animation */
#player-quality-value.changing {
  color: #ffcc00 !important;
  animation: pulse 1s ease !important;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Branding */
#player-logo {
  position: absolute !important;
  top: 20px !important;
  left: 20px !important;
  color: var(--player-primary) !important;
  font-size: 1.5em !important;
  font-weight: 700 !important;
  font-family: 'Poppins', sans-serif !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
  opacity: 0.8 !important;
  transition: opacity var(--player-transition), visibility var(--player-transition) !important;
  z-index: 2 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  padding: 5px 10px !important;
  border-radius: 4px !important;
  display: block !important;
  width: auto !important;
  height: auto !important;
}

#player-wrapper:hover #player-logo {
  opacity: 0.4 !important;
}

/* Link logo visibility to controls visibility */
#player-wrapper.fullscreen-mode #player-logo {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity var(--player-transition), visibility var(--player-transition) !important;
  display: none !important;
  pointer-events: none !important;
}

/* Only show logo on explicit hover in fullscreen mode */
#player-wrapper.fullscreen-mode:hover #player-logo {
  opacity: 0.8 !important;
  visibility: visible !important;
  display: block !important;
  pointer-events: auto !important;
}

/* Hide logo when cursor is hidden */
#player-wrapper.fullscreen-mode.cursor-hidden #player-logo {
  opacity: 0 !important;
  visibility: hidden !important;
  display: none !important;
  pointer-events: none !important;
}

/* Additional styles for better appearance */
#player-container * {
  box-sizing: border-box;
}

/* Force proper layout for all player elements */
#player-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

#player-wrapper {
  position: relative !important;
  width: 90% !important;
  height: 90% !important;
  max-width: 1280px !important;
  max-height: 720px !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  background: #000 !important;
}

#player {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background: #000 !important;
}

#player-controls {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  padding: 20px 20px !important;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 70%, rgba(0,0,0,0)) !important;
  display: flex !important;
  flex-direction: column !important;
  z-index: 2 !important;
}

#player-title-bar {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 15px !important;
  width: 100% !important;
}

#player-title {
  color: #ffffff !important;
  font-size: 1.2em !important;
  font-weight: 500 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 80% !important;
  font-family: 'Poppins', sans-serif !important;
}

#player-progress-container {
  width: 100% !important;
  height: 6px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 5px !important;
  cursor: pointer !important;
  position: relative !important;
  margin-bottom: 15px !important;
  overflow: hidden !important;
}

#player-progress-bar {
  height: 100% !important;
  background: #00bcd4 !important;
  width: 0 !important;
  border-radius: 5px !important;
  position: relative !important;
}

#player-buttons {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
  margin-top: 5px !important;
}

.player-button-group {
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
}

.player-button {
  background: transparent !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 1.2em !important;
  cursor: pointer !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  padding: 0 !important;
  margin: 0 !important;
}

#player-time-display {
  color: #ffffff !important;
  font-size: 0.9em !important;
  margin: 0 15px !important;
  white-space: nowrap !important;
  font-family: 'Roboto', sans-serif !important;
  min-width: 100px !important;
  text-align: center !important;
}

/* Make sure Font Awesome icons are properly sized */
.fas {
  width: 1em !important;
  height: 1em !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #player-time-display {
    min-width: 80px;
    font-size: 0.8em;
  }

  .player-button-group {
    gap: 10px;
  }

  .player-button {
    width: 36px;
    height: 36px;
  }

  #player-title {
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  #player-controls {
    padding: 15px 10px;
  }

  #player-time-display {
    min-width: 70px;
    font-size: 0.75em;
    margin: 0 5px;
  }

  .player-button-group {
    gap: 5px;
  }

  .player-button {
    width: 32px;
    height: 32px;
  }
}
