/* Remote Control Navigation Styles */

/* General focus styles */
.remote-navigation .remote-focus {
  outline: 3px solid var(--primary-color) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 10px rgba(0, 188, 212, 0.7) !important;
  position: relative;
  z-index: 10;
}

/* Sidebar focus styles */
.remote-navigation .sidebar a.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: translateX(5px);
}

/* Button focus styles */
.remote-navigation button.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.05);
}

/* Input focus styles */
.remote-navigation input.remote-focus {
  border-color: var(--primary-color) !important;
  background-color: rgba(0, 188, 212, 0.1) !important;
}

/* Grid item focus styles */
.remote-navigation .grid-item.remote-focus {
  transform: scale(1.1) !important;
  z-index: 10;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.7) !important;
  position: relative;
}

.remote-navigation .grid-item.remote-focus::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid var(--primary-color);
  border-radius: inherit;
  animation: pulse 1.5s infinite;
  pointer-events: none;
}

/* Carousel item focus styles */
.remote-navigation .carousel-item.remote-focus {
  transform: scale(1.1) !important;
  z-index: 10;
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.7) !important;
  border: 2px solid var(--primary-color) !important;
  outline: none !important;
}

/* Category item focus styles */
.remote-navigation .category-item.remote-focus {
  transform: translateY(-5px) !important;
  background-color: var(--primary-color) !important;
  color: white !important;
}

.remote-navigation .category-item.remote-focus i,
.remote-navigation .category-item.remote-focus span {
  color: white !important;
}

/* Player focus styles */
.remote-navigation #player.remote-focus {
  outline: 3px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

/* Ensure focus is visible */
.remote-navigation *:focus {
  outline: none !important;
}

/* Ensure scrolling is smooth */
.remote-navigation {
  scroll-behavior: smooth;
}

/* Ensure clickable elements have proper cursor */
.remote-navigation .grid-item,
.remote-navigation .carousel-item,
.remote-navigation .category-item,
.remote-navigation button,
.remote-navigation a,
.remote-navigation select {
  cursor: pointer;
}

/* Ensure focus indicators are visible on top of other elements */
.remote-navigation .remote-focus::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: inherit;
  pointer-events: none;
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(0, 188, 212, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 188, 212, 0);
  }
}

/* Ensure proper focus for player controls */
.remote-navigation .player-controls button.remote-focus {
  transform: scale(1.2);
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Ensure proper focus for close player button */
.remote-navigation #close-player.remote-focus {
  transform: scale(1.2) !important;
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Ensure proper focus for search input */
.remote-navigation #search-input.remote-focus,
.remote-navigation #livetv-search-input.remote-focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.3) !important;
}

/* Ensure proper focus for search button */
.remote-navigation #search-button.remote-focus,
.remote-navigation #livetv-search-button.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.05);
}

/* Ensure proper focus for sort select */
.remote-navigation select.remote-focus {
  border-color: var(--primary-color) !important;
  background-color: rgba(0, 188, 212, 0.1) !important;
}

/* Ensure proper focus for carousel navigation buttons */
.remote-navigation .carousel-nav.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.2);
}

/* Ensure proper focus for carousel refresh buttons */
.remote-navigation .carousel-refresh-btn.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.1);
  border-radius: 50%;
}

/* Ensure proper focus for hero buttons */
.remote-navigation .hero-btn.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.7) !important;
}

/* Ensure proper focus for filter controls */
.remote-navigation .filter-controls.remote-focus,
.remote-navigation .filter-controls select.remote-focus {
  border-color: var(--primary-color) !important;
  background-color: rgba(0, 188, 212, 0.1) !important;
  box-shadow: 0 0 5px rgba(0, 188, 212, 0.5) !important;
}

/* Ensure proper focus for LiveTV channel cards */
.remote-navigation .channel-card.remote-focus {
  transform: translateY(-5px) !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
}

.remote-navigation .channel-card.remote-focus::before {
  height: 100% !important;
}

/* Ensure proper focus for LiveTV favorite button */
.remote-navigation .favorite-button.remote-focus {
  transform: scale(1.2) !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  border-color: var(--primary-color) !important;
}

.remote-navigation .favorite-button.remote-focus i {
  color: var(--primary-color) !important;
}

/* Ensure proper focus for LiveTV play button */
.remote-navigation .channel-card.remote-focus .play-button {
  opacity: 1 !important;
  transform: translate(-50%, -50%) scale(1.1) !important;
  background-color: var(--primary-color) !important;
}

/* Ensure proper focus for player video element */
.remote-navigation video.remote-focus {
  outline: 3px solid var(--primary-color) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.7) !important;
}

/* Ensure proper focus for player iframe element */
.remote-navigation iframe.remote-focus {
  outline: 3px solid var(--primary-color) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.7) !important;
}

/* Media page specific styles */
.remote-navigation #return-arrow.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.2) !important;
  border-radius: 50% !important;
  box-shadow: 0 0 10px rgba(0, 188, 212, 0.7) !important;
}

.remote-navigation #media-info.remote-focus {
  outline: 3px solid var(--primary-color) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 10px rgba(0, 188, 212, 0.7) !important;
  background-color: rgba(0, 188, 212, 0.1) !important;
}

.remote-navigation #media-info.remote-focus dt {
  color: var(--primary-color) !important;
}

.remote-navigation #seasons .grid-item.remote-focus,
.remote-navigation #episodes .grid-item.remote-focus,
.remote-navigation #providers .grid-item.remote-focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.05) !important;
  box-shadow: 0 0 10px rgba(0, 188, 212, 0.7) !important;
  border-color: white !important;
}

.remote-navigation #providers .grid-item.remote-focus button {
  background-color: white !important;
  color: var(--primary-color) !important;
  font-weight: bold !important;
}

/* Make sure the media page sections have proper focus indicators */
.remote-navigation #seasons,
.remote-navigation #episodes,
.remote-navigation #providers {
  position: relative;
}

/* Add a subtle indicator to show which section is active */
.remote-navigation #seasons:has(.remote-focus),
.remote-navigation #episodes:has(.remote-focus),
.remote-navigation #providers:has(.remote-focus) {
  background-color: rgba(0, 188, 212, 0.05) !important;
  border-left: 3px solid var(--primary-color) !important;
  padding-left: 10px !important;
}
