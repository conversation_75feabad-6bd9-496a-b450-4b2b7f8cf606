/* TMDB Seasons Section Styles */
.tmdb-seasons-section {
  margin-bottom: 20px;
}

.tmdb-seasons-container {
  padding: 10px;
}

.tmdb-seasons-select-container {
  margin-bottom: 15px;
}

.tmdb-seasons-select-container label {
  margin-right: 10px;
  font-weight: 500;
}

#tmdb-season-select {
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: #fff;
}

.tmdb-season-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tmdb-season-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
}

.tmdb-season-info > div {
  margin-bottom: 10px;
}

.tmdb-data {
  margin: 0;
  padding: 0;
}

dt.tmdb-data {
  font-weight: 500;
  margin-bottom: 5px;
  color: #00bcd4;
}

dd.tmdb-data {
  margin-left: 0;
  margin-bottom: 10px;
}

.tmdb-season-episodes h4 {
  margin-bottom: 15px;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.tmdb-episodes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.tmdb-episode-card {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.tmdb-episode-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.tmdb-episode-card h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1em;
  color: #00bcd4;
}

.tmdb-episode-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9em;
  color: #aaa;
}

.tmdb-episode-overview {
  margin-top: 10px;
}

.tmdb-episode-overview p {
  margin-bottom: 10px;
  font-size: 0.9em;
  line-height: 1.4;
}

.play-episode-btn {
  background-color: #00bcd4;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

.play-episode-btn:hover {
  background-color: #00a0b7;
}

.no-streams {
  color: #888;
  font-style: italic;
  font-size: 0.9em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tmdb-episodes-list {
    grid-template-columns: 1fr;
  }

  .tmdb-season-info {
    grid-template-columns: 1fr;
  }
}
