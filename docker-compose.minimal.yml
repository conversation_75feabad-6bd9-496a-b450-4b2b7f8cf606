version: '3.8'

# Minimal Docker Compose - Uses your existing MongoDB Atlas and minimal local services
# Use this if you only want to containerize the frontend and backend

services:
  # Backend GraphQL API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: netstream-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - DOCKER=true
    volumes:
      - ./logs:/app/logs
      - ./provider_url_output:/app/provider_url_output
      - backend_cache:/app/.cache
    networks:
      - netstream-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/graphql?query={__typename}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M

  # Frontend Next.js Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: netstream-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      - API_URL=http://backend:3001/graphql
      - NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
      - NODE_ENV=production
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - netstream-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 256M

volumes:
  backend_cache:
    driver: local

networks:
  netstream-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
