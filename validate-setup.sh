#!/bin/bash

# NetStream Docker Setup Validation Script
# This script validates the Docker setup configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation counters
ERRORS=0
WARNINGS=0

# Function to check file exists
check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        print_success "$description exists: $file"
    else
        print_error "$description missing: $file"
        ((ERRORS++))
    fi
}

# Function to check directory exists
check_directory() {
    local dir=$1
    local description=$2
    
    if [ -d "$dir" ]; then
        print_success "$description exists: $dir"
    else
        print_warning "$description missing: $dir (will be created during deployment)"
        ((WARNINGS++))
    fi
}

# Function to validate docker-compose.yml
validate_compose() {
    print_status "Validating docker-compose.yml..."
    
    if [ ! -f "docker-compose.yml" ]; then
        print_error "docker-compose.yml not found"
        ((ERRORS++))
        return
    fi
    
    # Check for required services
    local services=("mongo" "redis" "backend" "frontend")
    for service in "${services[@]}"; do
        if grep -q "^  $service:" docker-compose.yml; then
            print_success "Service '$service' defined"
        else
            print_error "Service '$service' not found in docker-compose.yml"
            ((ERRORS++))
        fi
    done
    
    # Check for volumes
    if grep -q "^volumes:" docker-compose.yml; then
        print_success "Volumes section defined"
    else
        print_error "Volumes section not found"
        ((ERRORS++))
    fi
    
    # Check for networks
    if grep -q "^networks:" docker-compose.yml; then
        print_success "Networks section defined"
    else
        print_error "Networks section not found"
        ((ERRORS++))
    fi
}

# Function to validate Dockerfiles
validate_dockerfiles() {
    print_status "Validating Dockerfiles..."
    
    # Check backend Dockerfile
    if [ -f "Dockerfile.backend" ]; then
        print_success "Backend Dockerfile exists"
        
        # Check for multi-stage build
        if grep -q "FROM.*AS" Dockerfile.backend; then
            print_success "Backend uses multi-stage build"
        else
            print_warning "Backend Dockerfile doesn't use multi-stage build"
            ((WARNINGS++))
        fi
        
        # Check for health check
        if grep -q "HEALTHCHECK" Dockerfile.backend; then
            print_success "Backend has health check"
        else
            print_warning "Backend Dockerfile missing health check"
            ((WARNINGS++))
        fi
    else
        print_error "Dockerfile.backend not found"
        ((ERRORS++))
    fi
    
    # Check frontend Dockerfile
    if [ -f "Dockerfile.frontend" ]; then
        print_success "Frontend Dockerfile exists"
        
        # Check for multi-stage build
        if grep -q "FROM.*AS" Dockerfile.frontend; then
            print_success "Frontend uses multi-stage build"
        else
            print_warning "Frontend Dockerfile doesn't use multi-stage build"
            ((WARNINGS++))
        fi
    else
        print_error "Dockerfile.frontend not found"
        ((ERRORS++))
    fi
}

# Function to validate environment files
validate_env_files() {
    print_status "Validating environment files..."
    
    check_file ".env.example" "Environment example file"
    check_file ".env.ubuntu" "Ubuntu environment template"
    
    if [ -f ".env" ]; then
        print_success ".env file exists"
        
        # Check for required variables
        local required_vars=("MONGO_URI" "REDIS_URL" "TMDB_API_KEY" "ADMIN_KEY")
        for var in "${required_vars[@]}"; do
            if grep -q "^$var=" .env; then
                print_success "Required variable '$var' found in .env"
            else
                print_error "Required variable '$var' missing from .env"
                ((ERRORS++))
            fi
        done
    else
        print_warning ".env file not found (will be created from template)"
        ((WARNINGS++))
    fi
}

# Function to validate source code structure
validate_source_structure() {
    print_status "Validating source code structure..."
    
    # Check main server files
    check_file "server.js" "Main server file"
    check_file "server-fastify.js" "Fastify server file"
    check_file "package.json" "Package configuration"
    check_file "schema.graphql" "GraphQL schema"
    check_file "resolvers.js" "GraphQL resolvers"
    
    # Check directories
    check_directory "src" "Source directory"
    check_directory "netstream-nextjs" "Frontend directory"
    check_directory "scripts" "Scripts directory"
    check_directory "public" "Public assets directory"
    
    # Check critical source files
    if [ -d "src" ]; then
        check_directory "src/db" "Database models directory"
        check_directory "src/config" "Configuration directory"
        check_directory "src/utils" "Utilities directory"
        check_directory "src/scrapers" "Scrapers directory"
    fi
    
    # Check frontend structure
    if [ -d "netstream-nextjs" ]; then
        check_file "netstream-nextjs/package.json" "Frontend package.json"
        check_directory "netstream-nextjs/src" "Frontend source directory"
    fi
}

# Function to validate deployment scripts
validate_scripts() {
    print_status "Validating deployment scripts..."
    
    check_file "deploy-ubuntu.sh" "Ubuntu deployment script"
    
    if [ -f "deploy-ubuntu.sh" ]; then
        if [ -x "deploy-ubuntu.sh" ]; then
            print_success "Deployment script is executable"
        else
            print_warning "Deployment script is not executable (run: chmod +x deploy-ubuntu.sh)"
            ((WARNINGS++))
        fi
    fi
}

# Function to check network configuration
check_network_config() {
    print_status "Checking network configuration..."

    # Check current network interfaces
    if command -v ip &> /dev/null; then
        print_status "Current network interfaces:"
        ip addr show | grep -E "inet [0-9]" | awk '{print "  " $2}' | head -5

        # Check for potential subnet conflicts
        DOCKER_SUBNET="**********/16"
        print_status "Docker will use subnet: $DOCKER_SUBNET"

        # Check if Docker subnet conflicts with existing networks
        if ip route show | grep -q "172.20."; then
            print_warning "Potential subnet conflict detected with 172.20.x.x range"
            print_warning "Consider changing Docker subnet in docker-compose.yml"
            ((WARNINGS++))
        else
            print_success "No subnet conflicts detected"
        fi
    else
        print_warning "Cannot check network configuration (ip command not available)"
        ((WARNINGS++))
    fi

    # Check for port conflicts
    print_status "Checking for port conflicts..."
    PORTS=(3000 3001 6379)
    for port in "${PORTS[@]}"; do
        if command -v netstat &> /dev/null; then
            if netstat -tuln | grep -q ":$port "; then
                print_warning "Port $port is already in use"
                ((WARNINGS++))
            else
                print_success "Port $port is available"
            fi
        elif command -v ss &> /dev/null; then
            if ss -tuln | grep -q ":$port "; then
                print_warning "Port $port is already in use"
                ((WARNINGS++))
            else
                print_success "Port $port is available"
            fi
        fi
    done
}

# Function to check for potential issues
check_potential_issues() {
    print_status "Checking for potential issues..."

    # Check for hardcoded secrets in Dockerfiles
    if grep -r "ENV.*API_KEY\|ENV.*TOKEN\|ENV.*PASSWORD" Dockerfile* 2>/dev/null | grep -v "your_.*_here"; then
        print_error "Found hardcoded secrets in Dockerfiles"
        ((ERRORS++))
    else
        print_success "No hardcoded secrets found in Dockerfiles"
    fi

    # Check for proper .gitignore
    if [ -f ".gitignore" ]; then
        if grep -q "\.env$" .gitignore; then
            print_success ".env file is properly ignored in git"
        else
            print_warning ".env file should be added to .gitignore"
            ((WARNINGS++))
        fi
    else
        print_warning ".gitignore file not found"
        ((WARNINGS++))
    fi
}

# Main validation function
main() {
    echo "🔍 NetStream Docker Setup Validation"
    echo "===================================="
    echo ""
    
    validate_compose
    echo ""
    
    validate_dockerfiles
    echo ""
    
    validate_env_files
    echo ""
    
    validate_source_structure
    echo ""
    
    validate_scripts
    echo ""

    check_network_config
    echo ""

    check_potential_issues
    echo ""
    
    # Summary
    echo "📊 Validation Summary"
    echo "===================="
    
    if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
        print_success "✅ All validations passed! Setup is ready for deployment."
    elif [ $ERRORS -eq 0 ]; then
        print_warning "⚠️  Validation completed with $WARNINGS warning(s). Setup should work but consider addressing warnings."
    else
        print_error "❌ Validation failed with $ERRORS error(s) and $WARNINGS warning(s). Please fix errors before deployment."
        exit 1
    fi
    
    echo ""
    echo "Next steps:"
    echo "1. Copy .env.ubuntu to .env and update the required values"
    echo "2. Run: ./deploy-ubuntu.sh deploy"
    echo "3. Access your application at http://localhost:3000"
}

# Run validation
main
