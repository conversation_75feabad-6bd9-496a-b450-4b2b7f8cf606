# NetStream Deployment Guide

## 🚀 Render.com Deployment

### Prerequisites
1. **GitHub Repository** - Push your code to GitHub
2. **Render.com Account** - Sign up at [render.com](https://render.com)
3. **Database Services** - MongoDB and Redis (see options below)

### Quick Deploy Options

#### Option 1: One-Click Deploy (Recommended)
1. Click the deploy button (add to your README):
   ```markdown
   [![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy?repo=https://github.com/YOUR_USERNAME/YOUR_REPO)
   ```

#### Option 2: Manual Deploy
1. **Connect Repository**
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New +" → "Web Service"
   - Connect your GitHub repository

2. **Configure Service**
   - **Name**: `netstream-app`
   - **Environment**: `Docker`
   - **Plan**: `Free` (or paid for better performance)
   - **Branch**: `main`

3. **Environment Variables** (Required)
   ```bash
   NODE_ENV=production
   PORT=3000
   FASTIFY_PORT=3001
   MONGO_URI=your-mongodb-connection-string
   REDIS_URL=your-redis-connection-string
   NEXT_TELEMETRY_DISABLED=1
   SKIP_ENV_VALIDATION=1
   PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
   NODE_OPTIONS=--max-old-space-size=4096
   ```

### Database Setup Options

#### Option A: MongoDB Atlas (Free Tier)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create free cluster
3. Get connection string: `mongodb+srv://username:<EMAIL>/netstream`

#### Option B: Railway MongoDB
1. Go to [Railway](https://railway.app)
2. Deploy MongoDB template
3. Get connection string from environment variables

#### Option C: Render PostgreSQL + MongoDB Alternative
1. Use Render's free PostgreSQL
2. Modify code to use PostgreSQL instead of MongoDB

### Redis Setup Options

#### Option A: Upstash Redis (Free Tier)
1. Go to [Upstash](https://upstash.com)
2. Create free Redis database
3. Get connection URL: `redis://username:password@host:port`

#### Option B: Railway Redis
1. Go to [Railway](https://railway.app)
2. Deploy Redis template
3. Get connection details

### Environment Variables for Production

```bash
# Core Settings
NODE_ENV=production
PORT=3000
FASTIFY_PORT=3001

# Database
MONGO_URI=mongodb+srv://user:<EMAIL>/netstream
REDIS_URL=redis://user:pass@host:port

# Next.js
NEXT_TELEMETRY_DISABLED=1
SKIP_ENV_VALIDATION=1
NEXT_PUBLIC_API_URL=https://your-app.onrender.com
NEXT_PUBLIC_GRAPHQL_ENDPOINT=https://your-app.onrender.com/graphql
NEXT_PUBLIC_API_BASE_URL=https://your-app.onrender.com

# Performance
NODE_OPTIONS=--max-old-space-size=4096
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
```

### Deployment Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Deploy on Render**
   - Service will auto-build from Dockerfile
   - Build time: ~10-15 minutes
   - First deploy may take longer

3. **Verify Deployment**
   - Check build logs for errors
   - Visit your app URL
   - Test GraphQL endpoint: `/graphql`
   - Check health endpoint: `/api/system/health`

### Troubleshooting

#### Build Issues
- Check Dockerfile syntax
- Verify all dependencies in package.json
- Monitor build logs in Render dashboard

#### Runtime Issues
- Check environment variables
- Verify database connections
- Monitor application logs

#### Performance Issues
- Upgrade to paid plan for better resources
- Optimize Docker image size
- Enable caching

### Free Tier Limitations
- **Render Free**: 750 hours/month, sleeps after 15min inactivity
- **MongoDB Atlas**: 512MB storage
- **Upstash Redis**: 10K commands/day

### Production Recommendations
- Use paid plans for production workloads
- Set up monitoring and alerts
- Configure custom domain
- Enable SSL/HTTPS
- Set up backup strategies

## 📱 Android TV App Wrapper

After successful web deployment, you can wrap the web app into an Android TV app using:
- **Capacitor** - For hybrid app development
- **WebView** - For simple web wrapper
- **PWA** - Progressive Web App for TV browsers

## 🔧 Local Development

```bash
# Build and test Docker image locally
docker build -t netstream-combined .
docker run -p 3000:3000 -e MONGO_URI="your-mongo-uri" netstream-combined

# Access locally
http://localhost:3000
```
