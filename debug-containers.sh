#!/bin/bash

# Debug script for NetStream containers

echo "🔍 NetStream Container Debug Information"
echo "======================================="

# Check container status
echo ""
echo "📊 Container Status:"
docker-compose ps

echo ""
echo "🔍 Container Health:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "📋 Backend Logs (last 20 lines):"
echo "--------------------------------"
docker-compose logs --tail=20 backend

echo ""
echo "📋 Frontend Logs (last 20 lines):"
echo "---------------------------------"
docker-compose logs --tail=20 frontend

echo ""
echo "📋 Redis Logs (last 10 lines):"
echo "------------------------------"
docker-compose logs --tail=10 redis

echo ""
echo "🌐 Network Information:"
echo "----------------------"
docker network ls | grep netstream

echo ""
echo "💾 Volume Information:"
echo "---------------------"
docker volume ls | grep netstream

echo ""
echo "🔧 Quick Fix Commands:"
echo "====================="
echo "1. Restart all services:"
echo "   docker-compose restart"
echo ""
echo "2. Restart just frontend:"
echo "   docker-compose restart frontend"
echo ""
echo "3. View live logs:"
echo "   docker-compose logs -f"
echo ""
echo "4. Stop and rebuild:"
echo "   docker-compose down && docker-compose up --build -d"
echo ""
echo "5. Complete cleanup and restart:"
echo "   ./deploy-ubuntu.sh cleanup && ./deploy-ubuntu.sh deploy"
