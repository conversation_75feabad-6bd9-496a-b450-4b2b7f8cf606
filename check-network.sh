#!/bin/bash

# NetStream Network Configuration Checker for Proxmox
# This script helps identify network configuration for Docker deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🌐 NetStream Network Configuration Check"
echo "========================================"
echo ""

# Check current network configuration
print_status "Current Network Configuration:"
echo ""

# Show network interfaces
print_status "Network Interfaces:"
if command -v ip &> /dev/null; then
    ip addr show | grep -E "^[0-9]+:|inet " | while read line; do
        if [[ $line =~ ^[0-9]+: ]]; then
            echo -e "${BLUE}  Interface:${NC} $(echo $line | cut -d: -f2 | awk '{print $1}')"
        elif [[ $line =~ inet ]]; then
            echo -e "${GREEN}    IP:${NC} $(echo $line | awk '{print $2}')"
        fi
    done
else
    print_warning "ip command not available, using ifconfig"
    ifconfig | grep -E "^[a-z]|inet " | while read line; do
        if [[ $line =~ ^[a-z] ]]; then
            echo -e "${BLUE}  Interface:${NC} $(echo $line | awk '{print $1}')"
        elif [[ $line =~ inet ]]; then
            echo -e "${GREEN}    IP:${NC} $(echo $line | awk '{print $2}')"
        fi
    done
fi

echo ""

# Show routing table
print_status "Current Routes:"
if command -v ip &> /dev/null; then
    ip route show | head -10 | while read line; do
        echo "  $line"
    done
else
    route -n | head -10 | while read line; do
        echo "  $line"
    done
fi

echo ""

# Check Docker networks if Docker is installed
if command -v docker &> /dev/null; then
    print_status "Existing Docker Networks:"
    docker network ls 2>/dev/null | while read line; do
        echo "  $line"
    done
    echo ""
    
    # Check default bridge network
    print_status "Docker Bridge Network Details:"
    docker network inspect bridge 2>/dev/null | grep -E "Subnet|Gateway" | while read line; do
        echo "  $line"
    done
else
    print_warning "Docker not installed yet"
fi

echo ""

# Check port availability
print_status "Checking Required Ports:"
PORTS=(3000 3001 6379 27017)
for port in "${PORTS[@]}"; do
    if command -v netstat &> /dev/null; then
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            print_warning "Port $port is in use"
            netstat -tuln | grep ":$port " | while read line; do
                echo "    $line"
            done
        else
            print_success "Port $port is available"
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln 2>/dev/null | grep -q ":$port "; then
            print_warning "Port $port is in use"
            ss -tuln | grep ":$port " | while read line; do
                echo "    $line"
            done
        else
            print_success "Port $port is available"
        fi
    else
        print_warning "Cannot check port $port (no netstat or ss available)"
    fi
done

echo ""

# Analyze potential conflicts
print_status "Network Analysis:"

# Check for common Proxmox network ranges
CURRENT_IPS=$(ip addr show 2>/dev/null | grep "inet " | awk '{print $2}' | cut -d'/' -f1)

echo "Current IP addresses on this VM:"
for ip in $CURRENT_IPS; do
    echo "  - $ip"
    
    # Determine network class
    if [[ $ip =~ ^192\.168\. ]]; then
        echo "    Network: 192.168.x.x (Class C private)"
    elif [[ $ip =~ ^10\. ]]; then
        echo "    Network: 10.x.x.x (Class A private)"
    elif [[ $ip =~ ^172\.(1[6-9]|2[0-9]|3[0-1])\. ]]; then
        echo "    Network: 172.16-31.x.x (Class B private)"
    elif [[ $ip =~ ^127\. ]]; then
        echo "    Network: Loopback"
    else
        echo "    Network: Public or other"
    fi
done

echo ""

# Docker subnet recommendation
print_status "Docker Network Recommendations:"

# Check if **********/16 conflicts
if ip route show 2>/dev/null | grep -q "172\.20\."; then
    print_warning "Current Docker subnet (**********/16) may conflict"
    echo "  Recommended alternatives:"
    echo "  - **********/16"
    echo "  - **********/16"
    echo "  - **********/16"
else
    print_success "Default Docker subnet (**********/16) looks good"
fi

echo ""

# Proxmox specific checks
print_status "Proxmox Environment Checks:"

# Check if running in Proxmox VM
if [ -f "/sys/class/dmi/id/product_name" ]; then
    PRODUCT=$(cat /sys/class/dmi/id/product_name 2>/dev/null)
    if [[ $PRODUCT =~ "QEMU" ]] || [[ $PRODUCT =~ "KVM" ]]; then
        print_success "Running in virtualized environment (likely Proxmox)"
    fi
fi

# Check for veth interfaces (common in containers/VMs)
if ip link show 2>/dev/null | grep -q "veth"; then
    print_status "Virtual ethernet interfaces detected"
fi

echo ""

# Final recommendations
print_status "🎯 Recommendations for Your Setup:"
echo ""
echo "1. External Access Configuration:"
echo "   - Your VM IP: $(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' || echo 'Unable to detect')"
echo "   - Update .env file if you need external access:"
echo "     NEXT_PUBLIC_API_URL=http://YOUR_VM_IP:3001/graphql"
echo ""

echo "2. Firewall Configuration:"
echo "   - Ensure ports 3000 and 3001 are open in Proxmox firewall"
echo "   - Ubuntu UFW: sudo ufw allow 3000 && sudo ufw allow 3001"
echo ""

echo "3. Docker Network:"
if ip route show 2>/dev/null | grep -q "172\.20\."; then
    echo "   - ⚠️  Change Docker subnet in docker-compose.yml"
    echo "   - Replace '**********/16' with '**********/16'"
else
    echo "   - ✅ Current Docker subnet (**********/16) is fine"
fi

echo ""
echo "4. Next Steps:"
echo "   - Run: ./deploy-ubuntu.sh deploy"
echo "   - Access frontend: http://localhost:3000"
echo "   - Access from other machines: http://YOUR_VM_IP:3000"

echo ""
print_success "Network check completed!"
