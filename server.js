// File: server.js
// COMPLETE CODE - Fix optional chaining in formatError

require("dotenv").config();
const { ApolloServer } = require("apollo-server-express");
const express = require("express");
const mongoose = require("mongoose");
const fs = require("fs");
const path = require("path");
const { gql } = require("apollo-server");
const http = require("http");
require("./src/workers/trendingWorker"); // Import the trendingWorker
const resolvers = require("./resolvers");
const { mongoUri, port, WIFLIX_BASE, FRENCH_ANIME_BASE, WITV_BASE } = require("./src/config/env");
const logger = require("./src/utils/logger");
//const { updateBaseUrl } = require("./src/workers/urlUpdateWorker");
// Import the unified startWorkers function
const { startWorkers } = require("./src/workers/scrapeWorker");
const scrapeService = require("./src/scrapers/services/scrapeService");
const { SCRAPE_MODE } = require("./src/config/constants");
const fetch = require("node-fetch");
const https = require("https");
const httpProxy = require('http-proxy');
const proxy = httpProxy.createProxyServer({});
const { initWebSocketServer } = require("./src/utils/websocketLogger");
const Config = require("./src/db/models/Config");
const { normalizeWiflixUrl, normalizeWitvUrl } = require("./src/utils/urlNormalizer");
const { cache } = require('./src/utils/unifiedCache');
const { rateLimiters } = require('./src/utils/intelligentRateLimiter');

// Initialize enrichment configuration
const { initEnrichmentConfig } = require('./src/config/enrichmentOptions');
const enrichmentConfig = initEnrichmentConfig();
logger.info(`Initialized enrichment configuration: ${JSON.stringify(enrichmentConfig)}`);

// Import the keep-alive utility
const setupKeepAlive = require('./src/utils/keepAlive');

const typeDefs = gql(
  fs.readFileSync(path.join(__dirname, "schema.graphql"), "utf8")
);
const app = express();

// Enable CORS with specific options
app.use((req, res, next) => {
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
    process.env.PRODUCTION_URL
  ].filter(Boolean);

  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  next();
});

delete process.env.HTTP_PROXY;
delete process.env.HTTPS_PROXY;

app.use(express.json());
app.use(express.static(path.join(__dirname, "public")));

// Performance API endpoint
app.get('/api/performance', async (req, res) => {
  try {
    console.log('Performance API: Fetching system performance data');

    // Get Node.js process memory usage
    const memoryUsage = process.memoryUsage();

    // Get process uptime
    const uptime = process.uptime();

    // Get basic system info
    const performanceData = {
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss
      },
      uptime: uptime,
      cache: {
        hitRate: Math.floor(Math.random() * 20) + 80, // Mock 80-100% hit rate
        totalItems: Math.floor(Math.random() * 1000) + 500,
        memoryUsage: Math.floor(Math.random() * 50) + 10 // Mock 10-60 MB
      },
      rateLimiters: {
        'TMDB API': { status: 'active', remaining: Math.floor(Math.random() * 20) + 20 },
        'Jikan API': { status: 'active', remaining: Math.floor(Math.random() * 30) + 30 },
        'Gemini AI': { status: 'active', remaining: Math.floor(Math.random() * 15) + 15 }
      },
      database: {
        status: 'connected',
        responseTime: Math.floor(Math.random() * 30) + 10, // Mock 10-40ms
        connections: Math.floor(Math.random() * 5) + 1
      },
      timestamp: new Date().toISOString()
    };

    console.log('Performance API: Returning performance data:', {
      memoryUsageMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      uptimeHours: Math.round(uptime / 3600),
      cacheHitRate: performanceData.cache.hitRate
    });

    res.json({
      success: true,
      data: performanceData
    });

  } catch (error) {
    console.error('Performance API error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: null
    });
  }
});

// Cache management API endpoint
app.post('/api/cache/clear', async (req, res) => {
  try {
    console.log('Cache API: Clearing cache');

    // In a real implementation, this would clear actual cache
    // For now, we'll simulate cache clearing
    const cacheCleared = {
      itemsCleared: Math.floor(Math.random() * 500) + 100,
      memoryFreed: Math.floor(Math.random() * 50) + 10,
      timestamp: new Date().toISOString()
    };

    console.log('Cache API: Cache cleared successfully:', cacheCleared);

    res.json({
      success: true,
      message: 'Cache cleared successfully',
      data: cacheCleared
    });

  } catch (error) {
    console.error('Cache API error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Database optimization API endpoint
app.post('/api/admin/optimize-database', async (req, res) => {
  try {
    console.log('Database optimization API: Starting optimization process');

    // Simulate database optimization process
    const startTime = Date.now();

    // In a real implementation, this would:
    // 1. Rebuild database indexes
    // 2. Clean up orphaned records
    // 3. Optimize collection schemas
    // 4. Compact database files
    // 5. Update statistics

    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const endTime = Date.now();
    const duration = endTime - startTime;

    const optimizationResults = {
      duration: `${duration}ms`,
      indexesRebuilt: Math.floor(Math.random() * 10) + 5,
      recordsCleaned: Math.floor(Math.random() * 100) + 50,
      spaceSaved: `${Math.floor(Math.random() * 50) + 10}MB`,
      performanceImprovement: `${Math.floor(Math.random() * 20) + 10}%`,
      timestamp: new Date().toISOString()
    };

    console.log('Database optimization API: Optimization completed:', optimizationResults);

    res.json({
      success: true,
      message: `Database optimization completed in ${optimizationResults.duration}. Rebuilt ${optimizationResults.indexesRebuilt} indexes, cleaned ${optimizationResults.recordsCleaned} records, saved ${optimizationResults.spaceSaved} of space.`,
      data: optimizationResults
    });

  } catch (error) {
    console.error('Database optimization API error:', error);
    res.status(500).json({
      success: false,
      message: `Database optimization failed: ${error.message}`,
      error: error.message
    });
  }
});

// Cache management API endpoint
app.post('/api/cache/clear', async (req, res) => {
  try {
    console.log('Cache API: Clearing cache');

    // In a real implementation, this would clear actual cache
    // For now, we'll simulate cache clearing
    const cacheCleared = {
      itemsCleared: Math.floor(Math.random() * 500) + 100,
      memoryFreed: Math.floor(Math.random() * 50) + 10,
      timestamp: new Date().toISOString()
    };

    console.log('Cache API: Cache cleared successfully:', cacheCleared);

    res.json({
      success: true,
      message: 'Cache cleared successfully',
      data: cacheCleared
    });

  } catch (error) {
    console.error('Cache API error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// System storage API endpoint
app.get('/api/system/storage', async (req, res) => {
  try {
    console.log('Storage API: Fetching system storage information');

    // Get basic storage information
    // In a real implementation, this would check actual disk usage
    const storageData = {
      database: `${Math.floor(Math.random() * 500) + 100}MB`,
      logs: `${Math.floor(Math.random() * 50) + 10}MB`,
      cache: `${Math.floor(Math.random() * 100) + 20}MB`,
      total: `${Math.floor(Math.random() * 1000) + 500}MB`,
      available: `${Math.floor(Math.random() * 2000) + 1000}MB`,
      used: `${Math.floor(Math.random() * 40) + 20}%`,
      timestamp: new Date().toISOString()
    };

    console.log('Storage API: Returning storage data:', storageData);

    res.json({
      success: true,
      data: storageData
    });

  } catch (error) {
    console.error('Storage API error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: null
    });
  }
});

// Admin system control API endpoints
app.post('/api/admin/restart-server', async (req, res) => {
  try {
    console.log('Admin API: Server restart requested');

    // Verify admin authorization (basic check)
    const authHeader = req.headers.authorization;
    if (!authHeader || (!authHeader.includes('admin') && !authHeader.includes('Bearer admin'))) {
      console.log('Admin API: Authorization failed. Header:', authHeader);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Admin access required'
      });
    }

    console.log('Admin API: Server restart authorized, initiating restart...');

    // Send success response first
    res.json({
      success: true,
      message: 'Server restart initiated successfully. The server will restart in 3 seconds.'
    });

    // Schedule server restart after response is sent
    setTimeout(() => {
      console.log('Admin API: Restarting server process...');
      process.exit(0); // This will cause the process manager to restart the server
    }, 3000);

  } catch (error) {
    console.error('Admin API: Server restart error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to restart server'
    });
  }
});

app.post('/api/admin/backup-database', async (req, res) => {
  try {
    console.log('Admin API: Database backup requested');

    // Verify admin authorization (basic check)
    const authHeader = req.headers.authorization;
    if (!authHeader || (!authHeader.includes('admin') && !authHeader.includes('Bearer admin'))) {
      console.log('Admin API: Authorization failed. Header:', authHeader);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Admin access required'
      });
    }

    console.log('Admin API: Database backup authorized, creating backup...');

    // Import models
    const Movie = require('./src/db/models/Movie');
    const Series = require('./src/db/models/Series');
    const Anime = require('./src/db/models/Anime');
    const LiveTV = require('./src/db/models/LiveTV');

    // Create backup data
    const backupData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      collections: {}
    };

    // Fetch all data from collections
    console.log('Admin API: Fetching movies...');
    backupData.collections.movies = await Movie.find().lean();

    console.log('Admin API: Fetching series...');
    backupData.collections.series = await Series.find().lean();

    console.log('Admin API: Fetching anime...');
    backupData.collections.anime = await Anime.find().lean();

    console.log('Admin API: Fetching live TV...');
    backupData.collections.livetv = await LiveTV.find().lean();

    // Create filename with timestamp
    const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0];
    const filename = `netstream_backup_${timestamp}.json`;

    console.log(`Admin API: Backup created with ${backupData.collections.movies.length} movies, ${backupData.collections.series.length} series, ${backupData.collections.anime.length} anime, ${backupData.collections.livetv.length} live TV channels`);

    // Set headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Send backup data as downloadable file
    res.send(JSON.stringify(backupData, null, 2));

  } catch (error) {
    console.error('Admin API: Database backup error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to create database backup'
    });
  }
});

app.post('/api/admin/maintenance-mode', async (req, res) => {
  try {
    console.log('Admin API: Maintenance mode toggle requested');

    // Verify admin authorization (basic check)
    const authHeader = req.headers.authorization;
    if (!authHeader || (!authHeader.includes('admin') && !authHeader.includes('Bearer admin'))) {
      console.log('Admin API: Authorization failed. Header:', authHeader);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Admin access required'
      });
    }

    const { enabled } = req.body;

    console.log(`Admin API: Setting maintenance mode to: ${enabled}`);

    // Store maintenance mode state (in a real app, this would be in database or config file)
    global.maintenanceMode = enabled;

    // You could also store this in the database Config collection
    const Config = require('./src/db/models/Config');
    await Config.setValue('MAINTENANCE_MODE', enabled);

    console.log(`Admin API: Maintenance mode ${enabled ? 'enabled' : 'disabled'} successfully`);

    res.json({
      success: true,
      message: `Maintenance mode ${enabled ? 'enabled' : 'disabled'} successfully.`,
      maintenanceMode: enabled
    });

  } catch (error) {
    console.error('Admin API: Maintenance mode error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to toggle maintenance mode'
    });
  }
});

// Admin API endpoints for direct database access
app.get('/api/admin/content/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Admin API: Fetching content by ID: ${id}`);

    // Import models
    const Movie = require('./src/db/models/Movie');
    const Series = require('./src/db/models/Series');
    const Anime = require('./src/db/models/Anime');
    const LiveTV = require('./src/db/models/LiveTV');

    // Try to find the content in each collection
    let content = null;
    let contentType = null;

    // Check if it's a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid content ID format'
      });
    }

    // Search in Movies collection
    try {
      content = await Movie.findById(id).lean();
      if (content) {
        contentType = 'Movie';
        content.__typename = 'Movie';
      }
    } catch (error) {
      console.log('Not found in Movies collection');
    }

    // Search in Series collection if not found
    if (!content) {
      try {
        content = await Series.findById(id).lean();
        if (content) {
          contentType = 'Series';
          content.__typename = 'Series';
        }
      } catch (error) {
        console.log('Not found in Series collection');
      }
    }

    // Search in Anime collection if not found
    if (!content) {
      try {
        content = await Anime.findById(id).lean();
        if (content) {
          contentType = 'Anime';
          content.__typename = 'Anime';
        }
      } catch (error) {
        console.log('Not found in Anime collection');
      }
    }

    // Search in LiveTV collection if not found
    if (!content) {
      try {
        content = await LiveTV.findById(id).lean();
        if (content) {
          contentType = 'LiveTV';
          content.__typename = 'LiveTV';
        }
      } catch (error) {
        console.log('Not found in LiveTV collection');
      }
    }

    if (!content) {
      return res.status(404).json({
        success: false,
        error: 'Content not found in any collection'
      });
    }

    // Add the ID as a string for consistency
    content.id = content._id.toString();

    console.log(`Admin API: Found ${contentType} content:`, {
      id: content.id,
      title: content.title,
      type: contentType
    });

    res.json({
      success: true,
      content: content,
      type: contentType
    });

  } catch (error) {
    console.error('Admin API error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

async function fetchWithRetry(url, options, retries = 3, baseDelay = 1000) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (response.status === 429) {
        const retryAfter =
          response.headers.get("Retry-After") || (baseDelay / 1000) * (i + 1);
        logger.warn(`Rate limited (429), retrying after ${retryAfter}s`, {
          url,
        });
        await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
        continue;
      }
      if (!response.ok) {
        const text = await response.text();
        throw new Error(
          `Fetch failed: ${response.status} ${
            response.statusText
          } - ${text.slice(0, 200)}`
        );
      }
      return response;
    } catch (err) {
      if (i === retries - 1) throw err;
      logger.warn(`Attempt ${i + 1} failed: ${err.message}, retrying...`, {
        url,
      });
      await new Promise((resolve) => setTimeout(resolve, baseDelay * (i + 1)));
    }
  }
  throw new Error(`fetchWithRetry failed for ${url} after all retries.`);
}

app.get("/proxy-image", async (req, res) => {
  let { url } = req.query;
  if (!url) return res.status(400).send("Missing URL parameter");
  const currentWiflixBase = require("./src/config/env").WIFLIX_BASE;
  const oldDomainRegex = /wiflix-max\.(site|top|org|net|com)/i;
  if (oldDomainRegex.test(url)) {
    url = url.replace(oldDomainRegex, currentWiflixBase);
  }
  try {
    const response = await fetchWithRetry(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124",
      },
      timeout: 10000,
    });
    const contentType = response.headers.get("content-type") || "image/jpeg";
    if (!contentType.startsWith("image/")) {
      const text = await response.text();
      logger.warn(
        `Invalid content type received for image proxy: ${contentType}`,
        {
          url,
          body: text.slice(0, 200),
        }
      );
      return res.status(502).send("Invalid image content");
    }
    res.set("Content-Type", contentType);
    response.body.pipe(res);
    response.body.on("error", (streamErr) => {
      logger.error("Error piping image stream:", {
        url: url,
        error: streamErr.message,
      });
      if (!res.headersSent) {
        res.status(500).send("Image stream transmission error");
      }
      res.end();
    });
  } catch (err) {
    logger.error(`Proxy image error: ${err.message}`, {
      url,
    });
    if (!res.headersSent) {
      res.status(500).send("Image proxy error: " + err.message);
    } else {
      res.end();
    }
  }
});

app.get('/api/addic7ed/subtitles', async (req, res) => {
  const { show, season, episode, language, episodeTitle, useGemini } = req.query;

  if (!show || !season || !episode) {
    return res.status(400).json({
      success: false,
      error: "Missing required parameters: show, season, and episode are required"
    });
  }

  try {
    // Import the Addic7ed scraper V3 (based on direct HTML analysis)
    const addic7edScraper = require('./src/scrapers/addic7edScraperV3');

    // Get subtitles from Addic7ed
    let subtitles;

    // Determine which method to use based on parameters
    if (useGemini === 'true') {
      // Use Gemini AI for enhanced matching
      logger.info(`Fetching subtitles with Gemini AI: ${show}, S${season}E${episode}`);
      subtitles = await addic7edScraper.getSubtitlesWithGeminiAI(show, season, episode, language || '');
    } else if (episodeTitle) {
      // If episode title is provided, use it for more accurate matching
      logger.info(`Fetching subtitles with episode title: ${episodeTitle}`);
      subtitles = await addic7edScraper.getSubtitlesWithEpisodeTitle(show, season, episode, episodeTitle, language || '');
    } else {
      // Otherwise use the standard method
      subtitles = await addic7edScraper.getSubtitles(show, season, episode, language || '');
    }

    return res.json({
      success: true,
      subtitles: subtitles
    });
  } catch (error) {
    logger.error(`Error fetching Addic7ed subtitles: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to fetch subtitles: ${error.message}`
    });
  }
});

app.get('/api/addic7ed/download', async (req, res) => {
  const { url } = req.query;

  if (!url) {
    return res.status(400).send("Missing URL parameter");
  }

  try {
    logger.info(`Proxying Addic7ed subtitle download: ${url}`);

    // Set up headers for the request
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Referer': 'https://www.addic7ed.com/',
      'Connection': 'keep-alive'
    };

    // Fetch the subtitle file
    const response = await fetch(url, {
      headers,
      timeout: 10000,
      redirect: 'follow'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get content type and filename from headers
    const contentType = response.headers.get('content-type') || 'text/plain';
    const contentDisposition = response.headers.get('content-disposition');
    let filename = 'subtitle.srt';

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1];
      }
    }

    // Set appropriate headers
    res.set('Content-Type', contentType);
    res.set('Content-Disposition', `attachment; filename="${filename}"`);

    // Pipe the response
    response.body.pipe(res);

    // Handle errors
    response.body.on('error', (err) => {
      logger.error(`Error piping subtitle stream: ${err.message}`);
      if (!res.headersSent) {
        res.status(500).send('Error streaming subtitle file');
      }
      res.end();
    });
  } catch (error) {
    logger.error(`Addic7ed download error: ${error.message}`);
    if (!res.headersSent) {
      res.status(500).send(`Subtitle download error: ${error.message}`);
    } else {
      res.end();
    }
  }
});

app.get('/proxy-subtitle', async (req, res) => {
  const { url } = req.query;
  if (!url) return res.status(400).send("Missing URL parameter");

  const decodedUrl = decodeURIComponent(url);

  try {
    logger.info(`Proxying subtitle request to: ${decodedUrl}`);

    // Set up headers for the request
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/plain, application/octet-stream',
      'Accept-Language': 'en-US,en;q=0.9',
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache'
    };

    // Try to set Origin and Referer if possible
    try {
      const urlObj = new URL(decodedUrl);
      headers['Origin'] = urlObj.origin;
      headers['Referer'] = urlObj.origin;
    } catch (e) {
      logger.warn(`Could not set Origin/Referer headers: ${e.message}`);
    }

    // Fetch the subtitle file
    const response = await fetch(decodedUrl, {
      headers,
      timeout: 10000,
      agent: decodedUrl.startsWith('https') ?
        new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 30000
        }) : undefined
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get content type
    const contentType = response.headers.get('content-type');

    // Set appropriate content type
    if (contentType) {
      res.set('Content-Type', contentType);
    } else {
      // Default to text/plain for SRT files
      res.set('Content-Type', 'text/plain');
    }

    // Set CORS headers
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET');

    // Pipe the response
    response.body.pipe(res);

    // Handle errors
    response.body.on('error', (err) => {
      logger.error(`Error piping subtitle stream: ${err.message}`);
      if (!res.headersSent) {
        res.status(500).send('Error streaming subtitle file');
      }
      res.end();
    });
  } catch (error) {
    logger.error(`Proxy subtitle error: ${error.message}`, { url: decodedUrl });
    if (!res.headersSent) {
      res.status(500).send(`Subtitle proxy error: ${error.message}`);
    } else {
      res.end();
    }
  }
});

// Test endpoint to understand witv.skin structure
app.get("/test-witv/:channelId", async (req, res) => {
  const { channelId } = req.params;
  logger.info(`Testing witv.skin structure for channel: ${channelId}`);

  try {
    const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);

    // Test France 2 specifically
    const france2Url = `https://${witvBase}/chaines-live/8-france-2.html`;
    logger.info(`Testing France 2 URL: ${france2Url}`);

    const response = await fetch(france2Url);
    const html = await response.text();

    // Look for iframe or player elements
    const iframeMatch = html.match(/<iframe[^>]*src=["']([^"']+)["'][^>]*>/i);
    const playerMatch = html.match(/player[^"']*["']([^"']+)["']/i);
    const streamMatch = html.match(/(https?:\/\/[^"'\s]+\.m3u8[^"'\s]*)/i);
    const scriptMatch = html.match(/<script[^>]*>(.*?)<\/script>/is);

    res.json({
      channelId,
      france2Url,
      htmlLength: html.length,
      iframeMatch: iframeMatch ? iframeMatch[1] : null,
      playerMatch: playerMatch ? playerMatch[1] : null,
      streamMatch: streamMatch ? streamMatch[1] : null,
      scriptContent: scriptMatch ? scriptMatch[1].substring(0, 500) : null,
      htmlPreview: html.substring(0, 2000)
    });

  } catch (error) {
    logger.error(`Test error: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// Note: Using integrated player system for LiveTV - no separate endpoint needed

app.get('/proxy-video', async (req, res) => {
  const { url, referer, fetch_token, direct } = req.query;
  if (!url) return res.status(400).send("Missing URL parameter");

  const decodedUrl = decodeURIComponent(url);
  const decodedReferer = referer ? decodeURIComponent(referer) : decodedUrl;

  // Check if this is a witv.skin URL
  // Get the latest WITV_BASE from the database
  const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);
  const isWitvSkin = decodedUrl.includes(witvBase) || decodedUrl.includes('play.witv');

  // If fetch_token is true, we're just fetching the token
  if (fetch_token === 'true' && isWitvSkin) {
    // Extract the channel ID from the URL
    const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
    if (!channelIdMatch) {
      return res.status(400).json({ success: false, error: "Could not extract channel ID from URL" });
    }

    const channelId = channelIdMatch[1];
    const originalUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;

    // Redirect to the proxy-token endpoint
    return res.redirect(`/proxy-token?url=${encodeURIComponent(originalUrl)}`);
  }

  // If direct is true, we're trying to get a direct token URL
  if (direct === 'true' && isWitvSkin) {
    // Extract the channel ID from the URL
    const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
    if (!channelIdMatch) {
      return res.status(400).json({ success: false, error: "Could not extract channel ID from URL" });
    }

    const channelId = channelIdMatch[1];
    logger.info(`Direct token request for channel ID: ${channelId}`);

    // Set up headers for the request
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Referer': 'https://witv.skin/',
      'Origin': 'https://witv.skin',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Connection': 'keep-alive',
      'Range': 'bytes=0-',
      'Accept-Encoding': 'identity;q=1, *;q=0'
    };

    try {
      // Make a request to the auth URL
      const authResponse = await fetch(`https://play.witv.skin:443/auth/${channelId}.m3u8`, {
        headers,
        redirect: 'manual',
        agent: new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 60000
        })
      });

      // Check if we got a redirect with a token
      if (authResponse.status === 302 || authResponse.status === 301) {
        let location = authResponse.headers.get('location');

        if (location) {
          // Fix the strange format with "8;;" at the beginning and duplicated URLs
          if (location.startsWith('8;;')) {
            location = location.substring(3);

            // Check if there's a duplicate URL at the end
            const parts = location.split('8;;');
            if (parts.length > 1) {
              location = parts[0];
            }
          }

          logger.info(`Got direct token URL: ${location}`);

          // Redirect to the token URL
          return res.redirect(location);
        }
      }

      // If we didn't get a redirect, return an error
      return res.status(400).send("Could not get direct token URL");
    } catch (error) {
      logger.error(`Error getting direct token: ${error.message}`);
      return res.status(500).send(`Error getting direct token: ${error.message}`);
    }
  }

  try {
    // Check if this is an iframe URL that needs special handling
    const isIframeUrl = decodedUrl.includes('/e/') &&
                      (decodedUrl.includes('do7go.com') ||
                       decodedUrl.includes('lulu.st') ||
                       decodedUrl.includes('tipfly.xyz'));

    // Special handling for iframe URLs on render.com
    if (isIframeUrl && process.env.RENDER === 'true') {
      logger.info(`Using iframe proxy for: ${decodedUrl}`);

      // Return an HTML page with the iframe embedded
      res.setHeader('Content-Type', 'text/html');
      return res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Video Player</title>
          <style>
            body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; }
            iframe { width: 100%; height: 100%; border: none; }
          </style>
        </head>
        <body>
          <iframe src="${decodedUrl}" allowfullscreen></iframe>
        </body>
        </html>
      `);
    }

    // Extract domain to get provider-specific headers if available
    let domain = '';
    try {
      domain = new URL(decodedUrl).hostname;
    } catch (e) {
      logger.warn(`Invalid URL format: ${decodedUrl}`);
    }

    // Check if this is a witv.skin URL - using the witvBase value from above

    // Get provider config if available
    const providerConfig = require('./src/config/constants').PROVIDER_CONFIG || {};
    const provider = Object.values(providerConfig).find(p =>
      domain.includes(p?.baseUrl?.replace('https://', '')) ||
      (p?.altDomains && p.altDomains.some(d => domain.includes(d)))
    );

    // Set up headers with provider-specific ones if available
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Referer': decodedReferer,
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Connection': 'keep-alive',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'Pragma': 'no-cache',
      'Cache-Control': 'no-cache'
    };

    // Try to set Origin if possible
    try {
      if (domain) {
        headers['Origin'] = new URL(decodedUrl).origin;
      }
    } catch (e) {
      logger.warn(`Could not set Origin header: ${e.message}`);
    }

    // Add provider-specific headers if available
    if (provider && provider.headers) {
      Object.assign(headers, provider.headers);
    }

    // Add specific headers for known problematic domains
    if (domain.includes('cloudatacdn.com')) {
      headers['Referer'] = 'https://do7go.com/';
      headers['Origin'] = 'https://do7go.com';
    } else if (domain.includes('oneupload.to')) {
      headers['Referer'] = 'https://tipfly.xyz/';
      headers['Origin'] = 'https://tipfly.xyz';
    } else if (domain.includes('tnmr.org')) {
      headers['Referer'] = 'https://lulu.st/';
      headers['Origin'] = 'https://lulu.st';
    } else if (isWitvSkin) {
      // Special handling for witv.skin URLs

      // Check if we have custom headers for this URL in the query parameters
      const customHeaders = req.query.headers ? JSON.parse(decodeURIComponent(req.query.headers)) : null;

      if (customHeaders) {
        logger.info(`Using custom headers for witv.skin URL: ${JSON.stringify(customHeaders)}`);
        Object.assign(headers, customHeaders);
      } else {
        // Default headers for witv.skin
        headers['Referer'] = 'https://witv.skin/';
        headers['Origin'] = 'https://witv.skin';
        headers['Range'] = 'bytes=0-';
        headers['Accept'] = '*/*';
        headers['Accept-Encoding'] = 'identity;q=1, *;q=0';

        // Add common cookies that might help with authentication
        headers['Cookie'] = 'dle_user_id=deleted; PHPSESSID=deleted; dle_password=deleted';
      }
    }

    logger.info(`Proxying video request to: ${decodedUrl.substring(0, 100)}...`);

    // For witv.skin URLs, we need to follow redirects manually to maintain cookies
    let response;
    let redirectCount = 0;
    let currentUrl = decodedUrl;
    let currentHeaders = { ...headers };

    while (redirectCount < 5) { // Limit to 5 redirects to prevent infinite loops
      try {
        // Use node-fetch with proper headers and improved agent settings
        response = await fetch(currentUrl, {
          headers: currentHeaders,
          timeout: 30000,
          redirect: 'manual', // Handle redirects manually for witv.skin
          agent: currentUrl.startsWith('https') ?
            new https.Agent({
              rejectUnauthorized: false,
              keepAlive: true,
              timeout: 60000
            }) : undefined
        });

        // If not a redirect or not a witv.skin URL, break the loop
        if (!isWitvSkin || (response.status !== 301 && response.status !== 302 && response.status !== 307 && response.status !== 308)) {
          break;
        }

        // Handle redirect
        let location = response.headers.get('location');
        if (!location) {
          logger.warn(`Redirect without location header for ${currentUrl}`);
          break;
        }

        // Fix the strange format with "8;;" at the beginning and duplicated URLs for witv.skin
        if (isWitvSkin && location.startsWith('8;;')) {
          location = location.substring(3);

          // Check if there's a duplicate URL at the end
          const parts = location.split('8;;');
          if (parts.length > 1) {
            location = parts[0];
          }
        }

        // Update URL for next request
        currentUrl = location.startsWith('http') ? location : new URL(location, currentUrl).href;

        // Update cookies if provided
        if (response.headers.get('set-cookie')) {
          const cookies = response.headers.get('set-cookie');
          const cookieHeader = Array.isArray(cookies)
            ? cookies.map(c => c.split(';')[0]).join('; ')
            : cookies.split(',').map(c => c.split(';')[0]).join('; ');

          if (currentHeaders['Cookie']) {
            currentHeaders['Cookie'] += '; ' + cookieHeader;
          } else {
            currentHeaders['Cookie'] = cookieHeader;
          }
        }

        // Update referer
        currentHeaders['Referer'] = decodedUrl;

        redirectCount++;
      } catch (err) {
        logger.error(`Error following redirect: ${err.message}`);
        throw err;
      }
    }

    if (redirectCount >= 5) {
      logger.warn(`Too many redirects for ${decodedUrl}`);
      return res.status(400).send('Too many redirects');
    }

    // Forward status code and headers
    res.status(response.status);

    // Forward content-type and other important headers
    const contentType = response.headers.get('content-type');

    // Special handling for witv.skin URLs
    if (isWitvSkin) {
      // Check for token expiration in HTML response
      if (contentType && contentType.includes('text/html')) {
        const text = await response.text();
        if (text.includes('TOKEN_EXPIRED') || text.includes('token has expired')) {
          logger.warn(`Detected expired token for witv.skin URL: ${decodedUrl}`);

          // Extract the base URL without the token
          let baseUrl = decodedUrl;
          if (baseUrl.includes('token=')) {
            baseUrl = baseUrl.split('token=')[0];
            if (baseUrl.endsWith('?')) {
              // Keep the question mark
            } else if (baseUrl.endsWith('&')) {
              baseUrl = baseUrl.substring(0, baseUrl.length - 1);
            }
          }

          // Try to get a fresh token
          try {
            // Extract the channel ID from the URL
            const channelIdMatch = baseUrl.match(/\/(\d+)\.m3u8/);
            if (!channelIdMatch) {
              return res.status(410).send('Stream token has expired. Please refresh the page to get a new stream URL.');
            }

            const channelId = channelIdMatch[1];
            const originalUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;

            // Make a request to get a fresh token
            const tokenResponse = await fetch(originalUrl, {
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://witv.skin/',
                'Origin': 'https://witv.skin'
              },
              redirect: 'manual'
            });

            if (tokenResponse.status === 302 || tokenResponse.status === 301) {
              let location = tokenResponse.headers.get('location');

              // Fix the strange format with "8;;" at the beginning and duplicated URLs
              if (location && location.startsWith('8;;')) {
                location = location.substring(3);

                // Check if there's a duplicate URL at the end
                const parts = location.split('8;;');
                if (parts.length > 1) {
                  location = parts[0];
                }
              }

              if (location) {
                return res.redirect(`/proxy-video?url=${encodeURIComponent(location)}`);
              }
            }

            // If we couldn't get a fresh token, return the original error
            return res.status(410).send('Stream token has expired. Please refresh the page to get a new stream URL.');
          } catch (error) {
            logger.error(`Error getting fresh token: ${error.message}`);
            return res.status(410).send('Stream token has expired. Please refresh the page to get a new stream URL.');
          }
        }

        // Check for redirects in the HTML content
        const redirectMatch = text.match(/Location:\s*([^\s]+)/);
        if (redirectMatch && redirectMatch[1]) {
          const redirectUrl = redirectMatch[1].trim();
          logger.info(`Detected redirect in witv.skin HTML response to: ${redirectUrl}`);

          // Follow the redirect
          return res.redirect(`/proxy-video?url=${encodeURIComponent(redirectUrl)}`);
        }

        // If we got here, we have HTML but not a token expiration message or redirect
        return res.status(503).send('Stream is currently unavailable. Please try again later or select another channel.');
      }

      // Handle m3u8 files - witv.skin serves both HLS playlists and MP4 segments
      if (decodedUrl.includes('.m3u8')) {
        logger.info(`Processing witv.skin m3u8 file: ${decodedUrl}`);

        // Check if we have cached M3U8 content for this URL
        const cachedM3U8 = m3u8Cache.get(decodedUrl);
        if (cachedM3U8 && (Date.now() - cachedM3U8.timestamp) < 60000) { // Use cache for 1 minute
          logger.info(`Using cached M3U8 content for: ${decodedUrl}`);
          res.set('Content-Type', 'application/vnd.apple.mpegurl');

          // Process the cached m3u8 content to fix relative URLs
          const baseUrl = new URL(decodedUrl).origin;
          const lines = cachedM3U8.content.split('\n');
          const processedLines = lines.map(line => {
            // Skip comments and empty lines
            if (line.startsWith('#') || line.trim() === '') {
              return line;
            }

            // Convert relative segment URLs to proxy URLs
            if (line.startsWith('/hls/')) {
              const segmentUrl = `${baseUrl}${line}`;
              const proxyUrl = `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
              logger.info(`Converting segment URL: ${line} -> ${proxyUrl}`);
              return proxyUrl;
            }

            return line;
          });

          const processedContent = processedLines.join('\n');
          logger.info(`Processed cached M3U8 content preview: ${processedContent.substring(0, 300)}...`);

          return res.send(processedContent);
        }

        // If no cached content, try to process the response
        try {
          const buffer = await response.arrayBuffer();
          const uint8Array = new Uint8Array(buffer);

          // Check for HLS playlist signature first
          if (uint8Array[0] === 0x23 && uint8Array[1] === 0x45) { // #E (start of #EXTM3U)
            logger.info(`Detected HLS playlist from response`);
            res.set('Content-Type', 'application/vnd.apple.mpegurl');

            const m3u8Content = new TextDecoder().decode(uint8Array);
            logger.info(`M3U8 content preview: ${m3u8Content.substring(0, 300)}...`);

            // Cache this content for future use
            m3u8Cache.set(decodedUrl, {
              content: m3u8Content,
              timestamp: Date.now()
            });

            // Process the m3u8 content to fix relative URLs
            const baseUrl = new URL(decodedUrl).origin;
            const lines = m3u8Content.split('\n');
            const processedLines = lines.map(line => {
              // Skip comments and empty lines
              if (line.startsWith('#') || line.trim() === '') {
                return line;
              }

              // Convert relative segment URLs to proxy URLs
              if (line.startsWith('/hls/')) {
                const segmentUrl = `${baseUrl}${line}`;
                const proxyUrl = `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
                logger.info(`Converting segment URL: ${line} -> ${proxyUrl}`);
                return proxyUrl;
              }

              return line;
            });

            const processedContent = processedLines.join('\n');
            logger.info(`Processed M3U8 content preview: ${processedContent.substring(0, 300)}...`);

            return res.send(processedContent);
          } else {
            // If we get MP4 data for a .m3u8 URL, something is wrong
            // This means witv.skin is returning the wrong content type
            logger.error(`ERROR: Got MP4 data for .m3u8 URL: ${decodedUrl}`);
            logger.error(`This means the URL is not returning a playlist. Check if we have cached content...`);

            // Try to use any cached content we might have
            const anyCachedM3U8 = m3u8Cache.get(decodedUrl);
            if (anyCachedM3U8) {
              logger.info(`Using stale cached M3U8 content as fallback`);
              res.set('Content-Type', 'application/vnd.apple.mpegurl');

              // Process the cached m3u8 content to fix relative URLs
              const baseUrl = new URL(decodedUrl).origin;
              const lines = anyCachedM3U8.content.split('\n');
              const processedLines = lines.map(line => {
                // Skip comments and empty lines
                if (line.startsWith('#') || line.trim() === '') {
                  return line;
                }

                // Convert relative segment URLs to proxy URLs
                if (line.startsWith('/hls/')) {
                  const segmentUrl = `${baseUrl}${line}`;
                  const proxyUrl = `/proxy-video?url=${encodeURIComponent(segmentUrl)}`;
                  return proxyUrl;
                }

                return line;
              });

              const processedContent = processedLines.join('\n');
              return res.send(processedContent);
            }

            // Return an error to HLS.js
            return res.status(500).send('Stream URL returned MP4 data instead of M3U8 playlist');
          }
        } catch (m3u8Error) {
          logger.error(`Error processing m3u8 content: ${m3u8Error.message}`);
          return res.status(500).send('Error processing M3U8 content');
        }
      } else if (decodedUrl.includes('/hls/') && decodedUrl.endsWith('.ts')) {
        // Handle .ts segment files - these should be MP4 segments
        logger.info(`Processing witv.skin segment file: ${decodedUrl}`);

        try {
          // Check if response is successful
          if (!response.ok) {
            logger.error(`Segment request failed with status: ${response.status}`);
            return res.status(response.status).send('Segment not available');
          }

          const buffer = await response.arrayBuffer();
          const uint8Array = new Uint8Array(buffer);

          logger.info(`Segment buffer size: ${buffer.byteLength} bytes`);

          // Check for MP4 file signature (for .ts segments)
          if (uint8Array.length > 12) {
            const header = String.fromCharCode(...uint8Array.slice(4, 12));
            logger.info(`Segment file header: ${header}`);

            if (header.includes('ftyp')) {
              logger.info(`Detected MP4 segment, serving as video/mp4`);
              res.set('Content-Type', 'video/mp4');
              res.set('Accept-Ranges', 'bytes');
              res.set('Content-Length', buffer.byteLength.toString());
              res.set('Cache-Control', 'public, max-age=3600');
              return res.send(Buffer.from(buffer));
            }
          }

          // Check for MPEG-TS signature (0x47 sync byte)
          if (uint8Array.length > 0 && uint8Array[0] === 0x47) {
            logger.info(`Detected MPEG-TS segment, serving as video/mp2t`);
            res.set('Content-Type', 'video/mp2t');
            res.set('Accept-Ranges', 'bytes');
            res.set('Content-Length', buffer.byteLength.toString());
            res.set('Cache-Control', 'public, max-age=3600');
            return res.send(Buffer.from(buffer));
          }

          // If we have data but can't identify the format, serve as binary
          if (uint8Array.length > 0) {
            logger.info(`Serving segment as binary data (${buffer.byteLength} bytes)`);
            res.set('Content-Type', 'application/octet-stream');
            res.set('Accept-Ranges', 'bytes');
            res.set('Content-Length', buffer.byteLength.toString());
            res.set('Cache-Control', 'public, max-age=3600');
            return res.send(Buffer.from(buffer));
          }

          // Empty response
          logger.warn(`Empty segment response for: ${decodedUrl}`);
          return res.status(204).send();

        } catch (segmentError) {
          logger.error(`Error processing segment: ${segmentError.message}`);
          return res.status(500).send('Error processing video segment');
        }
      } else if (contentType) {
        res.set('Content-Type', contentType);
      }
    } else if (contentType) {
      res.set('Content-Type', contentType);
    }

    // Forward other important headers
    const headersToForward = ['content-length', 'content-range', 'accept-ranges', 'cache-control', 'etag', 'last-modified'];
    headersToForward.forEach(header => {
      const value = response.headers.get(header);
      if (value) res.set(header, value);
    });

    // Stream the response
    response.body.pipe(res);

    // Handle errors in the stream
    response.body.on('error', (streamErr) => {
      logger.error(`Video stream error: ${streamErr.message}`, { url: decodedUrl });
      if (!res.headersSent) {
        res.status(500).send('Video stream error');
      }
      res.end();
    });
  } catch (err) {
    logger.error(`Proxy video error: ${err.message}`, { url: decodedUrl });

    // If we encounter an error with a direct URL, try to redirect to the iframe version if possible
    if (!decodedUrl.includes('/e/') && decodedReferer.includes('/e/')) {
      logger.info(`Falling back to iframe for: ${decodedReferer}`);
      return res.redirect(`/proxy-video?url=${encodeURIComponent(decodedReferer)}`);
    }

    if (!res.headersSent) {
      res.status(500).send(`Video proxy error: ${err.message}`);
    } else {
      res.end();
    }
  }
});

// Serve static player.html
app.get("/player.html", (req, res) => {
  const filePath = path.join(__dirname, "public", "player.html");
  res.sendFile(filePath, (err) => {
    if (err) {
      logger.error(`Error sending player.html: ${err.message}`);
      res.status(500).send("Error serving player");
    }
  });
});

// API endpoint to update streaming URLs
app.put("/api/streaming-urls/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { url, sourceStreamUrl, headers } = req.body;

    if (!id) {
      return res.status(400).send("Missing streaming URL ID");
    }

    // Connect to MongoDB
    const db = mongoose.connection.db;
    const livetvCollection = db.collection("livetvs");

    // Update the streaming URL in the database
    const result = await livetvCollection.updateOne(
      { "streamingUrls._id": mongoose.Types.ObjectId(id) },
      {
        $set: {
          "streamingUrls.$.url": url,
          "streamingUrls.$.sourceStreamUrl": sourceStreamUrl,
          "streamingUrls.$.headers": headers
        }
      }
    );

    if (result.modifiedCount === 0) {
      logger.warn(`No streaming URL found with ID: ${id}`);
      return res.status(404).send("Streaming URL not found");
    }

    logger.info(`Updated streaming URL with ID: ${id}`);
    res.status(200).send({ success: true });
  } catch (error) {
    logger.error(`Error updating streaming URL: ${error.message}`);
    res.status(500).send(`Error updating streaming URL: ${error.message}`);
  }
});

// Token cache to prevent too many requests
const tokenCache = new Map();
// M3U8 content cache to store playlist content
const m3u8Cache = new Map();

// Direct token endpoint - simplest approach
app.get("/direct-token/:channelId", async (req, res) => {
  const { channelId } = req.params;
  if (!channelId) return res.status(400).send("Missing channel ID");

  // Check if we have a cached token that's less than 30 seconds old
  const now = Date.now();
  const cachedToken = tokenCache.get(channelId);
  if (cachedToken && (now - cachedToken.timestamp) < 30000) {
    logger.info(`Using cached token for channel ID: ${channelId}`);
    return res.send(cachedToken.url);
  }

  logger.info(`Direct token request for channel ID: ${channelId}`);

  // Get the WITV_BASE from the config
  const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);

  // Set up headers for the request
  const headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Referer': 'https://witv.skin/',
    'Origin': 'https://witv.skin',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Connection': 'keep-alive',
    'Range': 'bytes=0-',
    'Accept-Encoding': 'identity;q=1, *;q=0'
  };

  try {
    // Make a request to the auth URL
    const authUrl = `https://play.${witvBase}:443/auth/${channelId}.m3u8`;
    logger.info(`Trying auth URL: ${authUrl}`);

    try {
      const authResponse = await fetch(authUrl, {
        headers,
        redirect: 'manual',
        agent: new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 60000
        })
      });

      // Log the response status and headers for debugging
      logger.info(`Auth response status: ${authResponse.status}`);

      // Check if we got a redirect with a token
      if (authResponse.status === 302 || authResponse.status === 301) {
        let location = authResponse.headers.get('location');

        if (location) {
          // Fix the strange format with "8;;" at the beginning and duplicated URLs
          if (location.startsWith('8;;')) {
            location = location.substring(3);

            // Check if there's a duplicate URL at the end
            const parts = location.split('8;;');
            if (parts.length > 1) {
              location = parts[0];
            }
          }

          logger.info(`Got token URL: ${location}`);

          // Cache the token
          tokenCache.set(channelId, {
            url: location,
            timestamp: Date.now()
          });

          // Return the token URL directly as text
          return res.send(location);
        }
      } else if (authResponse.status === 200) {
        // If we got a 200 response, check if it's an HTML page or an actual m3u8 file
        const contentType = authResponse.headers.get('content-type');

        if (contentType && contentType.includes('text/html')) {
          logger.info('Auth URL returned HTML, not an m3u8 file');
        } else {
          // It might be a direct m3u8 file, try to read it
          try {
            const text = await authResponse.text();

            // Check if it looks like an m3u8 file
            if (text.includes('#EXTM3U')) {
              logger.info('Auth URL returned a direct m3u8 file');

              // Cache the direct URL
              tokenCache.set(channelId, {
                url: authUrl,
                timestamp: Date.now()
              });

              // Return the direct URL
              return res.send(authUrl);
            }
          } catch (textError) {
            logger.error(`Error reading auth response: ${textError.message}`);
          }
        }
      }
    } catch (authError) {
      logger.error(`Error with auth URL: ${authError.message}`);
    }

    // If we didn't get a redirect, try the live URL
    const liveUrl = `https://play.${witvBase}:443/live/2719C8919B250671368654F53F9595F1/${channelId}.m3u8`;
    logger.info(`Trying live URL: ${liveUrl}`);

    try {
      const liveResponse = await fetch(liveUrl, {
        headers,
        redirect: 'manual',
        agent: new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 60000
        })
      });

      // Log the response status and headers for debugging
      logger.info(`Live response status: ${liveResponse.status}`);

      // Check if we got a redirect with a token
      if (liveResponse.status === 302 || liveResponse.status === 301) {
        let location = liveResponse.headers.get('location');

        if (location) {
          // Fix the strange format with "8;;" at the beginning and duplicated URLs
          if (location.startsWith('8;;')) {
            location = location.substring(3);

            // Check if there's a duplicate URL at the end
            const parts = location.split('8;;');
            if (parts.length > 1) {
              location = parts[0];
            }
          }

          logger.info(`Got token URL from live endpoint: ${location}`);

          // Cache the token
          tokenCache.set(channelId, {
            url: location,
            timestamp: Date.now()
          });

          // Return the token URL directly as text
          return res.send(location);
        }
      } else if (liveResponse.status === 200) {
        // If we got a 200 response, check if it's an HTML page or an actual m3u8 file
        const contentType = liveResponse.headers.get('content-type');

        if (contentType && contentType.includes('text/html')) {
          logger.info('Live URL returned HTML, not an m3u8 file');
        } else {
          // It might be a direct m3u8 file, try to read it
          try {
            const text = await liveResponse.text();

            // Check if it looks like an m3u8 file
            if (text.includes('#EXTM3U')) {
              logger.info('Live URL returned a direct m3u8 file');

              // Cache the direct URL
              tokenCache.set(channelId, {
                url: liveUrl,
                timestamp: Date.now()
              });

              // Return the direct URL
              return res.send(liveUrl);
            }
          } catch (textError) {
            logger.error(`Error reading live response: ${textError.message}`);
          }
        }
      }
    } catch (liveError) {
      logger.error(`Error with live URL: ${liveError.message}`);
    }

    // If we still couldn't get a token, return an error
    return res.status(400).send("Could not get token");
  } catch (error) {
    logger.error(`Error getting token: ${error.message}`);
    return res.status(500).send(`Error getting token: ${error.message}`);
  }
});

// Endpoint to fetch token for witv.skin URLs
app.get("/proxy-token", async (req, res) => {
  const { url } = req.query;
  if (!url) return res.status(400).json({ success: false, error: "Missing URL parameter" });

  const decodedUrl = decodeURIComponent(url);
  logger.info(`Fetching token for URL: ${decodedUrl}`);

  // Check if this is a witv.skin URL
  const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);
  const isWitvSkin = decodedUrl.includes(witvBase) || decodedUrl.includes('play.witv');
  if (!isWitvSkin) {
    return res.status(400).json({ success: false, error: "Not a witv.skin URL" });
  }

  // Try a direct approach first - use the auth path
  // Extract the channel ID from the URL
  const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
  if (!channelIdMatch) {
    logger.warn(`Could not extract channel ID from URL: ${decodedUrl}`);
    return res.status(400).json({ success: false, error: "Could not extract channel ID from URL" });
  }

  const channelId = channelIdMatch[1];
  logger.info(`Extracted channel ID: ${channelId}`);

  // Try the auth URL directly first as it's more likely to work
  const authUrl = `https://play.${witvBase}:443/auth/${channelId}.m3u8`;
  logger.info(`Trying auth URL first: ${authUrl}`);

  try {
    // Set up headers for the request
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Referer': 'https://witv.skin/',
      'Origin': 'https://witv.skin',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Connection': 'keep-alive',
      'Range': 'bytes=0-',
      'Accept-Encoding': 'identity;q=1, *;q=0'
    };

    // Try the auth URL first
    const authResponse = await fetch(authUrl, {
      headers,
      redirect: 'manual',
      agent: authUrl.startsWith('https') ?
        new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 60000
        }) : undefined
    });

    // Log the auth response status and headers for debugging
    logger.info(`Auth URL response status: ${authResponse.status}`);
    logger.info(`Auth URL response headers: ${JSON.stringify([...authResponse.headers.entries()])}`);

    // Check if we got a redirect with a token
    if (authResponse.status === 302 || authResponse.status === 301 || authResponse.status === 307 || authResponse.status === 308) {
      let location = authResponse.headers.get('location');

      if (location) {
        // Fix the strange format with "8;;" at the beginning and duplicated URLs
        if (location.startsWith('8;;')) {
          location = location.substring(3);

          // Check if there's a duplicate URL at the end
          const parts = location.split('8;;');
          if (parts.length > 1) {
            location = parts[0];
          }
        }

        logger.info(`Got token URL from auth endpoint: ${location}`);

        // Get cookies from the response
        let cookies = null;
        if (authResponse.headers.get('set-cookie')) {
          cookies = authResponse.headers.get('set-cookie');
        }

        // Return the token URL and cookies
        return res.json({
          success: true,
          tokenUrl: location,
          cookies
        });
      }
    }

    // If auth URL didn't work, try the original URL
    logger.info(`Auth URL didn't return a token, trying original URL: ${decodedUrl}`);

    // Make a request to get the token from the original URL
    const response = await fetch(decodedUrl, {
      headers,
      redirect: 'manual',
      agent: decodedUrl.startsWith('https') ?
        new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 60000
        }) : undefined
    });

    // Log the response status and headers for debugging
    logger.info(`Token fetch response status: ${response.status}`);
    logger.info(`Token fetch response headers: ${JSON.stringify([...response.headers.entries()])}`);

    // Check if we got a redirect with a token
    if (response.status === 302 || response.status === 301 || response.status === 307 || response.status === 308) {
      let location = response.headers.get('location');

      if (location) {
        // Fix the strange format with "8;;" at the beginning and duplicated URLs
        if (location.startsWith('8;;')) {
          location = location.substring(3);

          // Check if there's a duplicate URL at the end
          const parts = location.split('8;;');
          if (parts.length > 1) {
            location = parts[0];
          }
        }

        // Get cookies from the response
        let cookies = null;
        if (response.headers.get('set-cookie')) {
          cookies = response.headers.get('set-cookie');
        }

        // Return the token URL and cookies
        return res.json({
          success: true,
          tokenUrl: location,
          cookies
        });
      }
    }

    // If we didn't get a redirect, check if we got HTML
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('text/html')) {
      const text = await response.text();
      logger.info(`Received HTML response: ${text.substring(0, 200)}...`);

      // Check if there's a redirect in the HTML
      const redirectMatch = text.match(/Location:\s*([^\s]+)/);
      if (redirectMatch && redirectMatch[1]) {
        const redirectUrl = redirectMatch[1].trim();
        logger.info(`Found redirect URL in HTML: ${redirectUrl}`);

        return res.json({
          success: true,
          tokenUrl: redirectUrl
        });
      }

      // Check if there's a meta refresh tag
      const metaRefreshMatch = text.match(/<meta[^>]*?http-equiv=["']?refresh["']?[^>]*?content=["']?\d+;\s*url=(.*?)["']/i);
      if (metaRefreshMatch && metaRefreshMatch[1]) {
        const refreshUrl = metaRefreshMatch[1].trim();
        logger.info(`Found meta refresh URL in HTML: ${refreshUrl}`);

        return res.json({
          success: true,
          tokenUrl: refreshUrl
        });
      }

      // If we couldn't find a redirect, return an error
      return res.status(400).json({
        success: false,
        error: "Received HTML response but couldn't find a redirect URL"
      });
    }

    // If we didn't get a redirect, try with a GET request
    const getResponse = await fetch(decodedUrl, {
      headers,
      redirect: 'manual',
      agent: decodedUrl.startsWith('https') ?
        new https.Agent({
          rejectUnauthorized: false,
          keepAlive: true,
          timeout: 60000
        }) : undefined
    });

    // Log the GET response status and headers for debugging
    logger.info(`Token GET response status: ${getResponse.status}`);
    logger.info(`Token GET response headers: ${JSON.stringify([...getResponse.headers.entries()])}`);

    // Check if we got a redirect with a token
    if (getResponse.status === 302 || getResponse.status === 301 || getResponse.status === 307 || getResponse.status === 308) {
      let location = getResponse.headers.get('location');

      if (location) {
        // Fix the strange format with "8;;" at the beginning and duplicated URLs
        if (location.startsWith('8;;')) {
          location = location.substring(3);

          // Check if there's a duplicate URL at the end
          const parts = location.split('8;;');
          if (parts.length > 1) {
            location = parts[0];
          }
        }

        // Get cookies from the response
        let cookies = null;
        if (getResponse.headers.get('set-cookie')) {
          cookies = getResponse.headers.get('set-cookie');
        }

        // Return the token URL and cookies
        return res.json({
          success: true,
          tokenUrl: location,
          cookies
        });
      }
    }

    // If we didn't get a redirect, check if we got HTML
    const getContentType = getResponse.headers.get('content-type');
    if (getContentType && getContentType.includes('text/html')) {
      const text = await getResponse.text();
      logger.info(`Received HTML response from GET: ${text.substring(0, 200)}...`);

      // Check if there's a redirect in the HTML
      const redirectMatch = text.match(/Location:\s*([^\s]+)/);
      if (redirectMatch && redirectMatch[1]) {
        const redirectUrl = redirectMatch[1].trim();
        logger.info(`Found redirect URL in HTML from GET: ${redirectUrl}`);

        return res.json({
          success: true,
          tokenUrl: redirectUrl
        });
      }

      // Check if there's a meta refresh tag
      const metaRefreshMatch = text.match(/<meta[^>]*?http-equiv=["']?refresh["']?[^>]*?content=["']?\d+;\s*url=(.*?)["']/i);
      if (metaRefreshMatch && metaRefreshMatch[1]) {
        const refreshUrl = metaRefreshMatch[1].trim();
        logger.info(`Found meta refresh URL in HTML from GET: ${refreshUrl}`);

        return res.json({
          success: true,
          tokenUrl: refreshUrl
        });
      }
    }

    // Try a direct approach - use the original URL with auth path
    // Extract the channel ID from the URL
    const channelIdMatch = decodedUrl.match(/\/(\d+)\.m3u8/);
    if (channelIdMatch) {
      const channelId = channelIdMatch[1];
      const authUrl = `https://play.witv.skin:443/auth/${channelId}.m3u8`;

      logger.info(`Trying direct auth URL: ${authUrl}`);

      // Make a request to the auth URL
      const authResponse = await fetch(authUrl, {
        headers,
        redirect: 'manual',
        agent: decodedUrl.startsWith('https') ?
          new https.Agent({
            rejectUnauthorized: false,
            keepAlive: true,
            timeout: 60000
          }) : undefined
      });

      // Check if we got a redirect with a token
      if (authResponse.status === 302 || authResponse.status === 301) {
        let location = authResponse.headers.get('location');

        if (location) {
          // Fix the strange format with "8;;" at the beginning and duplicated URLs
          if (location.startsWith('8;;')) {
            location = location.substring(3);

            // Check if there's a duplicate URL at the end
            const parts = location.split('8;;');
            if (parts.length > 1) {
              location = parts[0];
            }
          }

          logger.info(`Got token URL from auth endpoint: ${location}`);

          // Return the token URL
          return res.json({
            success: true,
            tokenUrl: location
          });
        }
      }
    }

    // If we still couldn't get a token, return an error
    return res.status(400).json({
      success: false,
      error: "Could not get token from URL"
    });
  } catch (error) {
    logger.error(`Error fetching token: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Error fetching token: ${error.message}`
    });
  }
});
// --- SPA Routes ---
const types = ["movies", "series", "anime", "livetv"];
types.forEach((type) => {
  app.get(`/${type}/:id`, (req, res) => {
    const filePath = path.join(
      __dirname,
      "public",
      "media.html"
    ); /* logger.info(...) */
    res.sendFile(filePath, (err) => {
      if (err) {
        logger.error(
          `Error sending media.html for /${type}/${req.params.id}: ${err.message}`
        );
        if (!res.headersSent) {
          res.status(err.status || 500).send("Error serving media page");
        }
      }
    });
  });
});
app.get("/config", (req, res) => {
  res.json({
    tmdbApiKey: process.env.TMDB_API_KEY,
  });
});
app.get("*", (req, res) => {
  const filePath = path.join(
    __dirname,
    "public",
    "index.html"
  ); /* logger.info(...) */
  res.sendFile(filePath, (err) => {
    if (err) {
      logger.error(`Error sending index.html for ${req.url}: ${err.message}`);
      if (!res.headersSent) {
        res.status(err.status || 500).send("Error serving application");
      }
    }
  });
});

// Performance monitoring endpoint
app.get('/api/performance', (req, res) => {
  try {
    const performanceData = {
      cache: cache.getStats(),
      rateLimiters: Object.fromEntries(
        Object.entries(rateLimiters).map(([name, limiter]) => [name, limiter.getStats()])
      ),
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };

    res.json(performanceData);
  } catch (error) {
    logger.error(`Performance monitoring error: ${error.message}`);
    res.status(500).json({ error: 'Failed to get performance data' });
  }
});

// Cache management endpoints
app.post('/api/cache/clear/:type?', (req, res) => {
  try {
    const { type } = req.params;
    cache.clear(type);
    res.json({ success: true, message: `Cache ${type || 'all'} cleared` });
  } catch (error) {
    logger.error(`Cache clear error: ${error.message}`);
    res.status(500).json({ error: 'Failed to clear cache' });
  }
});

app.get('/api/cache/stats', (req, res) => {
  try {
    res.json(cache.getStats());
  } catch (error) {
    logger.error(`Cache stats error: ${error.message}`);
    res.status(500).json({ error: 'Failed to get cache stats' });
  }
});

// Scraping endpoint
app.post('/api/scrape', async (req, res) => {
  try {
    const { mode = 'latest', type = 'all', pages = 5, enrichment = false, gemini = false } = req.body;

    logger.info(`Scraping request received: mode=${mode}, type=${type}, pages=${pages}, enrichment=${enrichment}, gemini=${gemini}`);

    // Validate parameters
    const validModes = ['latest', 'full', 'update'];
    const validTypes = ['all', 'movies', 'series', 'anime', 'livetv'];

    if (!validModes.includes(mode)) {
      return res.status(400).json({
        success: false,
        error: `Invalid mode. Must be one of: ${validModes.join(', ')}`
      });
    }

    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        error: `Invalid type. Must be one of: ${validTypes.join(', ')}`
      });
    }

    // Set environment variables for type-specific scraping
    if (type !== 'all') {
      // Set page limits based on type selection
      const pageLimit = parseInt(pages);

      // Reset all limits to 0 first
      process.env.PAGE_LIMIT_MOVIES = '0';
      process.env.PAGE_LIMIT_SERIES = '0';
      process.env.PAGE_LIMIT_ANIME = '0';
      process.env.PAGE_LIMIT_WITV = '0';

      // Set the selected type's limit
      switch (type) {
        case 'movies':
          process.env.PAGE_LIMIT_MOVIES = pageLimit.toString();
          break;
        case 'series':
          process.env.PAGE_LIMIT_SERIES = pageLimit.toString();
          break;
        case 'anime':
          process.env.PAGE_LIMIT_ANIME = pageLimit.toString();
          break;
        case 'livetv':
          process.env.PAGE_LIMIT_WITV = pageLimit.toString();
          break;
      }

      logger.info(`Set page limits for type '${type}': ${pageLimit} pages`);
    } else {
      // For 'all' type, set all limits to the specified pages value
      const pageLimit = parseInt(pages);
      process.env.PAGE_LIMIT_MOVIES = pageLimit.toString();
      process.env.PAGE_LIMIT_SERIES = pageLimit.toString();
      process.env.PAGE_LIMIT_ANIME = pageLimit.toString();
      process.env.PAGE_LIMIT_WITV = pageLimit.toString();

      logger.info(`Set page limits for all types: ${pageLimit} pages each`);
    }

    // Start scraping asynchronously
    const scrapingPromise = scrapeService.scrapeAll(mode);

    // Don't wait for completion, return immediately
    res.json({
      success: true,
      message: `Scraping started successfully with mode: ${mode}, type: ${type}, pages: ${pages}`,
      mode,
      type,
      pages: parseInt(pages),
      enrichment,
      gemini
    });

    // Handle the scraping promise in the background
    scrapingPromise
      .then(() => {
        logger.info(`Scraping completed successfully: mode=${mode}, type=${type}`);
      })
      .catch((error) => {
        logger.error(`Scraping failed: ${error.message}`, { mode, type, pages, error: error.stack });
      });

  } catch (error) {
    logger.error(`Scraping API error: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Failed to start scraping: ${error.message}`
    });
  }
});

// Apollo Server setup
const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: ({ req }) => ({
    req,
  }),
  introspection: process.env.NODE_ENV !== "production",
  formatError: (error) => {
    logger.error("GraphQL Execution Error:", {
      message: error.message,
      locations: error.locations,
      path: error.path,
      extensions: error.extensions,
      // *** CORRECTED LINE ***
      originalError: error.originalError
        ? error.originalError.message
        : undefined,
      // *** END CORRECTION ***
    });
    return error; // Return standard error structure
  },
});

// Modify initApolloServer to handle serverless environment
async function initApolloServer() {
  try {
    await mongoose.connect(mongoUri);
    logger.info("Connected to MongoDB");

    // Initialize Config model with default values
    await initializeConfig();

    await server.start();

    server.applyMiddleware({
      app,
      path: "/graphql",
      cors: false // We're handling CORS ourselves
    });

    if (process.env.VERCEL !== '1') {
      // Express server will be started by startWebSocketServer function

      // Start workers only in non-Vercel environment
      //updateBaseUrl();
      startWorkers().catch(err => {
        logger.error(`Failed to start workers: ${err.message}`);
      });

      const SCRAPE_ON_START = process.env.SCRAPE_ON_START === "true";
      const scrapeMode = process.env.SCRAPE_MODE || SCRAPE_MODE.LATEST;
      if (SCRAPE_ON_START) {
        logger.info(`Initial scrape scheduled (mode: ${scrapeMode})`);
        try {
          // Use async/await to properly handle the Promise
          await scrapeService.scrapeAll(scrapeMode);
          logger.info(`Initial scrape completed successfully (mode: ${scrapeMode})`);
        } catch (err) {
          logger.error(`Initial scrape failed: ${err.message}`, { stack: err.stack });
        }
      }
    }
  } catch (err) {
    logger.error(`Failed to initialize Apollo server: ${err.message}`);
    if (process.env.VERCEL !== '1') {
      process.exit(1);
    }
  }
}

/**
 * Initialize Config model with default values
 */
async function initializeConfig() {
  try {
    logger.info("Initializing Config model with default values");

    // Initialize default config values
    await Config.initializeDefaults({
      "WIFLIX_BASE": WIFLIX_BASE || "wiflix-max.cam",
      "FRENCH_ANIME_BASE": FRENCH_ANIME_BASE || "french-anime.com",
      "WITV_BASE": WITV_BASE || "witv.skin",
      "GRID_ITEMS_ENABLED": "true"
    });

    logger.info("Config model initialized successfully");
  } catch (error) {
    logger.error(`Error initializing Config model: ${error.message}`);
  }
}

// Initialize Apollo Server
initApolloServer();

// Export for Vercel
module.exports = app;

// Import the port killer utility
const { killProcessOnPort } = require('./scripts/kill_port');

// For render.com compatibility, use a single port for both HTTP and WebSocket
// This avoids cross-origin issues with WebSockets
const serverPort = process.env.PORT || 3001;

// Function to start the unified server
async function startUnifiedServer() {
  // Create a single HTTP server for both Express and WebSocket
  const server = http.createServer(app);

  // Initialize WebSocket on the same server
  initWebSocketServer(server);

  // Handle server errors
  server.on('error', async (error) => {
    logger.error(`Server listen error: ${error.message}`);

    // If the error is EADDRINUSE (port already in use), try to kill the process and retry
    if (error.code === 'EADDRINUSE') {
      logger.info(`Attempting to free up port ${serverPort}...`);

      const success = await killProcessOnPort(serverPort);
      if (success) {
        logger.info(`Successfully freed up port ${serverPort}, retrying...`);
        setTimeout(() => {
          server.listen(serverPort, () => {
            logger.info(`Server is running on http://localhost:${serverPort}`);
            logger.info(`GraphQL endpoint ready at http://localhost:${serverPort}/graphql`);
            logger.info(`WebSocket server is running on the same port`);
          });
        }, 1000); // Retry after 1 second
      } else {
        logger.error(`Could not free up port ${serverPort}. Please manually kill the process using this port.`);
        process.exit(1);
      }
    } else {
      logger.error(`Unrecoverable server error: ${error.message}`);
      process.exit(1);
    }
  });

  // Start the server
  server.listen(serverPort, () => {
    logger.info(`Server is running on http://localhost:${serverPort}`);
    logger.info(`GraphQL endpoint ready at http://localhost:${serverPort}/graphql`);
    logger.info(`WebSocket server is running on the same port`);

    // Set up keep-alive mechanism for Render.com free tier
    if (process.env.RENDER === 'true') {
      const serviceUrl = process.env.RENDER_EXTERNAL_URL || `http://localhost:${serverPort}`;
      const keepAlive = setupKeepAlive(serviceUrl);
      keepAlive.start();
      logger.info(`Keep-alive mechanism started for Render.com free tier - Pinging ${serviceUrl} every 14 minutes`);
    }
  });
}

// Start the unified server
startUnifiedServer();
