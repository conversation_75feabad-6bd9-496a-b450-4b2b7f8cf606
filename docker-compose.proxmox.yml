# Proxmox production override - uses 192.168.1.150
version: '3.8'

services:
  frontend:
    build:
      args:
        - NEXT_PUBLIC_API_URL=http://192.168.1.150:3001/graphql
        - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://192.168.1.150:3001/graphql
        - NEXT_PUBLIC_API_BASE_URL=http://192.168.1.150:3001
    environment:
      - NEXT_PUBLIC_API_URL=http://192.168.1.150:3001/graphql
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://192.168.1.150:3001/graphql
      - NEXT_PUBLIC_API_BASE_URL=http://192.168.1.150:3001
