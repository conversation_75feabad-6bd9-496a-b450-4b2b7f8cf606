// Complete helper functions for NetStream

export const getTitleWithState = (item, type) => {
  if (!item) return 'Unknown Title'

  // Return just the base title without language/episode data
  // The language/episode info will be handled separately by getLanguageEpisodeInfo
  const baseTitle = item.displayTitle || item.title || item.name || 'Unknown Title'

  return baseTitle
}

export const formatDuration = (duration) => {
  if (!duration) return 'Unknown'
  
  // Handle different duration formats
  if (typeof duration === 'number') {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }
  
  return duration
}

export const getImageUrl = (item, type = 'thumbnail') => {
  if (!item) return '/default-thumbnail.svg'

  // For anime, prioritize Jikan images
  if (item.__typename === 'Anime' || item.type === 'anime') {
    if (item.jikan?.images?.jpg?.large_image_url) {
      return item.jikan.images.jpg.large_image_url
    }
    if (item.jikan?.images?.jpg?.image_url) {
      return item.jikan.images.jpg.image_url
    }
  }

  // For TMDB content (movies/series)
  if (item.tmdb?.poster_path) {
    return `https://image.tmdb.org/t/p/w500${item.tmdb.poster_path}`
  }

  // Fallback to item properties with proxy for CORS handling
  const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'

  if (item.thumbnail && !item.thumbnail.includes('default-thumbnail')) {
    return `${apiBase}/proxy-image?url=${encodeURIComponent(item.thumbnail)}`
  }

  if (item.image && !item.image.includes('default-thumbnail')) {
    return `${apiBase}/proxy-image?url=${encodeURIComponent(item.image)}`
  }

  return '/default-thumbnail.svg'
}

// Enhanced thumbnail URL getter with correct hierarchy: TMDB -> Jikan -> Flemmix -> Default
export const getThumbnailUrl = (item) => {
  console.log('🖼️ getThumbnailUrl called with:', {
    itemId: item?.id,
    itemTitle: item?.title,
    tmdbPoster: item?.tmdb?.poster_path,
    jikanImage: item?.jikan?.images?.jpg?.large_image_url,
    thumbnail: item?.thumbnail,
    image: item?.image,
    typename: item?.__typename
  })

  if (!item) {
    console.log('🖼️ getThumbnailUrl: No item, returning default')
    return '/default-thumbnail.jpg'
  }

  // 1. TMDB images first (for all types) - from logs: item.tmdb.poster_path
  if (item.tmdb?.poster_path) {
    const tmdbUrl = `https://image.tmdb.org/t/p/w500${item.tmdb.poster_path}`
    console.log('🖼️ getThumbnailUrl: Using TMDB image:', tmdbUrl)
    return tmdbUrl
  }

  // 2. Jikan images (for anime) - from logs: item.jikan.images.jpg.large_image_url
  if (item.jikan?.images?.jpg?.large_image_url) {
    console.log('🖼️ getThumbnailUrl: Using Jikan large image:', item.jikan.images.jpg.large_image_url)
    return item.jikan.images.jpg.large_image_url
  }
  if (item.jikan?.images?.jpg?.image_url) {
    console.log('🖼️ getThumbnailUrl: Using Jikan image:', item.jikan.images.jpg.image_url)
    return item.jikan.images.jpg.image_url
  }

  // 3. Flemmix images (original source) - from logs: item.thumbnail and item.image
  // These are URLs like "https://wiflix-max.vip/checkimg.php?urli=stream-vf-0ddb-bdc2-7473-4872.jpg"
  // Use proxy for CORS handling like the original app
  const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'

  if (item.thumbnail && !item.thumbnail.includes('default-thumbnail')) {
    const proxyUrl = `${apiBase}/proxy-image?url=${encodeURIComponent(item.thumbnail)}`
    console.log('🖼️ getThumbnailUrl: Using proxied thumbnail:', proxyUrl)
    return proxyUrl
  }

  if (item.image && !item.image.includes('default-thumbnail')) {
    const proxyUrl = `${apiBase}/proxy-image?url=${encodeURIComponent(item.image)}`
    console.log('🖼️ getThumbnailUrl: Using proxied image:', proxyUrl)
    return proxyUrl
  }

  // 4. Default fallback - use .jpg like original app
  const defaultUrl = item.__typename === 'Anime' ? '/default-banner.jpg' : '/default-thumbnail.jpg'
  console.log('🖼️ getThumbnailUrl: Using default:', defaultUrl)
  return defaultUrl
}

export const formatRating = (item) => {
  if (!item) return 'N/A'

  if (item.tmdb?.vote_average && typeof item.tmdb.vote_average === 'number') {
    return item.tmdb.vote_average.toFixed(1)
  }

  if (item.jikan?.score && typeof item.jikan.score === 'number') {
    return item.jikan.score.toFixed(1)
  }

  return 'N/A'
}

export const getYear = (item) => {
  if (!item) return 'Unknown'

  // Try TMDB release date
  if (item.tmdb?.release_date) {
    const year = new Date(item.tmdb.release_date).getFullYear()
    if (!isNaN(year) && year > 1900) {
      return year.toString()
    }
  }

  // Try TMDB first air date (for series)
  if (item.tmdb?.first_air_date) {
    const year = new Date(item.tmdb.first_air_date).getFullYear()
    if (!isNaN(year) && year > 1900) {
      return year.toString()
    }
  }

  // Try Jikan aired date
  if (item.jikan?.aired?.from) {
    const year = new Date(item.jikan.aired.from).getFullYear()
    if (!isNaN(year) && year > 1900) {
      return year.toString()
    }
  }

  // Try metadata year
  if (item.metadata?.year) {
    const year = parseInt(item.metadata.year)
    if (!isNaN(year) && year > 1900) {
      return year.toString()
    }
  }

  return 'Unknown'
}

// Get language and episode info for carousel display
export const getLanguageEpisodeInfo = (item, type) => {
  if (!item) return []

  const itemType = item.__typename?.toLowerCase() || type
  const season = item.season || '1'

  if (itemType === 'anime') {
    const language = item.animeLanguage || 'VOSTFR'
    let latestEpisodeNum = 1

    if (item.episodes?.length > 0) {
      const episodeNumbers = item.episodes
        .map(ep => {
          const epNum = ep.episodeNumber
          if (typeof epNum === 'string') {
            if (epNum.toLowerCase() === 'film' || epNum.toLowerCase() === 'movie') {
              return 'Film'
            }
            const parsed = parseInt(epNum, 10)
            return !isNaN(parsed) ? parsed : null
          }
          return typeof epNum === 'number' ? epNum : null
        })
        .filter(n => n !== null)

      if (episodeNumbers.length > 0) {
        if (episodeNumbers.includes('Film')) {
          latestEpisodeNum = 'Film'
        } else {
          const numericEpisodes = episodeNumbers.filter(n => typeof n === 'number')
          if (numericEpisodes.length > 0) {
            latestEpisodeNum = Math.max(...numericEpisodes)
          }
        }
      }
    } else if (item.streamingUrls?.length > 0) {
      latestEpisodeNum = 'Film'
    }

    return [{
      language,
      season,
      episode: latestEpisodeNum,
      display: `${language} S${season}${latestEpisodeNum !== 'Film' ? ':E'+latestEpisodeNum : ''}`
    }]
  } else if (itemType === 'series') {
    const episodes = item.episodes || []
    const langEpisodes = {}

    episodes.forEach(ep => {
      (ep.streamingUrls || []).forEach(stream => {
        const lang = stream.language?.toUpperCase() || 'UNKNOWN'
        if (!langEpisodes[lang]) langEpisodes[lang] = 0
        const epNum = parseInt(ep.episodeNumber, 10)
        if (!isNaN(epNum) && epNum > langEpisodes[lang]) {
          langEpisodes[lang] = epNum
        }
      })
    })

    return Object.entries(langEpisodes)
      .sort(([langA], [langB]) => langA.localeCompare(langB))
      .map(([lang, latestEpNum]) => ({
        language: lang,
        season,
        episode: latestEpNum,
        display: `${lang} S${season}:E${latestEpNum}`
      }))
  }

  return []
}

export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
