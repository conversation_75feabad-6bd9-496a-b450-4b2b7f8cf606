"use client"
import { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'

// Cache for user data
let userCache = null
let lastFetchTime = 0
const CACHE_DURATION = 60000 // 1 minute cache

export default function AuthMenu() {
  const [user, setUser] = useState(userCache)
  const [loading, setLoading] = useState(!userCache)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const router = useRouter()
  const dropdownRef = useRef(null)

  useEffect(() => {
    async function fetchUser() {
      // Check if we have a valid cache
      const now = Date.now()
      if (userCache && now - lastFetchTime < CACHE_DURATION) {
        setUser(userCache)
        setLoading(false)
        return
      }

      try {
        // Check localStorage for user data first
        const userToken = localStorage.getItem('userToken')
        const userData = localStorage.getItem('userData')

        if (userToken && userData) {
          const user = JSON.parse(userData)
          userCache = user
          lastFetchTime = now
          setUser(user)
        } else {
          userCache = null
          setUser(null)
        }
      } catch (error) {
        console.error('Error fetching user:', error)
        userCache = null
        setUser(null)
      } finally {
        setLoading(false)
      }
    }
    fetchUser()
  }, [])

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = async () => {
    document.cookie = 'token=; Max-Age=0; path=/;'
    userCache = null // Clear cache on logout
    lastFetchTime = 0
    setUser(null)
    router.push('/login')
  }

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen)
  }

  if (loading) return null

  return (
    <div className="relative" ref={dropdownRef}>
      {user ? (
        <button onClick={toggleDropdown} className="focus:outline-none">
          {user.profileIcon ? (
            <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
              <i className={`${user.profileIcon} text-white`}></i>
            </div>
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
              <span className="text-sm text-white">{user.name?.charAt(0) || 'U'}</span>
            </div>
          )}
        </button>
      ) : (
        <button
          onClick={() => router.push('/login')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
        >
          Login
        </button>
      )}
      {user && dropdownOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50">
          <button
            onClick={() => {
              router.push('/profile')
              setDropdownOpen(false)
            }}
            className="w-full text-left px-4 py-2 text-white hover:bg-gray-700 rounded-t-lg"
          >
            Profile
          </button>
          {user.role === 'admin' && (
            <button
              onClick={() => {
                router.push('/admin')
                setDropdownOpen(false)
              }}
              className="w-full text-left px-4 py-2 text-white hover:bg-gray-700"
            >
              Admin Panel
            </button>
          )}
          <button
            onClick={() => {
              handleLogout()
              setDropdownOpen(false)
            }}
            className="w-full text-left px-4 py-2 text-white hover:bg-gray-700 rounded-b-lg"
          >
            Logout
          </button>
        </div>
      )}
    </div>
  )
}
