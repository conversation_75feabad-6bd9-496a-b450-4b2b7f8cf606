'use client'
import { useState, useEffect } from 'react'
import Carousel from '@/components/ui/Carousel'

export default function HomeCarousels() {
  console.log('🚀🚀🚀 HomeCarousels component loaded! 🚀🚀🚀')
  console.log('🚀 HomeCarousels: useState and useEffect should work now')

  const [mixedTrendingItems, setMixedTrendingItems] = useState([])
  const [latestMovies, setLatestMovies] = useState([])
  const [latestSeries, setLatestSeries] = useState([])
  const [latestAnime, setLatestAnime] = useState([])
  const [mixedWishlist, setMixedWishlist] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  console.log('🚀 HomeCarousels: State initialized:', { loading, itemsCount: mixedTrendingItems.length })

  // Fetch all content from Next.js API routes
  useEffect(() => {
    const fetchAllContent = async () => {
      try {
        console.log('🔄 HomeCarousels: Starting fetch for all content...')
        setLoading(true)
        setError(null)

        // Fetch all content in parallel using GraphQL
        const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'

        const trendingQuery = `
          query GetTrending($limit: Int) {
            trending(limit: $limit) {
              id
              title
              displayTitle
              thumbnail
              image
              trendingRank
              trendingRankDisplay
              __typename
            }
          }
        `

        const latestMoviesQuery = `
          query GetLatestMovies($limit: Int, $excludeAncien: Boolean) {
            latestMovies(limit: $limit, excludeAncien: $excludeAncien) {
              id
              title
              displayTitle
              thumbnail
              image
              __typename
            }
          }
        `

        const latestSeriesQuery = `
          query GetLatestSeries($limit: Int) {
            latestSeries(limit: $limit) {
              id
              title
              displayTitle
              thumbnail
              image
              __typename
            }
          }
        `

        const latestAnimeQuery = `
          query GetLatestAnime($limit: Int) {
            latestAnime(limit: $limit) {
              id
              title
              displayTitle
              thumbnail
              image
              __typename
            }
          }
        `

        const [trendingRes, moviesRes, seriesRes, animeRes] = await Promise.all([
          fetch(`${apiBase}/graphql`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query: trendingQuery, variables: { limit: 20 } })
          }),
          fetch(`${apiBase}/graphql`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query: latestMoviesQuery, variables: { limit: 20, excludeAncien: true } })
          }),
          fetch(`${apiBase}/graphql`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query: latestSeriesQuery, variables: { limit: 20 } })
          }),
          fetch(`${apiBase}/graphql`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query: latestAnimeQuery, variables: { limit: 20 } })
          })
        ])

        console.log('📡 HomeCarousels: All responses received')

        // Parse all responses
        const [trending, movies, series, anime] = await Promise.all([
          trendingRes.json(),
          moviesRes.json(),
          seriesRes.json(),
          animeRes.json()
        ])

        console.log('📦 HomeCarousels: All GraphQL data parsed')

        // Set data if successful
        if (trending.data?.trending) {
          console.log('✅ HomeCarousels: Setting trending data with', trending.data.trending.length, 'items')
          setMixedTrendingItems(trending.data.trending)
        }

        if (movies.data?.latestMovies) {
          console.log('✅ HomeCarousels: Setting movies data with', movies.data.latestMovies.length, 'items')
          setLatestMovies(movies.data.latestMovies)
        }

        if (series.data?.latestSeries) {
          console.log('✅ HomeCarousels: Setting series data with', series.data.latestSeries.length, 'items')
          setLatestSeries(series.data.latestSeries)
        }

        if (anime.data?.latestAnime) {
          console.log('✅ HomeCarousels: Setting anime data with', anime.data.latestAnime.length, 'items')
          setLatestAnime(anime.data.latestAnime)
        }

        // Wishlist is empty for now
        setMixedWishlist([])

        console.log('🏁 HomeCarousels: Setting loading to false')
        setLoading(false)

      } catch (error) {
        console.error('💥 HomeCarousels: Fetch error:', error)
        setError(error.message)
        setLoading(false)
      }
    }

    fetchAllContent()
  }, [])

  // Debug logging for render
  console.log('🎨 HomeCarousels render:', {
    loading,
    error,
    trendingCount: mixedTrendingItems.length,
    moviesCount: latestMovies.length,
    seriesCount: latestSeries.length,
    animeCount: latestAnime.length,
    wishlistCount: mixedWishlist.length
  })

  if (loading) {
    console.log('⏳ HomeCarousels: Showing loading state')
    return (
      <div className="flex justify-center py-8">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-2xl text-blue-500 mb-2"></i>
          <p className="text-gray-400">Loading content...</p>
        </div>
      </div>
    )
  }

  if (error) {
    console.log('❌ HomeCarousels: Showing error state:', error)
    return (
      <div className="flex justify-center py-8">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-2xl text-red-500 mb-2"></i>
          <p className="text-red-400">Error: {error}</p>
        </div>
      </div>
    )
  }

  // Check if we have any content at all
  const hasAnyContent = mixedTrendingItems.length > 0 || latestMovies.length > 0 ||
                       latestSeries.length > 0 || latestAnime.length > 0 || mixedWishlist.length > 0

  if (!hasAnyContent) {
    console.log('📭 HomeCarousels: Showing empty state')
    return (
      <div className="flex justify-center py-8">
        <div className="text-center">
          <i className="fas fa-film text-2xl text-gray-500 mb-2"></i>
          <p className="text-gray-400">No content available</p>
        </div>
      </div>
    )
  }

  console.log('🎬 HomeCarousels: Rendering all carousels')
  return (
    <div className="space-y-8">
      {/* Mixed Trending Carousel */}
      {mixedTrendingItems.length > 0 && (
        <Carousel
          title="Trending Now"
          items={mixedTrendingItems}
          type="mixed"
        />
      )}

      {/* Mixed Wishlist Carousel */}
      {mixedWishlist.length > 0 && (
        <Carousel
          title="My Wishlist"
          items={mixedWishlist}
          type="mixed"
        />
      )}

      {/* Latest Movies Carousel */}
      {latestMovies.length > 0 && (
        <Carousel
          title="Latest Movies"
          items={latestMovies}
          type="movies"
        />
      )}

      {/* Latest Series Carousel */}
      {latestSeries.length > 0 && (
        <Carousel
          title="Latest Series"
          items={latestSeries}
          type="series"
        />
      )}

      {/* Latest Anime Carousel */}
      {latestAnime.length > 0 && (
        <Carousel
          title="Latest Anime"
          items={latestAnime}
          type="anime"
        />
      )}
    </div>
  )
}
