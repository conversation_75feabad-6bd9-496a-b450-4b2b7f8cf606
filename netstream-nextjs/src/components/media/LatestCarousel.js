'use client'
import { useState, useEffect } from 'react'
import Carousel from '@/components/ui/Carousel'

// GraphQL queries for latest content
const GET_LATEST_MOVIES = `
  query GetLatestMovies($limit: Int, $page: Int, $excludeAncien: Boolean) {
    latestMovies(limit: $limit, page: $page, excludeAncien: $excludeAncien) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

const GET_LATEST_SERIES = `
  query GetLatestSeries($limit: Int, $page: Int) {
    latestSeries(limit: $limit, page: $page) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

const GET_LATEST_ANIME = `
  query GetLatestAnime($limit: Int, $page: Int) {
    latestAnime(limit: $limit, page: $page) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      jikan {
        mal_id
        title {
          default
          english
          japanese
        }
        synopsis
        images {
          jpg {
            image_url
            small_image_url
            large_image_url
          }
        }
        score
        status
      }
    }
  }
`

export default function LatestCarousel({ type }) {
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchLatestItems = async () => {
      try {
        console.log(`🔄 LatestCarousel: Fetching latest ${type} via GraphQL...`)
        setLoading(true)
        setError(null)

        const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
        const query = type === 'movies' ? GET_LATEST_MOVIES :
                     type === 'series' ? GET_LATEST_SERIES :
                     GET_LATEST_ANIME

        const variables = type === 'movies' ?
          { limit: 20, page: 1, excludeAncien: true } :
          { limit: 20, page: 1 }

        const response = await fetch(`${apiBase}/graphql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            variables
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log(`📦 LatestCarousel: GraphQL result for ${type}:`, result)

        if (result.data) {
          const dataKey = type === 'movies' ? 'latestMovies' :
                         type === 'series' ? 'latestSeries' :
                         'latestAnime'
          const items = result.data[dataKey] || []
          console.log(`✅ LatestCarousel: Setting ${items.length} latest ${type} items`)
          setItems(items)
        } else {
          console.error(`❌ LatestCarousel: GraphQL error for ${type}:`, result.errors)
          setError(result.errors?.[0]?.message || 'GraphQL error')
        }

        setLoading(false)

      } catch (error) {
        console.error(`💥 LatestCarousel: Fetch error for ${type}:`, error)
        setError(error.message)
        setLoading(false)
      }
    }

    fetchLatestItems()
  }, [type])

  if (loading) {
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-green-500 mr-4 rounded"></span>
            Latest {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <div className="text-center">
            <i className="fas fa-spinner fa-spin text-2xl text-green-500 mb-2"></i>
            <p className="text-gray-400">Loading latest {type}...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    console.error(`LatestCarousel error for ${type}:`, error)
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-green-500 mr-4 rounded"></span>
            Latest {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <div className="text-center">
            <i className="fas fa-exclamation-triangle text-2xl text-red-500 mb-2"></i>
            <p className="text-red-400">Error loading latest {type}: {error}</p>
          </div>
        </div>
      </div>
    )
  }

  if (!items.length) {
    return (
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white flex items-center">
            <span className="w-1 h-8 bg-green-500 mr-4 rounded"></span>
            Latest {type.charAt(0).toUpperCase() + type.slice(1)}
          </h2>
        </div>
        <div className="flex justify-center py-8">
          <div className="text-center">
            <i className="fas fa-film text-2xl text-gray-500 mb-2"></i>
            <p className="text-gray-400">No latest {type} available</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mb-12">
      <Carousel
        title={`Latest ${type.charAt(0).toUpperCase() + type.slice(1)}`}
        items={items}
        type={type}
      />
    </div>
  )
}
