'use client'
import React, { useState } from 'react'
import { useQuery, useMutation } from '@apollo/client'
import { GET_DATABASE_STATS, GET_CONTENT_OVERVIEW, SEARCH_MEDIA, DELETE_ITEM, UPDATE_ITEM, ADMIN_LOGIN } from '@/lib/queries'
import { useAdminContext } from '@/context/AdminContext'
import { useUser } from '@/context/UserContext'
import { AdminContentCard } from './AdminContentCard'
// Import new tab components
import PerformanceTab from '@/app/admin/components/PerformanceTab'
import ScrapingTab from '@/app/admin/components/ScrapingTab'
import UsersTab from '@/app/admin/components/UsersTab'
import SystemTab from '@/app/admin/components/SystemTab'
import ConfigurationTab from '@/app/admin/components/ConfigurationTab'
import LogsTab from '@/app/admin/components/LogsTab'

const TABS = [
  { key: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
  { key: 'content', label: 'Content', icon: 'fas fa-film' },
  { key: 'performance', label: 'Performance', icon: 'fas fa-chart-line' },
  { key: 'scraping', label: 'Scraping', icon: 'fas fa-spider' },
  { key: 'users', label: 'Users', icon: 'fas fa-users' },
  { key: 'system', label: 'System', icon: 'fas fa-server' },
  { key: 'config', label: 'Config', icon: 'fas fa-cog' },
  { key: 'logs', label: 'Logs', icon: 'fas fa-file-alt' },
]

export default function AdminPanel() {
  const { user: adminContextUser, logout: adminContextLogout } = useAdminContext()
  const { user: regularUser, logout: regularLogout } = useUser()
  const { data: dbStatsData, loading: dbLoading, error: dbError } = useQuery(GET_DATABASE_STATS, {
    errorPolicy: 'all',
    onError: (error) => {
      console.warn('Database stats query error:', error.message)
    }
  })
  const { data: overviewData, loading: overviewLoading, error: overviewError } = useQuery(GET_CONTENT_OVERVIEW, {
    errorPolicy: 'all',
    onError: (error) => {
      console.warn('Content overview query error:', error.message)
    }
  })
  const [activeTab, setActiveTab] = useState('dashboard')

  // Handle logout for both systems
  const handleLogout = () => {
    console.log('AdminPanel logout clicked')
    try {
      // Logout from AdminContext
      adminContextLogout()
      console.log('AdminContext logout called')

      // If we're on the /admin page, also logout from regular auth
      if (regularUser && regularLogout) {
        regularLogout()
        console.log('Regular logout called')
      }

      // Redirect to home
      setTimeout(() => {
        window.location.href = '/'
      }, 100)
    } catch (error) {
      console.error('Logout error:', error)
      // Force redirect even if logout fails
      window.location.href = '/'
    }
  }
  const [deleteItem] = useMutation(DELETE_ITEM)
  const [updateItem] = useMutation(UPDATE_ITEM)
  
  const handleDelete = async (id, type) => {
    if (!window.confirm('Are you sure you want to delete this item?')) return;
    try {
      if (!user?.token) {
        throw new Error('No admin token available');
      }
      await deleteItem({ variables: { id, type, adminToken: user.token } });
      // Refresh search or list
      refetch();
    } catch (err) {
      alert('Delete failed: ' + err.message);
    }
  }

  // handleUpdate removed - ContentTab has its own implementation

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-white">Admin Panel</h2>
          <div className="flex space-x-2">
            {TABS.map(tab => (
              <button
                key={tab.key}
                className={`px-3 py-2 rounded-lg flex items-center space-x-2 text-sm font-semibold transition-colors ${activeTab === tab.key ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
                onClick={() => setActiveTab(tab.key)}
              >
                <i className={`${tab.icon}`}></i>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
        <button
          onClick={handleLogout}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <i className="fas fa-sign-out-alt mr-2"></i>
          Logout
        </button>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'dashboard' && (
          <DashboardTab
            dbStats={dbStatsData?.databaseStats}
            dbLoading={dbLoading}
            dbError={dbError}
            overview={overviewData?.contentOverview}
            overviewLoading={overviewLoading}
            overviewError={overviewError}
          />
        )}
        {activeTab === 'content' && <ContentTab />}
        {activeTab === 'performance' && <PerformanceTab />}
        {activeTab === 'scraping' && <ScrapingTab />}
        {activeTab === 'users' && <UsersTab />}
        {activeTab === 'system' && <SystemTab />}
        {activeTab === 'config' && <ConfigurationTab />}
        {activeTab === 'logs' && <LogsTab />}
      </div>
    </div>
  )
}

function DashboardTab({ dbStats, dbLoading, dbError, overview, overviewLoading, overviewError }) {
  if (dbLoading || overviewLoading) {
    return (
      <div className="flex justify-center py-8">
        <i className="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
      </div>
    )
  }
  if (dbError || overviewError) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading admin stats: {dbError?.message || overviewError?.message}
      </div>
    )
  }
  return (
    <>
      {/* Database Stats Grid */}
      {dbStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-film text-blue-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Movies</h3>
                <p className="text-3xl font-bold text-blue-500">{dbStats.movies || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-tv text-green-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Series</h3>
                <p className="text-3xl font-bold text-green-500">{dbStats.series || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-dragon text-purple-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Anime</h3>
                <p className="text-3xl font-bold text-purple-500">{dbStats.anime || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="fas fa-broadcast-tower text-red-500 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-semibold text-white">Live TV</h3>
                <p className="text-3xl font-bold text-red-500">{dbStats.livetv || 0}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Content Overview */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Recently Added</h3>
            <p className="text-2xl font-bold text-blue-400">{overview.recentlyAdded}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Trending</h3>
            <p className="text-2xl font-bold text-green-400">{overview.trending}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Most Watched</h3>
            <p className="text-2xl font-bold text-purple-400">{overview.mostWatched}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Total Views</h3>
            <p className="text-2xl font-bold text-red-400">{overview.totalViews}</p>
          </div>
        </div>
      )}
    </>
  )
}

function ContentTab() {
  const [query, setQuery] = useState('')
  const [type, setType] = useState('')
  const [adminToken, setAdminToken] = useState(null)
  const [isGettingToken, setIsGettingToken] = useState(false)
  const adminContext = useAdminContext()
  const { user: regularUser } = useUser()
  const { user: adminUser } = adminContext

  // Simple token getter - just get a token directly when needed
  const getAdminToken = async () => {
    if (adminToken) return adminToken
    if (adminUser?.token) return adminUser.token

    setIsGettingToken(true)
    try {
      console.log('Getting admin token directly...')
      const { data } = await adminLogin({ variables: { adminKey: 'namery' } })
      if (data.adminLogin.success) {
        setAdminToken(data.adminLogin.token)
        console.log('Got admin token successfully')
        return data.adminLogin.token
      }
    } catch (error) {
      console.error('Failed to get admin token:', error)
    }
    setIsGettingToken(false)
    return null
  }
  const { data, loading, error, refetch } = useQuery(SEARCH_MEDIA, {
    variables: { query, page: 1, limit: 20 },
    skip: !query
  })
  const [deleteItem] = useMutation(DELETE_ITEM)
  const [updateItem] = useMutation(UPDATE_ITEM)
  const [adminLogin] = useMutation(ADMIN_LOGIN)

  const items = data?.search?.items || []
  const filtered = type ? items.filter(i => i.__typename?.toLowerCase() === type) : items

  const handleUpdate = async (editedItem) => {
    try {
      console.log('=== USING EXISTING ADMIN TOKEN ===')

      // Use the existing admin token from AdminContext
      const token = adminUser?.token
      console.log('Admin user from context:', adminUser)
      console.log('Token from admin context:', token ? 'EXISTS' : 'NULL')

      if (!token) {
        alert('NO ADMIN TOKEN FOUND - Please login as admin first')
        return
      }

      console.log('PROCEEDING WITH UPDATE USING TOKEN')

      // SIMPLIFIED TEST - Only send basic fields
      const itemInput = {
        title: editedItem.title,
        displayTitle: editedItem.displayTitle,
      }

      console.log('SENDING UPDATE MUTATION WITH VARIABLES:', {
        id: editedItem.id,
        type: editedItem.__typename.toUpperCase(),
        input: itemInput,
        adminToken: token ? 'TOKEN_PRESENT' : 'NO_TOKEN'
      })

      // BYPASS APOLLO CLIENT - USE DIRECT FETCH
      console.log('TESTING WITH DIRECT FETCH INSTEAD OF APOLLO CLIENT')

      const graphqlQuery = {
        query: `
          mutation UpdateItem($id: ID!, $type: ItemType!, $input: ItemInput!, $adminToken: String!) {
            updateItem(id: $id, type: $type, input: $input, adminToken: $adminToken) {
              success
              message
            }
          }
        `,
        variables: {
          id: editedItem.id,
          type: editedItem.__typename.toUpperCase(),
          input: itemInput,
          adminToken: token
        }
      }

      console.log('DIRECT FETCH QUERY:', JSON.stringify(graphqlQuery, null, 2))

      const response = await fetch('http://localhost:3001/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(graphqlQuery)
      })

      console.log('DIRECT FETCH RESPONSE STATUS:', response.status)
      const result = await response.json()
      console.log('DIRECT FETCH RESULT:', result)

      if (!result.data?.updateItem?.success) {
        throw new Error(result.data?.updateItem?.message || result.errors?.[0]?.message || 'Update failed')
      }
      refetch() // Refresh the list after update
    } catch (error) {
      console.error('Error updating item:', error)
      throw error
    }
  }

  const handleDelete = async (id, type) => {
    if (!window.confirm('Are you sure you want to delete this item?')) return;
    try {
      console.log('=== USING EXISTING ADMIN TOKEN FOR DELETE ===')

      // Use the existing admin token from AdminContext
      const token = adminUser?.token
      console.log('Token from admin context:', token ? 'EXISTS' : 'NULL')

      if (!token) {
        alert('NO ADMIN TOKEN FOUND - Please login as admin first')
        return
      }

      console.log('PROCEEDING WITH DELETE USING EXISTING TOKEN')

      // BYPASS APOLLO CLIENT - USE DIRECT FETCH (same as update)
      console.log('TESTING DELETE WITH DIRECT FETCH INSTEAD OF APOLLO CLIENT')

      const graphqlQuery = {
        query: `
          mutation DeleteItem($id: ID!, $type: ItemType!, $adminToken: String!) {
            deleteItem(id: $id, type: $type, adminToken: $adminToken) {
              success
              message
            }
          }
        `,
        variables: {
          id,
          type: type.toUpperCase(),
          adminToken: token
        }
      }

      console.log('DIRECT FETCH DELETE QUERY:', JSON.stringify(graphqlQuery, null, 2))

      const response = await fetch('http://localhost:3001/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(graphqlQuery)
      })

      console.log('DIRECT FETCH DELETE RESPONSE STATUS:', response.status)
      const result = await response.json()
      console.log('DIRECT FETCH DELETE RESULT:', result)

      if (!result.data?.deleteItem?.success) {
        throw new Error(result.data?.deleteItem?.message || result.errors?.[0]?.message || 'Delete failed')
      }

      // Refresh search or list
      refetch();
    } catch (err) {
      console.error('ContentTab handleDelete - Error:', err);
      alert('Delete failed: ' + err.message);
    }
  }

  return (
    <div className="space-y-4">
      {/* Admin Login Status */}
      <div style={{backgroundColor: adminUser?.token ? '#16a34a' : '#dc2626', padding: '16px', borderRadius: '8px', marginBottom: '16px'}}>
        <h3 style={{color: 'white', fontWeight: 'bold', marginBottom: '8px'}}>ADMIN LOGIN STATUS</h3>
        <p style={{color: 'white'}}>Status: {adminUser?.token ? 'LOGGED IN' : 'NOT LOGGED IN'}</p>
        <p style={{color: 'white'}}>Token: {adminUser?.token ? 'EXISTS' : 'NULL'}</p>
        {!adminUser?.token && (
          <button
            onClick={async () => {
              try {
                const { data } = await adminLogin({ variables: { adminKey: 'namery' } })
                if (data.adminLogin.success) {
                  // Use AdminContext login method to properly store the token
                  const loginSuccess = await adminContext.login(data.adminLogin.token)
                  if (loginSuccess) {
                    alert('ADMIN LOGIN SUCCESSFUL!')
                  } else {
                    alert('TOKEN VALIDATION FAILED')
                  }
                } else {
                  alert('LOGIN FAILED: ' + data.adminLogin.message)
                }
              } catch (error) {
                alert('ERROR: ' + error.message)
              }
            }}
            style={{backgroundColor: '#2563eb', color: 'white', padding: '8px 12px', borderRadius: '4px', marginTop: '8px', border: 'none', cursor: 'pointer'}}
          >
            LOGIN AS ADMIN
          </button>
        )}
        {adminUser?.token && (
          <button
            onClick={() => {
              adminContext.logout()
              alert('LOGGED OUT')
            }}
            style={{backgroundColor: '#dc2626', color: 'white', padding: '8px 12px', borderRadius: '4px', marginTop: '8px', border: 'none', cursor: 'pointer'}}
          >
            LOGOUT
          </button>
        )}
      </div>

      <div className="flex flex-col md:flex-row md:items-end gap-4">
        <div>
          <label className="block text-sm text-gray-300 mb-1">Search</label>
          <input
            className="bg-gray-800 text-white px-3 py-2 rounded w-64"
            placeholder="Title or keyword..."
            value={query}
            onChange={e => setQuery(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && refetch()}
          />
        </div>
        <div>
          <label className="block text-sm text-gray-300 mb-1">Type</label>
          <select
            className="bg-gray-800 text-white px-3 py-2 rounded"
            value={type}
            onChange={e => setType(e.target.value)}
          >
            <option value="">All</option>
            <option value="movie">Movies</option>
            <option value="series">Series</option>
            <option value="anime">Anime</option>
            <option value="livetv">Live TV</option>
          </select>
        </div>
        <button
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          onClick={() => refetch()}
        >
          <i className="fas fa-search mr-2"></i>Search
        </button>
        <button
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded ml-auto"
          // onClick={handleAddContent}
        >
          <i className="fas fa-plus mr-2"></i>Add Content
        </button>
      </div>
      {loading && <div className="text-gray-400">Loading...</div>}
      {error && <div className="text-red-500">Error: {error.message}</div>}
      <div className="flex flex-col mt-4">
        {filtered.map(item => (
          <AdminContentCard key={item.id} item={item} onDelete={handleDelete} onUpdate={handleUpdate} />
        ))}
      </div>
      {filtered.length === 0 && !loading && query && (
        <div className="text-gray-400 mt-8">No results found.</div>
      )}
    </div>
  )
}
