'use client';

import { useState, useEffect } from 'react';
// Removed CSS module import - using Tailwind classes directly

export default function ConfigurationTab() {
  const [configData, setConfigData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingKey, setEditingKey] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchConfigData();
  }, []);

  const fetchConfigData = async () => {
    try {
      setIsLoading(true);
      // Get admin token for authentication
      const adminToken = localStorage.getItem('adminToken');

      // Use backend to get real config data from MongoDB config collection
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
      const url = `${apiBase}/graphql?t=${Date.now()}`;
      console.log('🌐 Making request to:', url);
      console.log('🔑 Admin token:', adminToken ? 'PRESENT' : 'NULL');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-token': adminToken,
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify({
          query: `
            query GetAllConfig {
              config {
                tmdbApiKey
                wiflixBase
                frenchAnimeBase
                witvBase
                geminiApiKey
                scrapingConfig {
                  pages {
                    movies
                    series
                    anime
                    livetv
                  }
                  enrichment
                  gemini
                }
              }
            }
          `
        })
      });

      if (!response.ok) throw new Error(`Config API returned ${response.status}`);
      const result = await response.json();

      console.log('Config API Response:', result); // Debug log
      console.log('Full config object:', result.data?.config); // Debug full config
      console.log('Raw JSON response:', JSON.stringify(result, null, 2)); // Debug raw JSON
      console.log('Gemini API Key from response:', result.data?.config?.geminiApiKey); // Debug gemini specifically
      console.log('All config keys:', Object.keys(result.data?.config || {})); // Debug all keys

      if (result.data?.config) {
        const config = result.data.config;
        // Transform GraphQL config to match UI structure
        setConfigData({
          scraping: {
            wiflix: config.wiflixBase || 'wiflix-max.vip',
            frenchAnime: config.frenchAnimeBase || 'french-anime.com',
            witv: config.witvBase || 'witv.skin'
          },
          apis: {
            tmdb: config.tmdbApiKey || 'not_set',
            gemini: config.geminiApiKey || 'not_set'
          },
          scrapingConfig: config.scrapingConfig || {
            pages: { movies: 2, series: 2, anime: 2, livetv: 4 },
            enrichment: true,
            gemini: true
          },
          server: {
            port: 3001,
            environment: 'production',
            logLevel: 'info'
          }
        });
        console.log('Set config data - APIs:', {
          tmdb: config.tmdbApiKey || 'not_set',
          gemini: config.geminiApiKey || 'not_set'
        }); // Debug what we're setting
      } else {
        throw new Error(result.errors?.[0]?.message || 'Failed to fetch configuration');
      }
      setError(null);
    } catch (err) {
      console.error('Config fetch error:', err); // Debug log
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (key, currentValue) => {
    setEditingKey(key);
    setEditValue(currentValue);
  };

  // Map display keys to database keys
  const getDbKey = (displayKey) => {
    const keyMap = {
      'wiflix': 'WIFLIX_BASE',
      'frenchAnime': 'FRENCH_ANIME_BASE',
      'witv': 'WITV_BASE',
      'tmdb': 'TMDB_API_KEY',
      'gemini': 'GEMINI_API_KEY'
    };
    return keyMap[displayKey] || displayKey;
  };

  const handleSave = async () => {
    if (!editingKey || editValue.trim() === '') return;

    try {
      setIsSaving(true);
      const adminToken = localStorage.getItem('adminToken');
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'

      const response = await fetch(`${apiBase}/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-token': adminToken
        },
        body: JSON.stringify({
          query: `
            mutation UpdateBaseUrl($key: String!, $value: String!, $adminToken: String!) {
              updateBaseUrl(key: $key, value: $value, adminToken: $adminToken) {
                success
                message
                key
                value
              }
            }
          `,
          variables: {
            key: getDbKey(editingKey),
            value: editValue.trim(),
            adminToken
          }
        })
      });

      if (!response.ok) throw new Error(`Config API returned ${response.status}`);
      const result = await response.json();

      if (result.data?.updateBaseUrl?.success) {
        // Clear editing state first
        setEditingKey(null);
        setEditValue('');

        // Refetch config data from server to ensure consistency
        await fetchConfigData();

        alert('Configuration updated successfully!');
      } else {
        throw new Error(result.error || 'Failed to update configuration');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingKey(null);
    setEditValue('');
  };

  const toggleSystemSetting = async (setting) => {
    try {
      const response = await fetch('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          key: setting,
          value: !configData.systemSettings[setting]
        })
      });

      if (!response.ok) throw new Error(`Config API returned ${response.status}`);
      const result = await response.json();
      
      if (result.success) {
        setConfigData(prev => ({
          ...prev,
          systemSettings: {
            ...prev.systemSettings,
            [setting]: !prev.systemSettings[setting]
          }
        }));
        alert(`${setting} ${!configData.systemSettings[setting] ? 'enabled' : 'disabled'} successfully!`);
      } else {
        throw new Error(result.error || 'Failed to update setting');
      }
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div>
      <div className="flex gap-4 mb-6">
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center gap-2" onClick={fetchConfigData}>
          <i className="fas fa-sync"></i> Refresh Data
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Base URLs Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-link text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Base URLs</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : configData?.scraping ? (
            <div className="space-y-4">
              {Object.entries(configData.scraping).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white capitalize">{key}</div>
                    {editingKey === key ? (
                      <div className="flex items-center mt-1">
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="flex-1 bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm"
                          placeholder="Enter new value"
                        />
                        <button
                          onClick={handleSave}
                          disabled={isSaving}
                          className="ml-2 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
                        >
                          {isSaving ? 'Saving...' : 'Save'}
                        </button>
                        <button
                          onClick={handleCancel}
                          className="ml-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-400 mt-1">{value}</div>
                    )}
                  </div>
                  {editingKey !== key && (
                    <button
                      onClick={() => handleEdit(key, value)}
                      className="ml-4 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      Edit
                    </button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No base URLs configuration available</div>
          )}
        </div>

        {/* API Keys Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-key text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">API Keys</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : configData?.apis ? (
            <div className="space-y-4">
              {Object.entries(configData.apis).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white">{key.toUpperCase()}</div>
                    {editingKey === key ? (
                      <div className="flex items-center mt-1">
                        <input
                          type="password"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="flex-1 bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm"
                          placeholder="Enter API key"
                        />
                        <button
                          onClick={handleSave}
                          disabled={isSaving}
                          className="ml-2 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
                        >
                          {isSaving ? 'Saving...' : 'Save'}
                        </button>
                        <button
                          onClick={handleCancel}
                          className="ml-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-400 mt-1">{value}</div>
                    )}
                  </div>
                  {editingKey !== key && (
                    <button
                      onClick={() => handleEdit(key, '')}
                      className="ml-4 px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
                    >
                      Edit
                    </button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No API keys configuration available</div>
          )}
        </div>


        {/* System Settings */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-cog text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">System Settings</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : configData?.systemSettings ? (
            <div className="space-y-4">
              {Object.entries(configData.systemSettings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      {typeof value === 'boolean' ? (value ? 'Enabled' : 'Disabled') : value}
                    </div>
                  </div>
                  {typeof value === 'boolean' ? (
                    <button
                      onClick={() => toggleSystemSetting(key)}
                      className={`ml-4 px-3 py-1 rounded text-sm ${
                        value 
                          ? 'bg-red-600 text-white hover:bg-red-700' 
                          : 'bg-green-600 text-white hover:bg-green-700'
                      }`}
                    >
                      {value ? 'Disable' : 'Enable'}
                    </button>
                  ) : (
                    <div className="ml-4 text-sm text-gray-400">Read-only</div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No system settings available</div>
          )}
        </div>

        {/* Configuration Info */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-info-circle text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Configuration Info</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Environment</span>
              <span className="text-white font-semibold">{process.env.NODE_ENV || 'development'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Last Updated</span>
              <span className="text-white font-semibold">
                {configData?.timestamp ? new Date(configData.timestamp).toLocaleString() : 'Never'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Configuration Version</span>
              <span className="text-white font-semibold">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Auto Backup</span>
              <span className={`font-semibold ${configData?.systemSettings?.autoBackup ? 'text-green-500' : 'text-red-500'}`}>
                {configData?.systemSettings?.autoBackup ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Configuration Actions */}
      <div className="bg-gray-800 rounded-lg p-6 mt-6">
        <div className="flex items-center mb-4">
          <i className="fas fa-tools text-yellow-500 text-2xl mr-4"></i>
          <h3 className="text-lg font-semibold text-white">Configuration Actions</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => alert('Export configuration functionality would be implemented here')}
            className="button secondary"
          >
            <i className="fas fa-download"></i> Export Config
          </button>
          <button
            onClick={() => alert('Import configuration functionality would be implemented here')}
            className="button secondary"
          >
            <i className="fas fa-upload"></i> Import Config
          </button>
          <button
            onClick={() => {
              if (confirm('Are you sure you want to reset all configuration to defaults?')) {
                alert('Reset configuration functionality would be implemented here')
              }
            }}
            className="button danger"
          >
            <i className="fas fa-undo"></i> Reset to Defaults
          </button>
        </div>
      </div>
    </div>
  );
}
