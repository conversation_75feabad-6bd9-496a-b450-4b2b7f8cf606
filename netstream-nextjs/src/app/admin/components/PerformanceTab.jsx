'use client'

import { useState, useEffect } from 'react'

export default function PerformanceTab() {
  const [performanceData, setPerformanceData] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchPerformanceData()
  }, [])

  const fetchPerformanceData = async () => {
    try {
      setIsLoading(true)
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
      const response = await fetch(`${apiBase}/performance`)
      if (!response.ok) throw new Error(`Performance API returned ${response.status}`)
      const data = await response.json()
      setPerformanceData(data)
    } catch (err) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearCache = async () => {
    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
      const response = await fetch(`${apiBase}/cache/clear`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      if (!response.ok) throw new Error(`Cache API returned ${response.status}`)
      await fetchPerformanceData()
    } catch (err) {
      setError(err.message)
    }
  }

  const handleOptimizeDB = async () => {
    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
      const response = await fetch(`${apiBase}/database/optimize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      if (!response.ok) throw new Error(`Database API returned ${response.status}`)
      await fetchPerformanceData()
    } catch (err) {
      setError(err.message)
    }
  }

  return (
    <div>
      <div className="flex gap-4 mb-6">
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center gap-2" onClick={fetchPerformanceData}>
          <i className="fas fa-sync"></i> Refresh Data
        </button>
        <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded flex items-center gap-2" onClick={handleClearCache}>
          <i className="fas fa-trash"></i> Clear Cache
        </button>
        <button className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded flex items-center gap-2" onClick={handleOptimizeDB}>
          <i className="fas fa-database"></i> Optimize Database
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* Memory Usage */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-memory text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Memory Usage</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="error">{error}</div>
          ) : performanceData?.memory ? (
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Used</span>
                <span className="text-3xl font-bold text-blue-500">{performanceData.memory.used}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Total</span>
                <span className="text-3xl font-bold text-blue-500">{performanceData.memory.total}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Peak</span>
                <span className="text-3xl font-bold text-blue-500">{performanceData.memory.peak}</span>
              </div>
            </div>
          ) : (
            <div className="no-data">No memory data available</div>
          )}
        </div>

        {/* Cache Statistics */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-hdd text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Cache Statistics</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="error">{error}</div>
          ) : performanceData?.cache ? (
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Hit Rate</span>
                <span className="text-3xl font-bold text-green-500">{performanceData.cache.hitRate}%</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Size</span>
                <span className="text-3xl font-bold text-green-500">{performanceData.cache.size}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Items</span>
                <span className="text-3xl font-bold text-green-500">{performanceData.cache.items}</span>
              </div>
            </div>
          ) : (
            <div className="no-data">No cache data available</div>
          )}
        </div>

        {/* API Rate Limiters */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-network-wired text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">API Rate Limiters</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="error">{error}</div>
          ) : performanceData?.rateLimiters ? (
            <div>
              {Object.entries(performanceData.rateLimiters).map(([api, data]) => (
                <div key={api} className="flex justify-between mb-2">
                  <span className="text-gray-400">{api}</span>
                  <span className="text-3xl font-bold text-purple-500">{data.remaining}/{data.limit}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-data">No rate limiter data available</div>
          )}
        </div>

        {/* Database Performance */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-database text-red-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Database Performance</h3>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
          ) : error ? (
            <div className="error">{error}</div>
          ) : performanceData?.database ? (
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Response Time</span>
                <span className="text-3xl font-bold text-red-500">{performanceData.database.responseTime}ms</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Connections</span>
                <span className="text-3xl font-bold text-red-500">{performanceData.database.connections}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Operations/s</span>
                <span className="text-3xl font-bold text-red-500">{performanceData.database.operationsPerSecond}</span>
              </div>
            </div>
          ) : (
            <div className="no-data">No database performance data available</div>
          )}
        </div>
      </div>

      {/* System Overview */}
      {performanceData?.system && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <i className="fas fa-microchip text-blue-500 text-2xl mr-4"></i>
              <h3 className="text-lg font-semibold text-white">CPU Usage</h3>
            </div>
            <div className="text-3xl font-bold text-blue-500">{performanceData.system.cpu}</div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <i className="fas fa-memory text-green-500 text-2xl mr-4"></i>
              <h3 className="text-lg font-semibold text-white">System Memory</h3>
            </div>
            <div className="text-3xl font-bold text-green-500">{performanceData.system.memory}</div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <i className="fas fa-clock text-purple-500 text-2xl mr-4"></i>
              <h3 className="text-lg font-semibold text-white">Uptime</h3>
            </div>
            <div className="text-3xl font-bold text-purple-500">{performanceData.system.uptime}h</div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <i className="fas fa-chart-line text-red-500 text-2xl mr-4"></i>
              <h3 className="text-lg font-semibold text-white">System Load</h3>
            </div>
            <div className="text-3xl font-bold text-red-500">{performanceData.system.load}</div>
          </div>
        </div>
      )}

      {/* Database Collections */}
      {performanceData?.collections && (
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-database text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Database Collections</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{performanceData.collections.movies}</div>
              <div className="text-gray-400 text-sm">Movies</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{performanceData.collections.series}</div>
              <div className="text-gray-400 text-sm">Series</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-500">{performanceData.collections.anime}</div>
              <div className="text-gray-400 text-sm">Anime</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{performanceData.collections.livetv}</div>
              <div className="text-gray-400 text-sm">Live TV</div>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-700">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Total Items:</span>
              <span className="text-white font-semibold">{performanceData.collections.totalItems}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Total Size:</span>
              <span className="text-white font-semibold">{performanceData.collections.totalSize}</span>
            </div>
          </div>
        </div>
      )}

      {/* Performance Trends */}
      {performanceData?.trends && (
        <div className="bg-gray-800 rounded-lg p-6 mt-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-chart-line text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Performance Trends</h3>
          </div>
          <canvas id="performance-chart" width="800" height="400"></canvas>
        </div>
      )}
    </div>
  )
}