'use client'

import { useState, useEffect } from 'react'
// Removed CSS module import - using Tailwind classes directly

export default function ScrapingTab() {
  const [scrapingStatus, setScrapingStatus] = useState('idle')
  const [scrapingProgress, setScrapingProgress] = useState(0)
  const [scrapingStats, setScrapingStats] = useState(null)
  const [latestJob, setLatestJob] = useState(null)
  const [logs, setLogs] = useState([])
  const [autoScroll, setAutoScroll] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  // Form state
  const [scrapingMode, setScrapingMode] = useState('latest')
  const [scrapingType, setScrapingType] = useState('all')
  
  // Separate page configurations for each media type
  const [pageConfig, setPageConfig] = useState({
    movies: 2,
    series: 2,
    anime: 2,
    livetv: 4
  })
  
  const [enableEnrichment, setEnableEnrichment] = useState(true)
  const [enableGemini, setEnableGemini] = useState(true)

  useEffect(() => {
    loadScrapingData()
    loadScrapingConfig()
    loadLatestJob()
    
    // Set up polling to check scraping status every 5 seconds
    const statusInterval = setInterval(() => {
      if (scrapingStatus === 'running') {
        checkScrapingStatus()
      }
    }, 5000)

    return () => {
      clearInterval(statusInterval)
      if (window.scrapingProgressInterval) {
        clearInterval(window.scrapingProgressInterval)
      }
    }
  }, [scrapingStatus])

  const loadScrapingData = async () => {
    try {
      setIsLoading(true)
      // Mock scraping data since API doesn't exist
      setScrapingStats({
        totalItems: 1250,
        lastUpdate: new Date().toISOString(),
        status: 'idle'
      })
      addLog('info', 'Scraping data loaded from mock')
    } catch (err) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const checkScrapingStatus = async () => {
    try {
      // Mock status check since API doesn't exist
      if (scrapingStatus === 'running') {
        // Simulate job completion after some time
        const elapsed = Date.now() - (window.scrapingStartTime || Date.now())
        if (elapsed > 30000) { // 30 seconds
          setScrapingStatus('completed')
          setScrapingProgress(100)
          addLog('success', 'Scraping job completed successfully')

          // Clear progress simulation
          if (window.scrapingProgressInterval) {
            clearInterval(window.scrapingProgressInterval)
            window.scrapingProgressInterval = null
          }
        }
      }
    } catch (err) {
      console.warn('Failed to check scraping status:', err.message)
    }
  }

  const loadScrapingConfig = async () => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';

      const response = await fetch(`${apiBase}/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-token': adminToken
        },
        body: JSON.stringify({
          query: `
            query GetConfig {
              config {
                tmdbApiKey
                wiflixBase
                frenchAnimeBase
                witvBase
                geminiApiKey
                scrapingConfig {
                  pages {
                    movies
                    series
                    anime
                    livetv
                  }
                  enrichment
                  gemini
                }
              }
            }
          `
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.data?.config) {
          // Load actual scraping config from database
          if (result.data.config.scrapingConfig) {
            setPageConfig(result.data.config.scrapingConfig.pages || {
              movies: 2,
              series: 2,
              anime: 2,
              livetv: 4
            });
            setEnableEnrichment(result.data.config.scrapingConfig.enrichment ?? true);
            setEnableGemini(result.data.config.scrapingConfig.gemini ?? true);
          } else {
            // Set default page config if no scraping config found
            setPageConfig({
              movies: 2,
              series: 2,
              anime: 2,
              livetv: 4
            });
            setEnableEnrichment(true);
            setEnableGemini(!!result.data.config.geminiApiKey);
          }
        }
      }
    } catch (err) {
      console.warn('Failed to load scraping config:', err.message);
      // Use defaults if config loading fails
      setPageConfig({
        movies: 2,
        series: 2,
        anime: 2,
        livetv: 4
      });
      setEnableEnrichment(true);
      setEnableGemini(false);
    }
  };

  const loadLatestJob = async () => {
    try {
      // Skip latest job loading since API doesn't exist yet
      setLatestJob(null)
    } catch (err) {
      console.warn('Failed to load latest job:', err.message)
    }
  }

  const startScraping = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Update UI to show scraping started
      setScrapingStatus('running')
      setScrapingProgress(0)
      addLog('info', 'Starting scraping operation...')

      // Use the working /api/scrape endpoint (same as fastify-migration branch)
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'

      const requestBody = {
        mode: scrapingMode || 'latest',
        type: scrapingType || 'movies',
        pages: pageConfig || { movies: 2, series: 2, anime: 2, livetv: 4 },
        enrichment: enableEnrichment === true,
        gemini: enableGemini === true
      };

      addLog('info', `🚀 Starting scraping with: mode=${scrapingMode}, type=${scrapingType}`);
      addLog('info', `📊 Request body: ${JSON.stringify(requestBody, null, 2)}`);

      // Also log to browser console for debugging
      console.log('🚀 Starting scraping with:', { scrapingMode, scrapingType });
      console.log('📊 Request body:', requestBody);

      const response = await fetch(`${apiBase}/api/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        addLog('error', `❌ API Error ${response.status}: ${errorText}`);
        console.error('❌ API Error:', response.status, errorText);
        throw new Error(`Scraping API returned ${response.status}: ${response.statusText} - ${errorText}`)
      }

      const result = await response.json()

      if (result.success) {
        addLog('success', `Scraping started: ${result.message}`)
        setScrapingStatus('running')
        window.scrapingStartTime = Date.now()

        // Simulate progress updates
        simulateProgress()

        // Start status checking
        const statusInterval = setInterval(checkScrapingStatus, 5000)
        window.scrapingStatusInterval = statusInterval
      } else {
        throw new Error(result.error || 'Failed to start scraping')
      }

    } catch (err) {
      console.error('❌ Scraping failed:', err);
      setError(err.message)
      addLog('error', `Failed to start scraping: ${err.message}`)
      setScrapingStatus('error')
    } finally {
      setIsLoading(false)
    }
  }

  const stopScraping = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      addLog('info', 'Stopping scraping operation...')

      const response = await fetch('/api/scrape/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Stop scraping API returned ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.success) {
        addLog('success', result.message || 'Scraping stopped successfully')
        setScrapingStatus('stopped')
        setScrapingProgress(0)
        
        // Clear any progress simulation
        if (window.scrapingProgressInterval) {
          clearInterval(window.scrapingProgressInterval)
          window.scrapingProgressInterval = null
        }
      } else {
        throw new Error(result.message || 'Failed to stop scraping')
      }

    } catch (err) {
      setError(err.message)
      addLog('error', `Failed to stop scraping: ${err.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const simulateProgress = () => {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 10
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        setScrapingStatus('completed')
        setScrapingProgress(100)
        addLog('success', 'Scraping completed successfully')
      } else {
        setScrapingProgress(Math.round(progress))
        addLog('info', `Scraping progress: ${Math.round(progress)}%`)
      }
    }, 2000)
    
    // Store interval reference for stopping
    window.scrapingProgressInterval = interval
  }

  const addLog = (level, message) => {
    const timestamp = new Date().toLocaleTimeString()
    const newLog = {
      id: Date.now(),
      timestamp,
      level,
      message
    }
    setLogs(prev => [...prev, newLog])
  }

  const clearLogs = () => {
    setLogs([])
    addLog('info', 'Logs cleared')
  }

  const toggleAutoScroll = () => {
    setAutoScroll(!autoScroll)
  }

  const saveScrapingConfig = async () => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';

      const scrapingConfigValue = JSON.stringify({
        pages: pageConfig,
        enrichment: enableEnrichment,
        gemini: enableGemini
      });

      const response = await fetch(`${apiBase}/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-token': adminToken
        },
        body: JSON.stringify({
          query: `
            mutation UpdateBaseUrl($key: String!, $value: String!, $adminToken: String!) {
              updateBaseUrl(key: $key, value: $value, adminToken: $adminToken) {
                success
                message
              }
            }
          `,
          variables: {
            key: 'SCRAPING_CONFIG',
            value: scrapingConfigValue,
            adminToken
          }
        })
      });

      if (!response.ok) throw new Error(`API returned ${response.status}`);
      const result = await response.json();

      if (result.data?.updateBaseUrl?.success) {
        addLog('success', 'Scraping configuration saved to database successfully');
        // Also save to localStorage as backup
        localStorage.setItem('scrapingConfig', scrapingConfigValue);
        // Refetch config data to ensure consistency
        await loadScrapingConfig();
      } else {
        throw new Error(result.data?.updateBaseUrl?.message || 'Failed to save configuration');
      }
    } catch (err) {
      addLog('error', `Failed to save configuration: ${err.message}`);
    }
  }

  const updatePageConfig = (mediaType, value) => {
    setPageConfig(prev => ({
      ...(prev || {}),
      [mediaType]: parseInt(value) || 0
    }))
  }

  // Auto-save advanced settings changes
  const saveAdvancedSettingsChange = async (settingType, value) => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';

      // Update the appropriate setting
      const newEnrichment = settingType === 'enrichment' ? value : enableEnrichment;
      const newGemini = settingType === 'gemini' ? value : enableGemini;

      const scrapingConfigValue = JSON.stringify({
        pages: pageConfig,
        enrichment: newEnrichment,
        gemini: newGemini
      });

      const response = await fetch(`${apiBase}/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-token': adminToken
        },
        body: JSON.stringify({
          query: `
            mutation UpdateBaseUrl($key: String!, $value: String!, $adminToken: String!) {
              updateBaseUrl(key: $key, value: $value, adminToken: $adminToken) {
                success
                message
              }
            }
          `,
          variables: {
            key: 'SCRAPING_CONFIG',
            value: scrapingConfigValue,
            adminToken
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.data?.updateBaseUrl?.success) {
          addLog('success', `${settingType} setting updated to ${value ? 'enabled' : 'disabled'}`);
        }
      }
    } catch (err) {
      addLog('error', `Failed to save ${settingType} setting: ${err.message}`);
    }
  }

  // Auto-save page config changes after a delay
  const savePageConfigChange = async (mediaType, value) => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';

      // Update local state first
      const newPageConfig = { ...pageConfig, [mediaType]: parseInt(value) || 0 };

      const scrapingConfigValue = JSON.stringify({
        pages: newPageConfig,
        enrichment: enableEnrichment,
        gemini: enableGemini
      });

      const response = await fetch(`${apiBase}/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-token': adminToken
        },
        body: JSON.stringify({
          query: `
            mutation UpdateBaseUrl($key: String!, $value: String!, $adminToken: String!) {
              updateBaseUrl(key: $key, value: $value, adminToken: $adminToken) {
                success
                message
              }
            }
          `,
          variables: {
            key: 'SCRAPING_CONFIG',
            value: scrapingConfigValue,
            adminToken
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.data?.updateBaseUrl?.success) {
          addLog('success', `${mediaType} page count updated to ${value}`);
        }
      }
    } catch (err) {
      addLog('error', `Failed to save ${mediaType} page config: ${err.message}`);
    }
  }

  return (
    <div>
      {/* Scraping Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-spider text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Scraping Controls</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Mode</label>
              <select 
                value={scrapingMode}
                onChange={(e) => setScrapingMode(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="latest">Latest Content</option>
                <option value="full">Full Scrape</option>
                <option value="update">Update Existing</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Type</label>
              <select 
                value={scrapingType}
                onChange={(e) => setScrapingType(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="movies">Movies Only</option>
                <option value="series">Series Only</option>
                <option value="anime">Anime Only</option>
                <option value="livetv">Live TV Only</option>
              </select>
            </div>
            <button 
              className="button primary w-full" 
              onClick={startScraping}
              disabled={isLoading || scrapingStatus === 'running'}
            >
              <i className={`fas ${isLoading ? 'fa-spinner fa-spin' : 'fa-spider'}`}></i>
              {isLoading ? 'Starting...' : 'Start Scraping'}
            </button>
            {scrapingStatus === 'running' && (
              <button 
                className="button danger w-full mt-2" 
                onClick={stopScraping}
                disabled={isLoading}
              >
                <i className={`fas ${isLoading ? 'fa-spinner fa-spin' : 'fa-stop'}`}></i>
                {isLoading ? 'Stopping...' : 'Stop Scraping'}
              </button>
            )}
          </div>
        </div>

        {/* Scraping Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-cog text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Page Configuration</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Movies Pages</label>
              <input
                type="number"
                value={pageConfig?.movies || 0}
                onChange={(e) => {
                  updatePageConfig('movies', e.target.value);
                  // Auto-save after 1 second delay
                  clearTimeout(window.moviesSaveTimeout);
                  window.moviesSaveTimeout = setTimeout(() => {
                    savePageConfigChange('movies', e.target.value);
                  }, 1000);
                }}
                min="0"
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Series Pages</label>
              <input
                type="number"
                value={pageConfig?.series || 0}
                onChange={(e) => {
                  updatePageConfig('series', e.target.value);
                  // Auto-save after 1 second delay
                  clearTimeout(window.seriesSaveTimeout);
                  window.seriesSaveTimeout = setTimeout(() => {
                    savePageConfigChange('series', e.target.value);
                  }, 1000);
                }}
                min="0"
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Anime Pages</label>
              <input
                type="number"
                value={pageConfig?.anime || 0}
                onChange={(e) => {
                  updatePageConfig('anime', e.target.value);
                  // Auto-save after 1 second delay
                  clearTimeout(window.animeSaveTimeout);
                  window.animeSaveTimeout = setTimeout(() => {
                    savePageConfigChange('anime', e.target.value);
                  }, 1000);
                }}
                min="0"
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-2">Live TV Pages</label>
              <input
                type="number"
                value={pageConfig?.livetv || 0}
                onChange={(e) => {
                  updatePageConfig('livetv', e.target.value);
                  // Auto-save after 1 second delay
                  clearTimeout(window.livetvSaveTimeout);
                  window.livetvSaveTimeout = setTimeout(() => {
                    savePageConfigChange('livetv', e.target.value);
                  }, 1000);
                }}
                min="0"
                max="50"
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Advanced Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-sliders-h text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Advanced Settings</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enable-enrichment"
                checked={enableEnrichment}
                onChange={(e) => {
                  setEnableEnrichment(e.target.checked);
                  saveAdvancedSettingsChange('enrichment', e.target.checked);
                }}
                className="mr-2"
              />
              <label htmlFor="enable-enrichment" className="text-sm text-gray-300">
                Enable metadata enrichment
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enable-gemini"
                checked={enableGemini}
                onChange={(e) => {
                  setEnableGemini(e.target.checked);
                  saveAdvancedSettingsChange('gemini', e.target.checked);
                }}
                className="mr-2"
              />
              <label htmlFor="enable-gemini" className="text-sm text-gray-300">
                Use Gemini AI
              </label>
            </div>
            <button 
              className="button secondary w-full" 
              onClick={saveScrapingConfig}
            >
              <i className="fas fa-save"></i> Save Config
            </button>
          </div>
        </div>

        {/* Current Status */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-info-circle text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Current Status</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-400">Status:</span>
              <span className={`text-sm font-medium ${
                scrapingStatus === 'error' ? 'text-red-500' : 
                scrapingStatus === 'running' ? 'text-yellow-500' : 
                scrapingStatus === 'stopped' ? 'text-orange-500' :
                scrapingStatus === 'completed' ? 'text-green-500' :
                'text-gray-500'
              }`}>
                {scrapingStatus.charAt(0).toUpperCase() + scrapingStatus.slice(1)}
              </span>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Progress:</span>
                <span className="text-sm text-gray-300">{scrapingProgress}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${scrapingProgress}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-gray-800 rounded-lg p-6 mb-6">
        <div className="flex items-center mb-4">
          <i className="fas fa-chart-bar text-red-500 text-2xl mr-4"></i>
          <h3 className="text-lg font-semibold text-white">Statistics</h3>
        </div>
        {isLoading ? (
          <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading...</div>
        ) : error ? (
          <div className="text-red-500 text-sm">{error}</div>
        ) : scrapingStats ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{scrapingStats.statistics?.totalJobs || 0}</div>
              <div className="text-gray-400 text-sm">Total Jobs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{scrapingStats.statistics?.successfulJobs || 0}</div>
              <div className="text-gray-400 text-sm">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{scrapingStats.statistics?.failedJobs || 0}</div>
              <div className="text-gray-400 text-sm">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{scrapingStats.successRate || '0%'}</div>
              <div className="text-gray-400 text-sm">Success Rate</div>
            </div>
          </div>
        ) : (
          <div className="text-gray-500 text-sm">No data available</div>
        )}
      </div>

      {/* Latest Job Details */}
      {latestJob && (
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-info-circle text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Latest Job Details</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <span className="text-gray-400 text-sm">Job ID:</span>
              <div className="text-white font-mono text-sm">{latestJob.jobId}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Status:</span>
              <div className={`text-sm font-medium ${
                latestJob.status === 'running' ? 'text-yellow-500' :
                latestJob.status === 'completed' ? 'text-green-500' :
                latestJob.status === 'failed' ? 'text-red-500' :
                latestJob.status === 'stopped' ? 'text-orange-500' :
                'text-gray-500'
              }`}>
                {latestJob.status.charAt(0).toUpperCase() + latestJob.status.slice(1)}
              </div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Mode:</span>
              <div className="text-white text-sm capitalize">{latestJob.mode}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Type:</span>
              <div className="text-white text-sm capitalize">{latestJob.type}</div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Started:</span>
              <div className="text-white text-sm">
                {latestJob.startedAt ? new Date(latestJob.startedAt).toLocaleString() : 'N/A'}
              </div>
            </div>
            <div>
              <span className="text-gray-400 text-sm">Duration:</span>
              <div className="text-white text-sm">{latestJob.duration || 'N/A'}</div>
            </div>
            {latestJob.pages && (
              <div className="md:col-span-2 lg:col-span-3">
                <span className="text-gray-400 text-sm">Page Configuration:</span>
                <div className="grid grid-cols-4 gap-2 mt-1">
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.movies || 0}</div>
                    <div className="text-gray-400 text-xs">Movies</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.series || 0}</div>
                    <div className="text-gray-400 text-xs">Series</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.anime || 0}</div>
                    <div className="text-gray-400 text-xs">Anime</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold">{latestJob.pages.livetv || 0}</div>
                    <div className="text-gray-400 text-xs">Live TV</div>
                  </div>
                </div>
              </div>
            )}
            {latestJob.error && (
              <div className="md:col-span-2 lg:col-span-3">
                <span className="text-gray-400 text-sm">Error:</span>
                <div className="text-red-400 text-sm mt-1 bg-red-900 p-2 rounded">
                  {latestJob.error}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Live Logs */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <i className="fas fa-terminal text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Live Logs</h3>
          </div>
          <div className="flex space-x-2">
            <button 
              className="button secondary" 
              onClick={clearLogs}
            >
              <i className="fas fa-trash"></i> Clear Logs
            </button>
            <button 
              className={`button ${autoScroll ? 'primary' : 'secondary'}`}
              onClick={toggleAutoScroll}
            >
              <i className="fas fa-arrow-down"></i> Auto Scroll {autoScroll ? 'ON' : 'OFF'}
            </button>
          </div>
        </div>
        <div className="bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              <i className="fas fa-terminal text-2xl mb-2"></i>
              <p>No logs available</p>
            </div>
          ) : (
            <div className="space-y-1">
              {logs.map((log) => (
                <div 
                  key={log.id}
                  className={`text-sm p-2 rounded ${
                    log.level === 'error' ? 'bg-red-900 text-red-200' :
                    log.level === 'warning' ? 'bg-yellow-900 text-yellow-200' :
                    log.level === 'success' ? 'bg-green-900 text-green-200' :
                    'bg-gray-800 text-gray-300'
                  }`}
                >
                  <span className="text-gray-400">[{log.timestamp}]</span> {log.level.toUpperCase()}: {log.message}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}