'use client';

import { useState, useEffect } from 'react';
// Removed CSS module import - using Tailwind classes directly

export default function LogsTab() {
  const [logsData, setLogsData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    level: '',
    source: '',
    page: 1,
    limit: 50
  });

  useEffect(() => {
    fetchLogs();
  }, [filters]);

  const fetchLogs = async () => {
    try {
      setIsLoading(true);
      // Mock logs data since API doesn't exist
      const mockLogs = [
        { id: 1, timestamp: new Date().toISOString(), level: 'info', source: 'server', message: 'Server started successfully', details: 'NetStream server initialized on port 3001' },
        { id: 2, timestamp: new Date(Date.now() - 300000).toISOString(), level: 'warn', source: 'cache', message: 'Redis connection failed', details: 'Continuing without cache' },
        { id: 3, timestamp: new Date(Date.now() - 600000).toISOString(), level: 'info', source: 'database', message: 'MongoDB connected', details: 'Database connection established' },
        { id: 4, timestamp: new Date(Date.now() - 900000).toISOString(), level: 'error', source: 'scraper', message: 'Scraping failed', details: 'Network timeout while scraping content' },
        { id: 5, timestamp: new Date(Date.now() - 1200000).toISOString(), level: 'info', source: 'auth', message: 'User login', details: 'Admin user authenticated successfully' }
      ];

      setLogsData({
        logs: mockLogs,
        pagination: { page: filters.page, limit: filters.limit, total: 5, totalPages: 1 }
      });
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearLogs = async () => {
    if (!confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      return;
    }

    try {
      // Mock clear logs since API doesn't exist
      setLogsData({
        logs: [],
        pagination: { page: 1, limit: filters.limit, total: 0, totalPages: 0 }
      });
      alert('Logs cleared successfully!');
    } catch (err) {
      setError(err.message);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const getLevelColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'error':
        return 'text-red-500';
      case 'warning':
        return 'text-yellow-500';
      case 'info':
        return 'text-blue-500';
      case 'debug':
        return 'text-gray-500';
      case 'success':
        return 'text-green-500';
      default:
        return 'text-gray-400';
    }
  };

  const getLevelIcon = (level) => {
    switch (level?.toLowerCase()) {
      case 'error':
        return 'fas fa-exclamation-circle';
      case 'warning':
        return 'fas fa-exclamation-triangle';
      case 'info':
        return 'fas fa-info-circle';
      case 'debug':
        return 'fas fa-bug';
      case 'success':
        return 'fas fa-check-circle';
      default:
        return 'fas fa-circle';
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div>
      <div className="flex gap-4 mb-6">
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center gap-2" onClick={fetchLogs}>
          <i className="fas fa-sync"></i> Refresh Logs
        </button>
        <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded flex items-center gap-2" onClick={handleClearLogs}>
          <i className="fas fa-trash"></i> Clear All Logs
        </button>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-lg p-6 mb-6">
        <div className="flex items-center mb-4">
          <i className="fas fa-filter text-blue-500 text-2xl mr-4"></i>
          <h3 className="text-lg font-semibold text-white">Log Filters</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Log Level</label>
            <select
              value={filters.level}
              onChange={(e) => handleFilterChange('level', e.target.value)}
              className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600"
            >
              <option value="">All Levels</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
              <option value="success">Success</option>
            </select>
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-2">Source</label>
            <select
              value={filters.source}
              onChange={(e) => handleFilterChange('source', e.target.value)}
              className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600"
            >
              <option value="">All Sources</option>
              <option value="system">System</option>
              <option value="database">Database</option>
              <option value="scraping">Scraping</option>
              <option value="api">API</option>
              <option value="cache">Cache</option>
              <option value="enrichment">Enrichment</option>
            </select>
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-2">Items per Page</label>
            <select
              value={filters.limit}
              onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
              className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600"
            >
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={200}>200</option>
            </select>
          </div>
        </div>
      </div>

      {/* Logs Display */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <i className="fas fa-list text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">System Logs</h3>
          </div>
          {logsData && (
            <div className="text-sm text-gray-400">
              Showing {logsData.logs.length} of {logsData.pagination.total} logs
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center p-8 text-gray-400"><i className="fas fa-spinner animate-spin text-2xl mr-2"></i> Loading logs...</div>
        ) : error ? (
          <div className="text-red-500 text-sm">{error}</div>
        ) : logsData?.logs ? (
          <div className="space-y-3">
            {logsData.logs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <i className="fas fa-inbox text-4xl mb-4"></i>
                <p>No logs found matching the current filters</p>
              </div>
            ) : (
              logsData.logs.map((log) => (
                <div key={log.id} className="bg-gray-700 rounded-lg p-4 border-l-4 border-gray-600">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <i className={`${getLevelIcon(log.level)} ${getLevelColor(log.level)} text-lg mt-1`}></i>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`text-sm font-semibold ${getLevelColor(log.level)}`}>
                            {log.level.toUpperCase()}
                          </span>
                          <span className="text-sm text-gray-400">[{log.source}]</span>
                          <span className="text-sm text-gray-500">
                            {formatTimestamp(log.timestamp)}
                          </span>
                        </div>
                        <div className="text-white text-sm">{log.message}</div>
                        {log.meta && Object.keys(log.meta).length > 0 && (
                          <div className="mt-2 text-xs text-gray-400">
                            <details>
                              <summary className="cursor-pointer hover:text-gray-300">
                                Additional Info
                              </summary>
                              <pre className="mt-1 text-xs bg-gray-800 p-2 rounded overflow-x-auto">
                                {JSON.stringify(log.meta, null, 2)}
                              </pre>
                            </details>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        ) : (
          <div className="text-gray-500 text-sm">No logs data available</div>
        )}

        {/* Pagination */}
        {logsData?.pagination && logsData.pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-700">
            <div className="text-sm text-gray-400">
              Page {logsData.pagination.page} of {logsData.pagination.totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(logsData.pagination.page - 1)}
                disabled={logsData.pagination.page <= 1}
                className="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(logsData.pagination.page + 1)}
                disabled={logsData.pagination.page >= logsData.pagination.totalPages}
                className="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Log Statistics */}
      {logsData && (
        <div className="bg-gray-800 rounded-lg p-6 mt-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-chart-bar text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Log Statistics</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{logsData.pagination.total}</div>
              <div className="text-gray-400 text-sm">Total Logs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{logsData.pagination.totalPages}</div>
              <div className="text-gray-400 text-sm">Total Pages</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-500">{logsData.pagination.limit}</div>
              <div className="text-gray-400 text-sm">Per Page</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">
                {logsData.logs.filter(log => log.level === 'error').length}
              </div>
              <div className="text-gray-400 text-sm">Errors</div>
            </div>
          </div>
        </div>
      )}

      {/* Last Updated */}
      <div className="bg-gray-800 rounded-lg p-4 mt-6">
        <div className="flex items-center justify-between">
          <div className="text-gray-400">
            <i className="fas fa-clock mr-2"></i>
            Log Management
          </div>
          <div className="text-gray-400 text-sm">
            Last updated: {logsData?.timestamp ? new Date(logsData.timestamp).toLocaleString() : 'Never'}
          </div>
        </div>
      </div>
    </div>
  );
}
