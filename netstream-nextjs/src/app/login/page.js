"use client"
import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'

    // Check if this is admin login (<EMAIL>)
    if (email === '<EMAIL>') {
      try {
        // Use GraphQL admin login
        const res = await fetch(`${apiBase}/graphql`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            query: `mutation AdminLogin($adminKey: String!) {
              adminLogin(adminKey: $adminKey) {
                success token message
              }
            }`,
            variables: { adminKey: password }
          })
        })

        const data = await res.json()

        if (data.data?.adminLogin?.success) {
          // Store admin token
          localStorage.setItem('adminToken', data.data.adminLogin.token)
          localStorage.setItem('userRole', 'admin')
          localStorage.setItem('isAdmin', 'true')
          router.push('/admin')
        } else {
          setError(data.data?.adminLogin?.message || 'Admin login failed')
        }
      } catch (err) {
        setError('Admin login failed: ' + err.message)
      }
    } else {
      // Use backend REST API for regular user authentication
      const res = await fetch(`${apiBase}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      })

      if (res.ok) {
        const data = await res.json()
        if (data.success) {
          // Store user session token in localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('userToken', data.token)
            localStorage.setItem('userData', JSON.stringify(data.user))
          }

          // Redirect based on user role
          if (data.user.role === 'admin') {
            // Also store admin token for admin operations
            localStorage.setItem('adminToken', data.token)
            router.push('/admin')
          } else {
            router.push('/profile')
          }
        } else {
          setError(data.error || 'Login failed')
        }
      } else {
        const errorData = await res.json()
        setError(errorData.error || 'Login failed')
      }
    }
    setLoading(false)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <form onSubmit={handleSubmit} className="bg-gray-800 p-8 rounded-lg w-full max-w-md">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">Login</h2>
        {error && <div className="text-red-500 mb-4">{error}</div>}
        <div className="mb-4">
          <label className="block text-gray-300 mb-2">Email</label>
          <input
            type="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            className="w-full px-3 py-2 rounded bg-gray-700 text-white"
            placeholder="Enter your email"
            required
          />
        </div>
        <div className="mb-6">
          <label className="block text-gray-300 mb-2">Password</label>
          <input
            type="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            className="w-full px-3 py-2 rounded bg-gray-700 text-white"
            placeholder="Enter your password"
            required
          />
        </div>
        <button type="submit" disabled={loading} className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
          {loading ? 'Logging in...' : 'Login'}
        </button>
        <div className="mt-4 text-center">
          <a href="/register" className="text-blue-400 hover:underline">Create an account</a>
        </div>
      </form>
    </div>
  )
} 