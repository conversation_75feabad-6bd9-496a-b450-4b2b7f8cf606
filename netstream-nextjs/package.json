{"name": "netstream-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "graphql": "^16.11.0", "hls.js": "^1.6.7", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "next": "14.2.15", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-use": "^17.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^24.0.0", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "14.2.15", "postcss": "^8.4.49", "tailwindcss": "^3.4.0"}}