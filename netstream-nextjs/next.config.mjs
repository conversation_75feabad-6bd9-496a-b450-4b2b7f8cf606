/** @type {import('next').NextConfig} */
const nextConfig = {
  // This enables the standalone output mode, which creates a
  // minimal server for production deployment in Docker.
  output: 'standalone',
  // Optimize images
  images: {
    unoptimized: true, // For static export compatibility
  },
  // Environment variables
  env: {
    SKIP_ENV_VALIDATION: process.env.SKIP_ENV_VALIDATION || 'false',
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://netstream2.onrender.com',
  },
  // Handle build-time errors gracefully
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
};

export default nextConfig;
