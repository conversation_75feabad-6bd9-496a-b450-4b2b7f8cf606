#!/bin/bash

# NetStream Deployment Script
# Automatically detects environment and uses appropriate configuration
# Usage: ./deploy.sh [action] [--env=proxmox|development]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 NetStream Deployment Script${NC}"
echo "=================================="

# Function to detect if we're in Proxmox
detect_environment() {
    # Check for local environment file first
    if [ -f ".env.deployment" ]; then
        local env_setting=$(grep "^DEPLOYMENT_ENV=" .env.deployment | cut -d'=' -f2)
        if [ -n "$env_setting" ]; then
            echo "$env_setting"
            return
        fi
    fi

    # Check if we're in a Proxmox container/VM
    if [ -f /proc/version ] && grep -q "pve" /proc/version; then
        echo "proxmox"
    # Check if ************* is available on any interface
    elif ip addr show | grep -q "*************"; then
        echo "proxmox"
    # Check if we're on the 192.168.1.x network
    elif ip route | grep -q "***********/24"; then
        echo "proxmox"
    # Check if we can ping the Proxmox IP
    elif ping -c 1 ************* >/dev/null 2>&1; then
        echo "proxmox"
    # Check hostname patterns common in Proxmox
    elif hostname | grep -qE "(pve|proxmox|ubuntu.*192\.168\.1)"; then
        echo "proxmox"
    else
        echo "development"
    fi
}

# Function to get the correct API base URL
get_api_base_url() {
    local env=$1
    if [ "$env" = "proxmox" ]; then
        echo "http://*************:3001"
    else
        echo "http://localhost:3001"
    fi
}

# Parse command line arguments
ACTION=""
FORCE_ENV=""

for arg in "$@"; do
    case $arg in
        --env=*)
            FORCE_ENV="${arg#*=}"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [action] [--env=proxmox|development]"
            echo ""
            echo "Actions:"
            echo "  build    - Build containers"
            echo "  up       - Start services (default)"
            echo "  down     - Stop services"
            echo "  restart  - Restart services"
            echo "  rebuild  - Rebuild and restart"
            echo "  logs     - Show logs"
            echo ""
            echo "Environment Detection:"
            echo "  1. Command line: --env=proxmox or --env=development"
            echo "  2. Environment file: Create .env.deployment with DEPLOYMENT_ENV=proxmox"
            echo "  3. Auto-detection: Based on network/system characteristics"
            echo ""
            echo "Environment Types:"
            echo "  proxmox     - Uses ************* (production)"
            echo "  development - Uses localhost (development)"
            exit 0
            ;;
        *)
            if [ -z "$ACTION" ]; then
                ACTION="$arg"
            fi
            ;;
    esac
done

# Set default action
ACTION=${ACTION:-"up"}

# Detect or use forced environment
if [ -n "$FORCE_ENV" ]; then
    ENVIRONMENT="$FORCE_ENV"
    echo -e "${YELLOW}📍 Forced environment: ${ENVIRONMENT}${NC}"
else
    ENVIRONMENT=$(detect_environment)
    echo -e "${YELLOW}📍 Detected environment: ${ENVIRONMENT}${NC}"
fi

API_BASE_URL=$(get_api_base_url $ENVIRONMENT)
echo -e "${YELLOW}🌐 Using API base URL: ${API_BASE_URL}${NC}"

# Choose the appropriate docker-compose command
if [ "$ENVIRONMENT" = "proxmox" ]; then
    echo -e "${GREEN}🏗️  Building for Proxmox deployment...${NC}"
    COMPOSE_CMD="docker-compose -f docker-compose.yml -f docker-compose.proxmox.yml"
else
    echo -e "${GREEN}🏗️  Building for development...${NC}"
    COMPOSE_CMD="docker-compose"
fi

case $ACTION in
    "build")
        echo -e "${BLUE}🔨 Building containers...${NC}"
        $COMPOSE_CMD build --no-cache
        ;;
    "up")
        echo -e "${BLUE}🚀 Starting services...${NC}"
        $COMPOSE_CMD up -d
        ;;
    "down")
        echo -e "${BLUE}🛑 Stopping services...${NC}"
        $COMPOSE_CMD down
        ;;
    "restart")
        echo -e "${BLUE}🔄 Restarting services...${NC}"
        $COMPOSE_CMD down
        $COMPOSE_CMD up -d
        ;;
    "rebuild")
        echo -e "${BLUE}🔨 Rebuilding and restarting...${NC}"
        $COMPOSE_CMD down
        $COMPOSE_CMD build --no-cache
        $COMPOSE_CMD up -d
        ;;
    "logs")
        echo -e "${BLUE}📋 Showing logs...${NC}"
        $COMPOSE_CMD logs -f
        ;;
    *)
        echo -e "${RED}❌ Unknown action: $ACTION${NC}"
        echo "Usage: $0 [action] [--env=proxmox|development]"
        echo "Actions: build, up, down, restart, rebuild, logs"
        echo "Environment: proxmox (uses *************), development (uses localhost)"
        exit 1
        ;;
esac

# Show status
echo ""
echo -e "${GREEN}✅ Operation completed!${NC}"
echo -e "${BLUE}📊 Container status:${NC}"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Show access URLs
echo ""
echo -e "${GREEN}🌐 Access URLs:${NC}"
if [ "$ENVIRONMENT" = "proxmox" ]; then
    echo -e "   Frontend: ${BLUE}http://*************:3000${NC}"
    echo -e "   Backend:  ${BLUE}http://*************:3001${NC}"
    echo -e "   Admin:    ${BLUE}http://*************:3000/admin${NC}"
else
    echo -e "   Frontend: ${BLUE}http://localhost:3000${NC}"
    echo -e "   Backend:  ${BLUE}http://localhost:3001${NC}"
    echo -e "   Admin:    ${BLUE}http://localhost:3000/admin${NC}"
fi
